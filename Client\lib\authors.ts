export interface Author {
    id: string;
    displayName: string;
    bio?: string;
    birthDate?: string;
    // Add other fields from your API
}

export interface Work {
    id: string;
    workId: string;    
    title: string;
    authorName: string;
    content: string;
    authorId: string;
    excerpt: string;
    publishedDate: string;
}

export interface WorkPageProps {
    work: Work & {
        authorName: string;
    };
}

export async function getAuthorsList(): Promise<Author[]> {
    const response = await fetch("http://localhost:3556/authors");
    return await response.json() as Author[];
}

export async function getAuthor(id: string): Promise<Author> {
    const response = await fetch(`http://localhost:3556/authors/${id}`);
    return await response.json() as Author;
}

export async function getAuthorWorksList(authorId: string): Promise<Work[]> {
    const response = await fetch(`http://localhost:3556/authors/works?id=${authorId}`);
    return await response.json() as Work[];
}

export async function getWorks(authorId: string): Promise<Work[]> {
    const response = await fetch(`http://localhost:3556/authors/works?id=${authorId}`);
    return await response.json() as Work[];
}

export async function getWork(workId: string): Promise<Work> {
    const response = await fetch(`http://localhost:3556/authors/work?workId=${workId}`);
    return await response.json() as Work;
}

export async function getAllWorks(): Promise<Work[]> {
    const response = await fetch("http://localhost:3556/authors/allWorks");
    return await response.json() as Work[];
}

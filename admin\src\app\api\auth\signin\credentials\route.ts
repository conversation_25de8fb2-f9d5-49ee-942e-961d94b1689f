import { NextResponse } from "next/server";

export async function POST(request: Request) {
  try {
    const body = await request.json();
    console.log("Signin credentials request:", body);
    
    // Перенаправляем на обработчик NextAuth
    return NextResponse.redirect(new URL("/api/auth/callback/credentials", request.url));
  } catch (error) {
    console.error("Signin credentials error:", error);
    return NextResponse.json({ error: "Invalid request" }, { status: 400 });
  }
}

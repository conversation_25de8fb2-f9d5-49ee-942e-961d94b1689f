import { getSession } from "next-auth/react";
import { gql } from "graphql-tag";
import {
  GetAuthorsQuery,
  GetAuthorsQueryVariables,
  GetAuthorQuery,
  GetAuthorQueryVariables,
  CreateAuthorMutation,
  CreateAuthorMutationVariables,
  UpdateAuthorMutation,
  UpdateAuthorMutationVariables,
  DeleteAuthorMutation,
  DeleteAuthorMutationVariables,
  Author,
  CreateAuthorInput,
  UpdateAuthorInput
} from "@/lib/graphql/generated/graphql";

const API_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:3556";

async function fetchWithAuth(url: string, options: RequestInit = {}) {
  const session = await getSession();

  return fetch(url, {
    ...options,
    headers: {
      ...options.headers,
      "Content-Type": "application/json",
      Authorization: `Bearer ${session?.accessToken}`,
    },
  });
}

// GraphQL запросы и мутации
const GET_AUTHORS = gql`
  query GetAuthors($skip: Int, $take: Int, $where: AuthorFilterInput) {
    authors(skip: $skip, take: $take, where: $where) {
      items {
        id
        urlPart
        displayName
        birthDate
        deathDate
        addedDate
        modifiedDate
      }
      totalCount
    }
  }
`;

const GET_AUTHOR = gql`
  query GetAuthor($id: String!) {
    author(id: $id) {
      id
      urlPart
      name
      surName
      lastName
      displayName
      biography
      birthDate
      deathDate
      addedDate
      modifiedDate
    }
  }
`;

const CREATE_AUTHOR = gql`
  mutation CreateAuthor($input: CreateAuthorInput!) {
    createAuthor(input: $input) {
      author {
        id
        urlPart
        displayName
      }
    }
  }
`;

const UPDATE_AUTHOR = gql`
  mutation UpdateAuthor($id: String!, $input: UpdateAuthorInput!) {
    updateAuthor(id: $id, input: $input) {
      author {
        id
        urlPart
        displayName
      }
    }
  }
`;

const DELETE_AUTHOR = gql`
  mutation DeleteAuthor($id: String!) {
    deleteAuthor(id: $id) {
      success
    }
  }
`;

export async function getAuthors(page: number = 1, pageSize: number = 10, filter?: string): Promise<{ authors: Author[], totalCount: number }> {
  const skip = (page - 1) * pageSize;

  // Создаем объект фильтрации для GraphQL запроса
  const where = filter ? { displayName: { contains: filter } } : undefined;

  const response = await fetchWithAuth(`${API_URL}/graphql`, {
    method: "POST",
    body: JSON.stringify({
      query: GET_AUTHORS.loc?.source.body,
      variables: { skip, take: pageSize, where }
    }),
  });

  const data = await response.json();

  if (data.errors) {
    throw new Error(data.errors[0].message);
  }

  const result = data.data as GetAuthorsQuery;
  return {
    authors: result.authors.items as Author[],
    totalCount: result.authors.totalCount
  };
}

export async function getAuthor(id: string): Promise<Author> {
  const response = await fetchWithAuth(`${API_URL}/graphql`, {
    method: "POST",
    body: JSON.stringify({
      query: GET_AUTHOR.loc?.source.body,
      variables: { id },
    }),
  });

  const data = await response.json();

  if (data.errors) {
    throw new Error(data.errors[0].message);
  }

  const result = data.data as GetAuthorQuery;
  return result.author as Author;
}

export async function createAuthor(input: CreateAuthorInput): Promise<Author> {
  const response = await fetchWithAuth(`${API_URL}/graphql`, {
    method: "POST",
    body: JSON.stringify({
      query: CREATE_AUTHOR.loc?.source.body,
      variables: { input },
    }),
  });

  const data = await response.json();

  if (data.errors) {
    throw new Error(data.errors[0].message);
  }

  const result = data.data as CreateAuthorMutation;
  return result.createAuthor.author as Author;
}

export async function updateAuthor(id: string, input: UpdateAuthorInput): Promise<Author> {
  const response = await fetchWithAuth(`${API_URL}/graphql`, {
    method: "POST",
    body: JSON.stringify({
      query: UPDATE_AUTHOR.loc?.source.body,
      variables: { id, input },
    }),
  });

  const data = await response.json();

  if (data.errors) {
    throw new Error(data.errors[0].message);
  }

  const result = data.data as UpdateAuthorMutation;
  return result.updateAuthor.author as Author;
}

export async function deleteAuthor(id: string): Promise<boolean> {
  const response = await fetchWithAuth(`${API_URL}/graphql`, {
    method: "POST",
    body: JSON.stringify({
      query: DELETE_AUTHOR.loc?.source.body,
      variables: { id },
    }),
  });

  const data = await response.json();

  if (data.errors) {
    throw new Error(data.errors[0].message);
  }

  const result = data.data as DeleteAuthorMutation;
  return result.deleteAuthor.success;
}

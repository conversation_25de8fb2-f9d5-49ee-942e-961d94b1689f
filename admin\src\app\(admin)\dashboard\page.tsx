import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Башбит | Шигърият.ру Админ",
  description: "Шигърият.ру админ панеленең башбите",
};

export default function DashboardPage() {
  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">Башбит</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Шагыйрьләр</CardTitle>
            <CardDescription>Шагыйрьләр белән идарә итү</CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild>
              <Link href="/authors">Шагыйрьләр исемлеге</Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Әсәрләр</CardTitle>
            <CardDescription>Әсәрләр белән идарә итү</CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild>
              <Link href="/works">Әсәрләр исемлеге</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

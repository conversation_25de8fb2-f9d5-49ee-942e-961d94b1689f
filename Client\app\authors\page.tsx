'use client';

import Layout from "@/components/layout";
import { getAuthorsList, Author } from "@/lib/authors";
import Link from "next/link";
import { useState } from "react";

export default function AuthorsPage() {
  const [authors, setAuthors] = useState<Author[]>([]);
  const [filterText, setFilterText] = useState("");

  // Load authors on component mount
  useState(() => {
    const loadAuthors = async () => {
      const authorsList = await getAuthorsList();
      setAuthors(authorsList);
    };
    loadAuthors();
  }, []);

  const filteredAuthors = authors.filter((author) =>
    author.displayName.toLowerCase().includes(filterText.toLowerCase())
  );

  return (
    <Layout home>
      <section className="container mx-auto px-4 py-8">
        <h2 className="text-3xl font-bold text-gray-800 mb-4">Шагыйрьләр</h2>

        <input
          type="text"
          placeholder="Шагыйрьне эзләргә..."
          value={filterText}
          onChange={(e) => setFilterText(e.target.value)}
          className="w-full mb-4 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />

        <ul className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredAuthors.map(({ id, displayName }) => (
            <li key={id} className="mb-2">
              <Link href={`/${id}`} className="text-xl font-medium underline hover:text-blue-700">
                {displayName}
              </Link>
            </li>
          ))}
        </ul>
      </section>
    </Layout>
  );
} 
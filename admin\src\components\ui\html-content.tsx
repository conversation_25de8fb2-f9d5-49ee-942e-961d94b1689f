"use client";

import { cn } from "@/lib/utils";

interface HtmlContentProps {
  html: string;
  className?: string;
}

/**
 * Компонент для безопасного отображения HTML-контента
 */
export function HtmlContent({ html, className }: HtmlContentProps) {
  // Функция для обработки переносов строк
  const processLineBreaks = (htmlContent: string) => {
    // Заменяем одиночные переносы строк на <br>
    return htmlContent.replace(/\n(?!\n)/g, '<br>');
  };

  const processedHtml = processLineBreaks(html);

  return (
    <div
      className={cn("content-html", className)}
      dangerouslySetInnerHTML={{ __html: processedHtml }}
    />
  );
}

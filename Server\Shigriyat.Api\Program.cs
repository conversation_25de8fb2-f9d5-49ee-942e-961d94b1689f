using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using Shigriyat.Api.Utils;
using System.Threading.Tasks;

namespace Shigriyat.Api
{
    public class Program
    {
        public static async Task Main(string[] args)
        {
            var host = CreateHostBuilder(args).Build();

            // Инициализация администратора
            await AdminInitializer.InitializeAdmin(host);

            await host.RunAsync();
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseStartup<Startup>();
                });
    }
}

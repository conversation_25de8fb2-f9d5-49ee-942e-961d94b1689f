import { NextResponse } from "next/server";

export async function GET(request: Request) {
  try {
    console.log("Callback credentials GET request");

    // Перенаправляем на главную страницу
    return NextResponse.redirect(new URL("/dashboard", request.url));
  } catch (error) {
    console.error("Callback credentials GET error:", error);
    return NextResponse.json({ error: "Authentication failed" }, { status: 401 });
  }
}

export async function POST(/* request: Request */) {
  try {
    console.log("Callback credentials POST request");

    // Возвращаем успешный ответ
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Callback credentials POST error:", error);
    return NextResponse.json({ error: "Authentication failed" }, { status: 401 });
  }
}

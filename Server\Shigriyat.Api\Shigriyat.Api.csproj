<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <LangVersion>latestmajor</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="HotChocolate.AspNetCore" Version="15.1.3" />
    <PackageReference Include="HotChocolate.AspNetCore.Authorization" Version="15.1.3" />
    <PackageReference Include="HotChocolate.Authorization" Version="15.1.3" />
    <PackageReference Include="HotChocolate.Data" Version="15.1.3" />
    <PackageReference Include="HotChocolate.Data.Raven" Version="15.1.3" />
    <PackageReference Include="HotChocolate.Execution" Version="15.1.3" />
    <PackageReference Include="HotChocolate.Types.Analyzers" Version="15.1.3" />
    <PackageReference Include="HotChocolate.Types.OffsetPagination" Version="15.1.3" />
    <PackageReference Include="JetBrains.Annotations" Version="2025.1.0-eap1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.0-preview.3.24172.13" />
    <PackageReference Include="RavenDB.Client" Version="7.0.1" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Shigriyat.Domain\Shigriyat.Domain.csproj" />
  </ItemGroup>

</Project>

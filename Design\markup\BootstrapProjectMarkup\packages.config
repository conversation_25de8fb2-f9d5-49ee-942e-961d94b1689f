﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="jQuery" version="2.0.3" targetFramework="net40" />
  <package id="jQuery.Validation" version="1.11.1" targetFramework="net40" />
  <package id="Microsoft.AspNet.Mvc" version="4.0.30506.0" targetFramework="net40" />
  <package id="Microsoft.AspNet.Mvc.FixedDisplayModes" version="1.0.1" targetFramework="net40" />
  <package id="Microsoft.AspNet.Razor" version="2.0.30506.0" targetFramework="net40" />
  <package id="Microsoft.AspNet.Web.Optimization" version="1.1.0" targetFramework="net40" />
  <package id="Microsoft.AspNet.WebPages" version="2.0.30506.0" targetFramework="net40" />
  <package id="Microsoft.jQuery.Unobtrusive.Validation" version="2.0.30506.0" targetFramework="net40" />
  <package id="Microsoft.Web.Infrastructure" version="1.0.0.0" targetFramework="net40" />
  <package id="Twitter.Bootstrap" version="2.3.2" targetFramework="net40" />
  <package id="WebGrease" version="1.3.0" targetFramework="net40" />
</packages>
import Layout from "@/components/layout";
import utilStyles from "@/styles/utils.module.css";
import {
  ApolloClient,
  InMemoryCache,
  gql,
} from "@apollo/client";

interface GuestbookMessage {
  authorName: string;
  authorEmail: string;
  message: string;
  messageDate: string;
  answerAuthorName?: string;
  answerMessage?: string;
  answerDate?: string;
}

interface PageInfo {
  hasNextPage: boolean;
  startCursor: string;
  endCursor: string;
}

interface GuestbookMessagesData {
  nodes: GuestbookMessage[];
  pageInfo: PageInfo;
}

export default async function GuestbookPage() {
  const client = new ApolloClient({
    uri: "http://localhost:3556/graphql/",
    cache: new InMemoryCache(),
  });

  const guestbookMessageReponse = await client.query<{
    guestbookMessages: GuestbookMessagesData;
  }>({
    query: gql`
      query {
        guestbookMessages(order: { messageDate: ASC }, first: 10) {
          pageInfo {
            hasNextPage
            startCursor
            endCursor
          }
          nodes {
            authorName
            authorEmail
            message
            messageDate
            answerAuthorName
            answerMessage
            answerDate
          }
        }
      }
    `,
  });

  const messages = guestbookMessageReponse.data.guestbookMessages;

  return (
    <Layout home>
      <section className={`${utilStyles.headingMd} ${utilStyles.padding1px}`}>
        <h2 className={utilStyles.headingLg}>Гостевая книга</h2>
        <ul className={utilStyles.list}>
          {messages.nodes.map(({ authorName, message }) => (
            <li className={utilStyles.listItem} key={message}>
              <a>{authorName}</a>
              <span>{message}</span>
            </li>
          ))}
        </ul>
      </section>
    </Layout>
  );
} 
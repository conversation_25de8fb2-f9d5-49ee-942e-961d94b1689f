import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getAuthors, getAuthor, createAuthor, updateAuthor, deleteAuthor } from "@/lib/api/authors";
import { Author, CreateAuthorInput, UpdateAuthorInput } from "@/lib/graphql/generated/graphql";

export function useAuthors(page: number = 1, pageSize: number = 10, filter?: string) {
  return useQuery({
    queryKey: ["authors", { page, pageSize, filter }],
    queryFn: () => getAuthors(page, pageSize, filter),
    staleTime: 5000, // Данные считаются актуальными в течение 5 секунд
    placeholderData: (previousData) => previousData, // Используем предыдущие данные как заполнитель
  });
}

export function useAuthor(id: string) {
  return useQuery({
    queryKey: ["author", id],
    queryFn: () => getAuthor(id),
    enabled: !!id,
  });
}

export function useCreateAuthor() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateAuthorInput) => createAuthor(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["authors"] });
    },
  });
}

export function useUpdateAuthor() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateAuthorInput }) =>
      updateAuthor(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ["authors"] });
      queryClient.invalidateQueries({ queryKey: ["authors", variables.id] });
    },
  });
}

export function useDeleteAuthor() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deleteAuthor(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["authors"] });
    },
  });
}

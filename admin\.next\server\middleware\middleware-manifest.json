{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_35401bb2._.js", "server/edge/chunks/[root of the server]__860be4f0._.js", "server/edge/chunks/edge-wrapper_b36fea90.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/dashboard/:path*{(\\\\.json)}?", "originalSource": "/dashboard/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/authors/:path*{(\\\\.json)}?", "originalSource": "/authors/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/works/:path*{(\\\\.json)}?", "originalSource": "/works/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/login{(\\\\.json)}?", "originalSource": "/login"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "jIBJsTS5W+Q9SPDF+Y9qVlmAvdl4pgmfLaawoITWOIU=", "__NEXT_PREVIEW_MODE_ID": "a9996f8b6c17932a4f0b49734caf1261", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "fef9b17567d6fb3b759dceb5c6387184b093ad5e3ca84c106445b64481ad061a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "35b369bd15a96eb4653a92e937579778fcd17176ea7f0b85f29a1790c56ec753"}}}, "instrumentation": null, "functions": {}}
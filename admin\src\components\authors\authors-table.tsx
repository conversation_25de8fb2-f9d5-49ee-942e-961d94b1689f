"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useAuthors, useDeleteAuthor } from "@/lib/hooks/use-authors";
import { formatDate } from "@/lib/utils/format-date";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { toast } from "sonner";
import { Pagination } from "@/components/ui/pagination";
import { ChevronsUpDown } from "lucide-react";
import { cn } from "@/lib/utils";

export function AuthorsTable() {
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [authorToDelete, setAuthorToDelete] = useState<string | null>(null);
  const [pageSizeDropdownOpen, setPageSizeDropdownOpen] = useState(false);
  const pageSizeRef = useRef<HTMLDivElement>(null);

  // Дебаунсинг для поиска
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Сброс страницы при изменении поискового запроса
  useEffect(() => {
    setCurrentPage(1);
  }, [debouncedSearchTerm]);

  // Закрытие выпадающего списка размера страницы при клике вне его
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (pageSizeRef.current && !pageSizeRef.current.contains(event.target as Node)) {
        setPageSizeDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const { data, isLoading, error, refetch } = useAuthors(currentPage, pageSize, debouncedSearchTerm);

  // Используем useRef для сохранения ссылки на поле ввода
  const searchInputRef = useRef<HTMLInputElement>(null);
  const deleteAuthor = useDeleteAuthor();

  const authors = data?.authors || [];
  const totalCount = data?.totalCount || 0;
  const totalPages = Math.ceil(totalCount / pageSize);

  if (isLoading) {
    return <div className="text-center py-4">Йөкләнә...</div>;
  }

  if (error) {
    return <div className="text-center py-4 text-red-500">Хата: {error.message}</div>;
  }

  // Фильтрация происходит на сервере
  const filteredAuthors = authors;

  const handleDelete = async () => {
    if (authorToDelete) {
      try {
        await deleteAuthor.mutateAsync(authorToDelete);
        toast.success("Шагыйрь уңышлы бетерелде");
        setAuthorToDelete(null);

        // Сохраняем фокус после обновления данных
        const activeElement = document.activeElement;
        await refetch();
        if (activeElement === searchInputRef.current) {
          searchInputRef.current?.focus();
        }
      } catch (error) {
        toast.error("Хата: " + (error instanceof Error ? error.message : "Билгесез хата"));
      }
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 w-full sm:w-auto">
          <Input
            ref={searchInputRef}
            placeholder="Эзләү..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full sm:w-auto max-w-sm"
          />
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground whitespace-nowrap">Биттә күрсәтү:</span>
            <div className="relative" ref={pageSizeRef}>
              <Button
                variant="outline"
                className="w-16 h-9 px-3 flex justify-between items-center"
                onClick={() => setPageSizeDropdownOpen(!pageSizeDropdownOpen)}
              >
                {pageSize}
                <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50" />
              </Button>

              {pageSizeDropdownOpen && (
                <div className="absolute top-full mt-1 w-16 rounded-md border bg-popover text-popover-foreground shadow-md z-[9999]">
                  <div className="p-1">
                    {[5, 10, 20, 50].map((size) => (
                      <div
                        key={size}
                        className={cn(
                          "relative flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground",
                          pageSize === size && "bg-accent text-accent-foreground"
                        )}
                        onClick={() => {
                          setPageSize(size);
                          setCurrentPage(1);
                          setPageSizeDropdownOpen(false);
                        }}
                      >
                        {size}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
        <Button onClick={() => router.push("/authors/create")}>
          Яңа шагыйрь өстәү
        </Button>
      </div>

      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Исем</TableHead>
              <TableHead>Туган көне</TableHead>
              <TableHead>Үлгән көне</TableHead>
              <TableHead>Өстәлгән вакыт</TableHead>
              <TableHead className="text-right">Гамәлләр</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredAuthors?.length ? (
              filteredAuthors.map((author) => (
                <TableRow key={author.id}>
                  <TableCell className="font-medium">{author.displayName}</TableCell>
                  <TableCell>{author.birthDate ? formatDate(author.birthDate) : "-"}</TableCell>
                  <TableCell>{author.deathDate ? formatDate(author.deathDate) : "-"}</TableCell>
                  <TableCell>{formatDate(author.addedDate)}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="secondary"
                        size="sm"
                        onClick={() => router.push(`/works?authorId=${encodeURIComponent(author.id)}`)}
                      >
                        Әсәрләр
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => router.push(`/authors/${author.urlPart}`)}
                      >
                        Үзгәртү
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => setAuthorToDelete(author.id)}
                      >
                        Бетерү
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={5} className="text-center">
                  Шагыйрьләр табылмады
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {totalPages > 1 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={setCurrentPage}
        />
      )}

      <div className="text-sm text-muted-foreground mt-2 text-center">
        Барлыгы {totalCount} шагыйрь, күрсәтелә {(currentPage - 1) * pageSize + 1}-{Math.min(currentPage * pageSize, totalCount)}
      </div>

      <Dialog open={!!authorToDelete} onOpenChange={(open) => !open && setAuthorToDelete(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Шагыйрьне бетерергә телисезме?</DialogTitle>
            <DialogDescription>
              Бу гамәлне кире кайтарып булмый. Шагыйрь һәм аның барлык әсәрләре бетереләчәк.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setAuthorToDelete(null)}>
              Юк
            </Button>
            <Button variant="destructive" onClick={handleDelete} disabled={deleteAuthor.isPending}>
              {deleteAuthor.isPending ? "Бетерелә..." : "Әйе, бетерергә"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

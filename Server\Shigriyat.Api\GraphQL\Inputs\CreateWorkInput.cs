using System;
using System.Collections.Generic;
using JetBrains.Annotations;

namespace Shigriyat.Api.GraphQL.Inputs
{
    [UsedImplicitly]
    public class CreateWorkInput
    {
        public string AuthorId { get; set; }
        public string Title { get; set; }
        public string Content { get; set; }
        public List<string> Genres { get; set; }
        public string Comments { get; set; }
        public string Source { get; set; }
        public DateTime? PublishedDate { get; set; }
    }
}

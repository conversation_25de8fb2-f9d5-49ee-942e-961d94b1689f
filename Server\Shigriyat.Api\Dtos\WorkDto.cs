﻿using Shigriyat.Api.Extensions;
using Shigriyat.Data;
using System;

namespace Shigriyat.Api.Dtos
{
    public sealed class WorkDto
    {
        public string WorkId { get; }
        public string AuthorId { get; }

        public string Title { get; }
        public string AuthorName { get; }
        public string Content { get; }
        public DateTime PublishedDate { get; }
        // short version of the content
        public string Excerpt => Content.Length > 100 ? Content.Substring(0, 100) : Content;

        public WorkDto(Author author, Work work)
        {
            WorkId = work.Id.TrimPrefix(Work.IdPrefix);
            AuthorId = author.Id.TrimPrefix(Author.IdPrefix);
            Title = work.Title;
            AuthorName = author.DisplayName;
            Content = work.Content;
            PublishedDate = work.PublishedDate;
        }
    }
}
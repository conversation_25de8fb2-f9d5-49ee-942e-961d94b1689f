using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using Shigriyat.Data;
using BC = BCrypt.Net.BCrypt;

namespace Shigriyat.Api.Utils
{
    public static class AdminInitializer
    {
        public static async Task InitializeAdmin(IHost host)
        {
            using var scope = host.Services.CreateScope();
            var services = scope.ServiceProvider;
            var logger = services.GetRequiredService<ILogger<Program>>();
            var store = services.GetRequiredService<IDocumentStore>();

            try
            {
                using var session = store.OpenAsyncSession();
                var adminUser = await session.Query<User>()
                    .FirstOrDefaultAsync(u => u.Username == "admin");

                if (adminUser == null)
                {
                    logger.LogInformation("Admin user not found. Creating default admin user.");
                    
                    var user = new User
                    {
                        UrlPart = "admin",
                        Username = "admin",
                        PasswordHash = BC.HashPassword("admin123"), // Временный пароль, который нужно будет сменить
                        Name = "Administrator",
                        Email = "<EMAIL>",
                        Role = "Admin",
                        CreatedDate = DateTime.UtcNow,
                        LastLoginDate = DateTime.UtcNow
                    };

                    await session.StoreAsync(user);
                    await session.SaveChangesAsync();
                    
                    logger.LogInformation("Default admin user created successfully.");
                }
                else
                {
                    logger.LogInformation("Admin user already exists.");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while initializing admin user.");
            }
        }
    }
}

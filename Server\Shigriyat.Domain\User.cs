using System;
using JetBrains.Annotations;
using Newtonsoft.Json;

namespace Shigriyat.Data
{
    /// <summary>
    /// Пользователь системы
    /// </summary>
    public sealed class User
    {
        public const string IdPrefix = "users/";
        private string urlPart;

        /// <summary>
        /// Идентификатор: IdPrefix + UrlPart
        /// </summary>
        [NotNull]
        public string Id { get; private set; }
        
        [JsonIgnore]
        [NotNull]
        public string UrlPart
        {
            get
            {
                if (urlPart != null)
                    return urlPart;

                if (string.IsNullOrEmpty(Id))
                    return Id;
                if (Id.StartsWith(IdPrefix))
                    return Id.Substring(IdPrefix.Length);
                throw new InvalidOperationException("Id not start with prefix");
            }
            set
            {
                urlPart = value;
                Id = IdPrefix + value;
            }
        }

        [NotNull]
        public string Username { get; set; }
        
        [NotNull]
        public string PasswordHash { get; set; }
        
        [NotNull]
        public string Name { get; set; }
        
        [CanBeNull]
        public string Email { get; set; }
        
        [NotNull]
        public string Role { get; set; }
        
        public DateTime CreatedDate { get; set; }
        public DateTime LastLoginDate { get; set; }
    }
}

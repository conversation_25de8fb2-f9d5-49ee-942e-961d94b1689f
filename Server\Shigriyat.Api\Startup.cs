using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using Shigriyat.Api.Settings;
using System;
using System.Text;
using Shigriyat.Api.GraphQL;
using Shigriyat.Api.Logging;

namespace Shigriyat.Api
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddControllers();
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "Shigriyat.Api", Version = "v1" });

                c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
                {
                    Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
                    Name = "Authorization",
                    In = ParameterLocation.Header,
                    Type = SecuritySchemeType.ApiKey,
                    Scheme = "Bearer"
                });

                c.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "Bearer"
                            }
                        },
                        Array.Empty<string>()
                    }
                });
            });

            // Настройка JWT аутентификации
            services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                .AddJwtBearer(options =>
                {
                    options.TokenValidationParameters = new TokenValidationParameters
                    {
                        ValidateIssuer = true,
                        ValidateAudience = true,
                        ValidateLifetime = true,
                        ValidateIssuerSigningKey = true,
                        ValidIssuer = Configuration["Jwt:Issuer"],
                        ValidAudience = Configuration["Jwt:Audience"],
                        IssuerSigningKey = new SymmetricSecurityKey(
                            Encoding.UTF8.GetBytes(Configuration["Jwt:Key"]))
                    };
                });


            services
                //.AddAuthorization()
                .AddGraphQLServer()
                .DisableIntrospection(false)
                .ModifyCostOptions(o => o.EnforceCostLimits = false)
                .AddAuthorization()
                .AddGraphQL()
                .ModifyRequestOptions(options => options.IncludeExceptionDetails = true)
                .AddQueryType<SchemaRoot>()
                .AddDiagnosticEventListener<ConsoleQueryLogger>()
                .AddMutationType()
                .AddTypeExtension<AdminMutations>()
                .AddRavenFiltering()
                .AddRavenSorting()
                .AddRavenPagingProviders()
                .AddRavenProjections();

            ConfigureRaven(services);
        }

        private void ConfigureRaven(IServiceCollection services)
        {
            var settings = new RavenSettings();
            Configuration.Bind(settings);

            var store = new DocumentStore
            {
                Urls = settings.Database.Urls,
                Database = settings.Database.DatabaseName,
            };

            store.Initialize();

            services.AddSingleton<IDocumentStore>(store);

            services.AddTransient<IAsyncDocumentSession>(serviceProvider => serviceProvider
                .GetService<IDocumentStore>()
                .OpenAsyncSession());

            services.AddTransient<IDocumentSession>(serviceProvider => serviceProvider
                .GetService<IDocumentStore>()
                .OpenSession());
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
                app.UseSwagger();
                app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "Shigriyat.Api v1"));
            }

            app.UseRouting();

            // Добавляем CORS для админской части
            app.UseCors(builder => builder
                .AllowAnyOrigin()
                .AllowAnyMethod()
                .AllowAnyHeader());

            app.UseAuthentication();
            app.UseAuthorization();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapGraphQL();
                endpoints.MapControllers();
            });
        }
    }
}

"use client";

import { WorkForm } from "@/components/works/work-form";
import { useSearchParams } from "next/navigation";
import { useDynamicTitle } from "@/lib/utils/use-dynamic-title";
import { useMemo } from "react";

export default function CreateWorkPage() {
  const searchParams = useSearchParams();
  const authorId = searchParams.get("authorId");

  // Мемоизируем заголовок
  const pageTitle = useMemo(() => "Яңа әсәр өстәү", []);

  // Устанавливаем заголовок страницы
  useDynamicTitle(pageTitle);

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-8">Яңа әсәр өстәү</h1>
      <WorkForm authorId={authorId || undefined} />
    </div>
  );
}

/* Стили для отображения HTML-контента */
.content-html h1 {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0.75rem 0;
  color: rgb(5, 5, 5);
}

.content-html h2 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0.75rem 0;
  color: rgb(5, 5, 5);
}

.content-html h3 {
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0.75rem 0;
  color: rgb(5, 5, 5);
}

.content-html blockquote {
  border-left: 4px solid #ccc;
  margin: 1rem 0;
  padding-left: 1rem;
  color: rgb(101, 103, 107);
  font-style: italic;
}

.content-html ul {
  list-style-type: disc;
  margin: 1rem 0;
  padding-left: 2rem;
}

.content-html ol {
  list-style-type: decimal;
  margin: 1rem 0;
  padding-left: 2rem;
}

.content-html li {
  margin: 0.25rem 0;
}

.content-html a {
  color: rgb(33, 111, 219);
  text-decoration: underline;
  cursor: pointer;
}

.content-html p {
  margin: 0.75rem 0;
}

.content-html strong,
.content-html b {
  font-weight: bold;
}

.content-html em,
.content-html i {
  font-style: italic;
}

.content-html u {
  text-decoration: underline;
}

.content-html s,
.content-html strike {
  text-decoration: line-through;
}

.content-html img {
  max-width: 100%;
  height: auto;
  margin: 1rem 0;
}

"use client";

import { WorksTable } from "@/components/works/works-table";
import { useSearchParams, useRouter, usePathname } from "next/navigation";
import { useCallback, useMemo } from "react";
import { useDynamicTitle } from "@/lib/utils/use-dynamic-title";

export default function WorksListPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const authorId = searchParams.get("authorId");

  // Мемоизируем заголовок, чтобы он не менялся при каждом рендере
  const pageTitle = useMemo(() => {
    return authorId ? "Автор әсәрләре" : "Әсәрләр";
  }, [authorId]);

  // Устанавливаем заголовок страницы
  useDynamicTitle(pageTitle);

  // Используем useCallback для предотвращения лишних перерендеров
  const handleAuthorChange = useCallback((newAuthorId: string) => {
    // Создаем новый объект URLSearchParams вместо изменения существующего
    const params = new URLSearchParams();

    // Сохраняем все существующие параметры, кроме authorId
    searchParams.forEach((value, key) => {
      if (key !== "authorId") {
        params.set(key, value);
      }
    });

    // Добавляем новый authorId, если он есть
    if (newAuthorId) {
      params.set("authorId", newAuthorId);
    }

    // Используем реплейс вместо пуш
    const url = params.toString() ? `${pathname}?${params.toString()}` : pathname;
    router.replace(url, { scroll: false });
  }, [pathname, router, searchParams]);

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-8">Әсәрләр</h1>
      <WorksTable initialAuthorId={authorId || undefined} onAuthorChange={handleAuthorChange} />
    </div>
  );
}

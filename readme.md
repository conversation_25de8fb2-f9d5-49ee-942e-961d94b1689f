shigriyat.ru представляет собой онлайн-платформу, где пользователи могут изучать татарскую поэзию. На сайте доступны стихи известных татарских поэтов, их биографии и возможность прослушивания произношения текста.

Структура проекта:

1. **/Client**:
   - Основной сайт shigriyat.ru.
   - Интерфейс для пользователей с возможностью поиска и просмотра информации о татарских поэтах.
   - Статический сайт, написанный на Next.js и Tailwind CSS.
   - Весь сайт и контент на татарском языке.
   - Более подробное описание проекта можно найти в /Client/readme.md

2. **/Server**:
   - .NET backend сервис GraphQL.
   - Предоставляет данные для работы сайта /Client.
   - Использует базу данных RavenDB для хранения информации о стихах, биографиях и произношении текст
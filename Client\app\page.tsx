import { PoemSection } from "@/components/poem-section";
import Layout from "@/components/layout";
import { getAuthorsList } from "@/lib/authors";
import Link from "next/link";

export default async function Home() {
  const authors = await getAuthorsList();

  return (
    <Layout home>
      <section className="container mx-auto px-4 py-8">
        <PoemSection category="love" />

        <h2 className="text-3xl font-bold text-gray-800 mb-4">Шагыйрьләр</h2>

        <ul className="list-disc list-inside">
          {authors.map(({ id, displayName }) => (
            <li key={id} className="mb-2">
              <Link href={`/${id}`} className="text-xl font-medium underline hover:text-blue-700">
                {displayName}
              </Link>
            </li>
          ))}
        </ul>
      </section>
    </Layout>
  );
} 
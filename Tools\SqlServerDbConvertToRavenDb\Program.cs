﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Raven.Client.Documents;
using Shigriyat.Data;

namespace SqlServerDbConvertToRavenDb
{
    internal sealed class Program
    {
        private const string RavenDbName = "shigriyat";

        static void Main(string[] args)
        {
            using (var sqlConnection = new SqlConnection(ConfigurationManager.ConnectionStrings["sql"].ConnectionString))
            using (var ravenStore = new DocumentStore
            {
                Database = "Shigriyat",
                Urls = new[] { "http://localhost:8080" },
            })
            {
                sqlConnection.Open();

                ravenStore.Conventions.RegisterAsyncIdConvention<GuestbookMessage>(
                    (dbname, guestbookMessage) =>
                        Task.FromResult($"guestbook/{guestbookMessage.Id}"));

                //NonAdminHttp.EnsureCanListenToWhenInNonAdminContext(8080);
                ravenStore.Initialize();
                //ravenStore.DatabaseCommands.EnsureDatabaseExists(RavenDbName);

                var authorLegacyIdToAuthor = new Dictionary<int, Author>();
                var command =
                    new SqlCommand(
                        "SELECT [ID],[UrlPart],[DeathDate],[Biography],[AddDate],[BirthDate],[SurName],[Name],[DisplayName],[LastName] FROM [Authors]",
                        sqlConnection);
                using (var reader = command.ExecuteReader())
                using (var ravenSession = ravenStore.OpenSession())
                {
                    int authorsCount = 0;
                    while (reader.Read())
                    {
                        var authorId = reader.GetInt32(0);
                        var authorUrlPart = reader.GetString(1);
                        var author = new Author
                        {
                            UrlPart = authorUrlPart,
                            LegacyId = authorId,
                            DeathDate = reader.IsDBNull(2) ? (DateTime?)null : reader.GetDateTime(2),

                            Biography = reader.IsDBNull(3) ? null : reader.GetString(3),

                            AddedDate = reader.GetDateTime(4),
                            ModifiedDate = reader.GetDateTime(4),
                            BirthDate = reader.IsDBNull(5) ? (DateTime?)null : reader.GetDateTime(5),

                            SurName = reader.IsDBNull(6) ? null : reader.GetString(6),
                            Name = reader.GetString(7),
                            DisplayName = reader.GetString(8),
                            LastName = reader.IsDBNull(9) ? null : reader.GetString(9),
                        };
                        authorLegacyIdToAuthor.Add(authorId, author);
                        ravenSession.Store(author);
                        authorsCount++;
                    }
                    ravenSession.SaveChanges();
                    Console.WriteLine("{0} authors exported.", authorsCount);
                }

                command = new SqlCommand(
                    "SELECT [ID],[UrlPart],[PublishedDate],[PublishedBy],[ChangedDate],[ChangedBy],[Name],[Text],[Comments],[GettedFrom],[Author_id] FROM [Works]",
                    sqlConnection);
                using (var reader = command.ExecuteReader())
                using (var ravenSession = ravenStore.OpenSession())
                {
                    int worksCount = 0;
                    while (reader.Read())
                    {
                        var authorId = reader.GetInt32(10);
                        var work = new Work
                        {
                            LegacyId = reader.GetInt32(0),
                            UrlPart = reader.GetString(1),

                            PublishedDate = reader.GetDateTime(2),
                            PublishedBy = reader.GetString(3),

                            ModifiedDate = reader.IsDBNull(4) ? reader.GetDateTime(2) : reader.GetDateTime(4),
                            ModifiedBy = reader.IsDBNull(5) ? reader.GetString(3) : reader.GetString(5),

                            Title = reader.GetString(6),
                            Content = reader.GetString(7),
                            Comments = reader.IsDBNull(8) ? null : reader.GetString(8),

                            Source = reader.IsDBNull(9) ? null : reader.GetString(9),

                            AuthorId = authorLegacyIdToAuthor[authorId].Id,
                        };
                        ravenSession.Store(work);
                        worksCount++;
                    }
                    ravenSession.SaveChanges();
                    Console.WriteLine("{0} works exported.", worksCount);
                }
                command = new SqlCommand(
                    "SELECT [ID],[MessageDate],[Message],[AuthorName],[AuthorEmail],[AnswerAuthorName],[AnswerMessage],[AnswerDate] FROM [Guestbook]",
                    sqlConnection);
                using (var reader = command.ExecuteReader())
                using (var ravenSession = ravenStore.OpenSession())
                {
                    int guestbookMessagesCount = 0;
                    while (reader.Read())
                    {
                        var guestbookMessage = new GuestbookMessage
                        {
                            Id = reader.GetGuid(0).ToString(),

                            MessageDate = reader.GetDateTime(1),
                            Message = reader.GetString(2),
                            AuthorName = reader.GetString(3),
                            AuthorEmail = reader.IsDBNull(4) ? null : reader.GetString(4),

                            AnswerAuthorName = reader.IsDBNull(5) ? null : reader.GetString(5),
                            AnswerMessage = reader.IsDBNull(6) ? null : reader.GetString(6),
                            AnswerDate = reader.IsDBNull(7) ? (DateTime?)null : reader.GetDateTime(7),
                        };
                        ravenSession.Store(guestbookMessage);
                        guestbookMessagesCount++;
                    }
                    ravenSession.SaveChanges();
                    Console.WriteLine("{0} guestbook messages exported.", guestbookMessagesCount);
                }


                Console.WriteLine("Import successfuly completed! Press enter for shutdown raven EmbeddedHttpServer ");
                Console.ReadLine();
            }
    }
}
}
"use client";

import { useSession } from "next-auth/react";
import { AdminHeader } from "./admin-header";
import { AdminSidebar } from "./admin-sidebar";
import { AuthCheck } from "@/components/auth/auth-check";
import { useState, useEffect } from "react";

interface AdminLayoutProps {
  children: React.ReactNode;
}

export function AdminLayout({ children }: AdminLayoutProps) {
  const { data: session } = useSession();
  const [isMounted, setIsMounted] = useState(false);

  // Устанавливаем isMounted в true после первого рендера
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Если компонент не смонтирован, возвращаем пустой div
  if (!isMounted) {
    return <div className="flex h-screen bg-slate-50"></div>;
  }

  return (
    <AuthCheck>
      <div className="flex h-screen bg-slate-50">
        <AdminSidebar />
        <div className="flex flex-col flex-1 overflow-hidden">
          <AdminHeader user={session?.user} />
          <main className="flex-1 overflow-y-auto p-6">
            {children}
          </main>
        </div>
      </div>
    </AuthCheck>
  );
}

import { NextResponse } from "next/server";
import { NextRequest } from "next/server";
import { getToken } from "next-auth/jwt";

export async function middleware(request: NextRequest) {
  try {
    console.log("Middleware running for path:", request.nextUrl.pathname);

    // Проверяем токен NextAuth
    let token = null;
    try {
      token = await getToken({ req: request });
    } catch (tokenError) {
      console.error("Error getting token:", tokenError);
    }

    console.log("Token in middleware:", token ? "exists" : "null");

    // Временное решение: проверяем cookie
    const adminAuthCookie = request.cookies.get("admin-auth");
    console.log("Admin auth cookie in middleware:", adminAuthCookie ? "exists" : "null");

    // Считаем аутентифицированным, если есть токен или cookie
    const isAuthenticated = !!token || !!adminAuthCookie;

    // Проверяем, является ли маршрут частью админской панели
    const isAdminRoute = request.nextUrl.pathname.startsWith("/dashboard") ||
                         request.nextUrl.pathname.startsWith("/authors") ||
                         request.nextUrl.pathname.startsWith("/works");

    // Если это админский маршрут и пользователь не аутентифицирован
    if (isAdminRoute && !isAuthenticated) {
      return NextResponse.redirect(new URL("/login", request.url));
    }

    // Если пользователь уже аутентифицирован и пытается зайти на страницу логина
    if (request.nextUrl.pathname === "/login" && isAuthenticated) {
      return NextResponse.redirect(new URL("/dashboard", request.url));
    }

    return NextResponse.next();
  } catch (error) {
    console.error("Middleware error:", error);
    return NextResponse.next();
  }
}

export const config = {
  matcher: ["/dashboard/:path*", "/authors/:path*", "/works/:path*", "/login"],
};

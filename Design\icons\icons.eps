%!PS-Adobe-3.0 EPSF-3.0
%%BoundingBox: 0 0 360 16 
%%LanguageLevel: 2
%%Creator: CorelDRAW X6
%%Title: icons.eps
%%CreationDate: Mon Aug 12 10:43:14 2013
%%DocumentProcessColors: <PERSON><PERSON> Yellow Black 
%%DocumentSuppliedResources: (atend)
%%EndComments
%%BeginProlog
/AutoFlatness false def
/AutoSteps 0 def
/CMYKMarks true def
/DocPsLevel 2 def
%Build: CorelDRAW X6 Version 16.1.0.843
/EpsFile true def
%%BeginResource: procset wCorel16Dict 16.0 0
/wCorel16Dict 300 dict def wCorel16Dict begin
%----------------------------------------------------------------------------
% Core Corel PostScript prolog functions
/bd{bind def}bind def/ld{load def}bd/xd{exch def}bd/rp{{pop}repeat}bd/dexec{
exch begin cvx exec end}bd/@cp/closepath ld/@gs/gsave ld/@gr/grestore ld/@np
/newpath ld/Tl/translate ld/$sv 0 def/@sv{/$sv save def}bd/@rs{$sv restore}bd
/spg/showpage ld/showpage{}bd currentscreen/@dsp xd/$dsp/@dsp def/$dsa xd/$dsf
xd/$sdf false def/$SDF false def/$Scra 0 def/SetScr/setscreen ld/@ss{2 index 0
eq{$dsf 3 1 roll 4 -1 roll pop}if exch $Scra add exch load SetScr}bd
/SeparationMode where{pop}{/SeparationMode/Composite def}ifelse
/SeparationPlateName where{pop}{/SeparationPlateName null def}ifelse
/SeparateInColor where{pop}{/SeparateInColor false def}ifelse/EpsFile where
{pop}{/EpsFile false def}ifelse/FillOverprint false def/$fil 0 def
/OutlineOverprint 0 def/$PF false def/$bkg false def/CurrentOverprint false def
matrix currentmatrix/$ctm xd/$ptm matrix def/$ttm matrix def/$stm matrix def
/$ffpnt true def/CorelDrawReencodeVect[16#0/grave 16#5/breve 16#6/dotaccent
16#8/ring 16#A/hungarumlaut 16#B/ogonek 16#C/caron 16#D/dotlessi
16#27/quotesingle 16#60/grave 16#7C/bar 16#80/Euro
16#82/quotesinglbase/florin/quotedblbase/ellipsis/dagger/daggerdbl
16#88/circumflex/perthousand/Scaron/guilsinglleft/OE
16#91/quoteleft/quoteright/quotedblleft/quotedblright/bullet/endash/emdash
16#98/tilde/trademark/scaron/guilsinglright/oe 16#9F/Ydieresis
16#A1/exclamdown/cent/sterling/currency/yen/brokenbar/section
16#a8/dieresis/copyright/ordfeminine/guillemotleft/logicalnot/minus/registered/macron
16#b0/degree/plusminus/twosuperior/threesuperior/acute/mu/paragraph/periodcentered
16#b8/cedilla/onesuperior/ordmasculine/guillemotright/onequarter/onehalf/threequarters/questiondown
16#c0/Agrave/Aacute/Acircumflex/Atilde/Adieresis/Aring/AE/Ccedilla
16#c8/Egrave/Eacute/Ecircumflex/Edieresis/Igrave/Iacute/Icircumflex/Idieresis
16#d0/Eth/Ntilde/Ograve/Oacute/Ocircumflex/Otilde/Odieresis/multiply
16#d8/Oslash/Ugrave/Uacute/Ucircumflex/Udieresis/Yacute/Thorn/germandbls
16#e0/agrave/aacute/acircumflex/atilde/adieresis/aring/ae/ccedilla
16#e8/egrave/eacute/ecircumflex/edieresis/igrave/iacute/icircumflex/idieresis
16#f0/eth/ntilde/ograve/oacute/ocircumflex/otilde/odieresis/divide
16#f8/oslash/ugrave/uacute/ucircumflex/udieresis/yacute/thorn/ydieresis]def
/get_ps_level/languagelevel where{pop systemdict/languagelevel get exec}{1}
ifelse def/Level2 get_ps_level 2 ge def/Level3 get_ps_level 3 ge def
/AdobeDistiller/product where{pop systemdict/setdistillerparams known product
(Adobe PostScript Parser)ne and}{false}ifelse def/InRipSeparation
AdobeDistiller{false}{Level2{currentpagedevice/Separations 2 copy known{get}{
pop pop false}ifelse}{false}ifelse}ifelse def/ColorSeparation/LumSepsDict where
{pop false}{/AldusSepsDict where{pop false}{InRipSeparation{true}{1 0 0 0 gsave
setcmykcolor currentcmykcolor grestore add add add 0 ne 0 1 0 0 gsave
setcmykcolor currentcmykcolor grestore add add add 0 ne 0 0 1 0 gsave
setcmykcolor currentcmykcolor grestore add add add 0 ne 0 0 0 1 gsave
setcmykcolor currentcmykcolor grestore add add add 0 ne and and and not}ifelse
}ifelse}ifelse def/IsColorDevice/deviceinfo where{pop deviceinfo/Colors known{
deviceinfo/Colors get exec 1 gt}{false}ifelse}{/statusdict where{pop statusdict
/processcolors known{statusdict/processcolors get exec 1 gt}{false}ifelse}{
false}ifelse}ifelse def/get_simulate_devicen get_ps_level 2 eq{{SeparationMode
/OnHost ne InRipSeparation AdobeDistiller or and}bind}{false}ifelse def
/DocGrayScaleSpace[/DeviceGray]def/DocRgbSpace[/DeviceRGB]def/DocCmykSpace
[/DeviceCMYK]def/DocLabSpace[/CIEBasedABC <</BlackPoint[0 0 0]/WhitePoint[
0.9637 1.0000 0.8241]/RangeABC[0 100 -128 127 -128 127]/DecodeABC[{16 add 116
div}bind{500 div}bind{200 div}bind]/MatrixABC[1 1 1 1 0 0 0 0 -1]/DecodeLMN[
{dup 0.206897 ge{dup dup mul mul}{0.137931 sub 0.128419 mul}ifelse 0.9637 mul
}bind{dup 0.206897 ge{dup dup mul mul}{0.137931 sub 0.128419 mul}ifelse 1.0000
mul}bind{dup 0.206897 ge{dup dup mul mul}{0.137931 sub 0.128419 mul}ifelse
0.8241 mul}bind]>>]def/validate_cie_colorspace_whitepoint{load dup aload pop
/WhitePoint get 1 1.0 put pop}bd/set_cie_colorspaces{/DocGrayScaleSpaceCIE
where{pop/DocGrayScaleSpaceCIE validate_cie_colorspace_whitepoint
/DocGrayScaleSpace xd DocGrayScaleSpace aload pop begin pop/DecodeA where{pop
DecodeA type/arraytype eq{DecodeA aload pop/DecodeA exch def}if}if end}if
/DocRgbSpaceCIE where{pop/DocRgbSpaceCIE validate_cie_colorspace_whitepoint
/DocRgbSpace xd}if/DocCmykSpaceCIE where{pop/DocCmykSpaceCIE
validate_cie_colorspace_whitepoint/DocCmykSpace xd}if}bd/set_rendering_intent{
Level3{findcolorrendering{/ColorRendering findresource setcolorrendering}{dup
/DefaultColorRendering eq{pop}{/ColorRendering findresource setcolorrendering}
}ifelse}{pop}ifelse}bd/findcmykcustomcolor where{pop}{/findcmykcustomcolor{5
array astore}def}ifelse/setcustomcolor where SeparationMode/OnHost ne and{pop}
{/tint_cmyk_color{5 -1 roll dup 1 ge{pop}{4{dup 6 -1 roll mul exch}repeat pop}
ifelse}bd/setprocesscolor_5{SepMode_5 0 eq{SetCmyk_5}{SepsColor not{4 1 roll
pop pop pop 1 exch sub SetGry}{SetCmyk_5}ifelse}ifelse}bd/setcustomcolor{exch
aload pop SepMode_5 0 eq{pop tint_cmyk_color setprocesscolor_5}{
CurrentInkName_5 eq{4 index}{0}ifelse 6 1 roll 5 rp 1 sub neg SetGry}ifelse}bd
}ifelse/convert_rgb_to_cmyk{dup type/dicttype eq{}{3{1 exch sub 3 1 roll}
repeat 3 copy min min 3{dup 5 -1 roll sub neg exch}repeat}ifelse}bd
/safe_setcolorspace{dup currentcolorspace eq{pop}{setcolorspace}ifelse}bd
/safe_setoverprint{dup type/booleantype eq{dup currentoverprint ne{
setoverprint}{pop}ifelse}{1 eq setoverprint}ifelse}bd/is_cmyk_channel_name{
/channel_name xd channel_name/Cyan eq channel_name/Magenta eq or channel_name
/Yellow eq or channel_name/Black eq or{true}{false}ifelse}bd
/is_rgb_channel_name{/channel_name xd channel_name/Red eq channel_name/Green eq
or channel_name/Blue eq or{true}{false}ifelse}bd/is_spot_channel_name{
/channel_name xd channel_name is_cmyk_channel_name channel_name
is_rgb_channel_name or channel_name/Gray eq or{false}{true}ifelse}bd
/spot_tint_transform_proc{/alternativeSpace xd/alternativeColors xd/tint xd
alternativeSpace DocLabSpace eq{/mixMethod/Additive def/altSpaceNumChannels 3
def}{alternativeSpace DocCmykSpace eq{/mixMethod/Subtractive def
/altSpaceNumChannels 4 def}{alternativeSpace DocRgbSpace eq{/mixMethod
/Additive def/altSpaceNumChannels 3 def}{alternativeSpace DocGrayScaleSpace eq
{/mixMethod/Additive def/altSpaceNumChannels 1 def}if}ifelse}ifelse}ifelse
alternativeSpace DocLabSpace eq{alternativeColors aload pop tint mul 3 1 roll
tint mul 3 1 roll dup 100 exch sub 1 tint sub mul add 3 1 roll}{/tintedColor
altSpaceNumChannels{0.0}repeat altSpaceNumChannels array astore def 0 1
altSpaceNumChannels 1 sub{/altChannelIndex exch def alternativeColors
altChannelIndex get mixMethod/Additive eq{1.0 exch sub tint mul 1.0 exch sub}{
tint mul}ifelse tintedColor altChannelIndex 3 2 roll put}for tintedColor aload
pop}ifelse}bd/devicen_colorant_mixer_proc{/numChannels xd/alternativeSpace xd
/channelNames xd/alternativeColors xd numColorants array astore/colorants xd
/is_spot_channel_name_loc{is_spot_channel_name}bind def alternativeSpace
DocCmykSpace eq{/mixMethod/Subtractive def/altSpaceNumChannels 4 def
/is_spot_channel_name_loc{is_cmyk_channel_name not}bind def}{alternativeSpace
DocRgbSpace eq{/mixMethod/Additive def/altSpaceNumChannels 3 def
/is_spot_channel_name_loc{is_rgb_channel_name not}bind def}{alternativeSpace
DocGrayScaleSpace eq{/mixMethod/Additive def/altSpaceNumChannels 1 def
/is_spot_channel_name_loc{/Gray ne}bind def}if}ifelse}ifelse mixMethod
/Additive eq{/mixedColor altSpaceNumChannels{0.0}repeat altSpaceNumChannels
array astore def/bProcessPlateTinted false def 0 1 numChannels 1 sub{
/channelIndex exch def channelNames channelIndex get is_spot_channel_name_loc
/bSpotChannel xd/sourceChannel colorants channelIndex get def/bWhiteBackground
0 mixedColor{add}forall altSpaceNumChannels div 0.999 gt bProcessPlateTinted
and def bSpotChannel bProcessPlateTinted not and{/bSpotChannel false def}if 0 1
altSpaceNumChannels 1 sub{/altChannelIndex exch def/mixedChannel mixedColor
altChannelIndex get def sourceChannel alternativeColors channelIndex get
altChannelIndex get 1.0 exch sub mul 1.0 exch sub dup 1.0 gt{pop 1.0}if dup 0.0
lt{pop 0.0}if bSpotChannel{sourceChannel 0.0 gt{mixedChannel mul}{pop
mixedChannel}ifelse}{/bProcessPlateTinted true def globaldict begin
/bSkipDeviceNImage where{pop false}{true}ifelse end DevicenImage and{pop
sourceChannel alternativeColors channelIndex get altChannelIndex get mul
mixedChannel add}{mixedChannel add}ifelse}ifelse dup 1.0 gt{pop 1.0}if dup 0.0
lt{pop 0.0}if mixedColor altChannelIndex 3 2 roll put}for}for}{/mixedColor
altSpaceNumChannels{1}repeat altSpaceNumChannels array astore def 0 1
altSpaceNumChannels 1 sub{/targetIndex exch def 0 1 numColorants 1 sub{
/sourceIndex exch def colorants sourceIndex get alternativeColors sourceIndex
get targetIndex get mul 1 exch sub mixedColor targetIndex get mul mixedColor
targetIndex 3 2 roll put}for mixedColor targetIndex 1 mixedColor targetIndex
get sub put}for}ifelse mixedColor aload pop}bd/ColorSpec 9 dict begin/color[]
def/names[]def/num 0 def/ColorModel null def/ColorSpace[]def/set_color{
ColorSpace safe_setcolorspace color aload pop setcolor}bd/separate_set_color{
SeparationMode/OnHost eq{separate_color{/set_color dexec true}{pop
currentoverprint{false}{SeparateInColor{0 0 0 0 SetCmyk_5 true}{1 SetGry true}
ifelse}ifelse}ifelse}{set_color true}ifelse}bd/separate_color{currentdict false
}bd/convert_to_cmyk{currentdict}bd currentdict end def/GrayscaleColorSpec
ColorSpec 9 dict copy begin/names{/Black}def/num 1 def/ColorModel/Grayscale def
/ColorSpace DocGrayScaleSpace def/separate_color{currentdict false
SeparationMode/OnHost eq{SeparationPlateName/Black eq{SeparateInColor{pop
convert_to_cmyk true}{pop true}ifelse}if}if}bd/convert_to_cmyk{0 0 0 color
aload pop 1 exch sub create_cmyk_color}bd currentdict end def
/create_grayscale_color{GrayscaleColorSpec 9 dict copy begin/color exch 1 array
astore def/ColorSpace DocGrayScaleSpace def currentdict end}bd/RgbColorSpec
ColorSpec 9 dict copy begin/names{/Red/Green/Blue}def/num 3 def/ColorModel/Rgb
def/ColorSpace DocRgbSpace def/separate_color{convert_to_cmyk/separate_color
dexec}bd/convert_to_cmyk{color aload pop convert_rgb_to_cmyk create_cmyk_color
}bd currentdict end def/create_rgb_color{RgbColorSpec 9 dict copy begin 3 array
astore/color exch def/ColorSpace DocRgbSpace def currentdict end}bd
/CmykColorSpec ColorSpec 9 dict copy begin/names{/Cyan/Magenta/Yellow/Black}
def/num 4 def/ColorModel/Cmyk def/ColorSpace DocCmykSpace def/separate_color{
currentdict false SeparationPlateIndex 0 ge SeparationPlateIndex 3 le and{
color SeparationPlateIndex get dup 0 gt{3 1 roll pop pop SeparateInColor{0 0 0
4 SeparationPlateIndex roll create_cmyk_color true}{1 exch sub
create_grayscale_color true}ifelse}{pop}ifelse}if}bd/convert_to_cmyk{
currentdict}bd currentdict end def/create_cmyk_color{CmykColorSpec 9 dict copy
begin 4 array astore/color exch def/ColorSpace DocCmykSpace def currentdict end
}bd/SpotColorSpec ColorSpec 9 dict copy begin/num 1 def/ColorModel/Spot def
/separate_color{currentdict false SeparationPlateIndex 4 eq{names aload pop
SeparationPlateName eq{color aload pop dup 0 gt{3 1 roll pop pop
SeparateInColor{pop convert_to_cmyk true}{1 exch sub create_grayscale_color
true}ifelse}{pop}ifelse}if}if}bd/convert_to_cmyk{/alternativeSpace ColorSpace 2
get def/tintTransformProc ColorSpace 3 get def alternativeSpace DocCmykSpace eq
{color aload pop tintTransformProc create_cmyk_color}{alternativeSpace
DocRgbSpace eq{color aload pop tintTransformProc create_rgb_color
/convert_to_cmyk dexec}{alternativeSpace DocGrayScaleSpace eq{0 0 0 color aload
pop 1 sub create_cmyk_color}if}ifelse}ifelse}bd currentdict end def
/create_spot_colorspace{load/alternativeSpace xd/alternativeColor xd/name xd[
/Separation name alternativeSpace[alternativeColor alternativeSpace
/spot_tint_transform_proc cvx]cvx]}bd/create_spot_color{SpotColorSpec 9 dict
copy begin dup type/arraytype ne{create_spot_colorspace}if/ColorSpace xd/color
exch 1 array astore def ColorSpace 1 get 1 array astore/names xd currentdict
end}bd/DevicenColorSpec SpotColorSpec 9 dict copy begin/ColorModel/DeviceN def
/set_color{Level3{ColorSpace safe_setcolorspace color aload pop setcolor}{
ColorSpace 2 get setcolorspace color aload pop ColorSpace 3 get exec setcolor}
ifelse}bd/separate_color{currentdict false/PlateIndex -1 def 0 1 num 1 sub{dup
names exch get SeparationPlateName eq{/PlateIndex xd exit}if pop}for PlateIndex
0 ge{color PlateIndex get dup 0 gt{SeparateInColor{num 1 sub{0}repeat num
PlateIndex roll ColorSpace create_devicen_color/convert_to_cmyk dexec true}{1
exch sub create_grayscale_color true}ifelse 4 2 roll pop pop}{pop}ifelse}if}bd
/get_ink_as_spot{dup/indexInk xd dup color exch get exch names exch get[1.0 num
1 sub{0}repeat num indexInk roll ColorSpace 3 get exec]ColorSpace 2 get
create_spot_color}bd currentdict end def/create_devicen_colorspace{
/numColorants xd load/alternativeSpace xd bind/tintTransform exch def/names xd
[/DeviceN names alternativeSpace/tintTransform load]}bd/create_devicen_color{
DevicenColorSpec 9 dict copy begin dup type/arraytype ne{
create_devicen_colorspace}if/ColorSpace xd/num ColorSpace 1 get length def
/names ColorSpace 1 get def num array astore/color xd currentdict end}bd
/RegistrationColorSpec ColorSpec 9 dict copy begin/num 1 def/ColorModel
/Registration def/ColorSpace{[/Separation/All DocCmykSpace{dup dup dup}]}def
/names[/All]def/separate_color{SeparateInColor{convert_to_cmyk true}{color
aload pop 1 exch sub create_grayscale_color true}ifelse}bd/convert_to_cmyk{
color aload pop dup dup dup create_cmyk_color}bd currentdict end def
/create_registration_color{RegistrationColorSpec 9 dict copy begin 1 array
astore/color xd currentdict end}bd/LabColorSpec ColorSpec 9 dict copy begin
/names{/L/a/b}def/num 3 def/ColorModel/Lab def/ColorSpace DocLabSpace def
/separate_color{convert_to_cmyk/separate_color dexec}bd/convert_to_cmyk{0 0 0 0
create_cmyk_color}bd currentdict end def/create_lab_color{LabColorSpec 9 dict
copy begin 3 array astore/color exch def/ColorSpace DocLabSpace def currentdict
end}bd/set_solid_fill{/FillColor xd/$fil 0 def}bd/set_outline{/OutlineColor xd
}bd/CheckLevelCompatibility{/DocPsLevel where{pop DocPsLevel get_ps_level gt{
@np/Courier findfont 12 scalefont setfont 72 144 m
(The PostScript level of Corel document is higher than the PostScript)show 72
132 m(level of this device. Change the PS Level in the Corel application)show
72 120 m(by selecting the PostScript tab in the print dialog, and selecting)
show 72 108 m(document level from the Compatibility drop down list.)show flush
spg quit}if}if}bd/@BeginSysCorelDict{systemdict/Corel30Dict known{systemdict
/Corel30Dict get exec}if systemdict/CorelLexDict known{1 systemdict
/CorelLexDict get exec}if}bd/@EndSysCorelDict{systemdict/Corel30Dict known
{end}if/EndCorelLexDict where{pop EndCorelLexDict}if}bd/AutoFlatness where{pop
AutoFlatness{/@ifl{dup currentflat exch sub 10 gt{
([Error: PathTooComplex; OffendingCommand: AnyPaintingOperator]\n)print flush
@np exit}{currentflat 2 add setflat}ifelse}bd/@fill/fill ld/fill{currentflat{
{@fill}stopped{@ifl}{exit}ifelse}bind loop setflat}bd/@eofill/eofill ld/eofill
{currentflat{{@eofill}stopped{@ifl}{exit}ifelse}bind loop setflat}bd/@clip
/clip ld/clip{currentflat{{@clip}stopped{@ifl}{exit}ifelse}bind loop setflat}
bd/@eoclip/eoclip ld/eoclip{currentflat{{@eoclip}stopped{@ifl}{exit}ifelse}
bind loop setflat}bd/@stroke/stroke ld/stroke{currentflat{{@stroke}stopped
{@ifl}{exit}ifelse}bind loop setflat}bd}if}if/@ssa Level2{{true
setstrokeadjust}}{{}}ifelse bd/d/setdash ld/j/setlinejoin ld/J/setlinecap ld/M
/setmiterlimit ld/w/setlinewidth ld/O{/FillOverprint xd}bd/R{/OutlineOverprint
xd}bd/W/eoclip ld/c/curveto ld/C/c ld/l/lineto ld/L/l ld/rl/rlineto ld/m
/moveto ld/n/newpath ld/N/newpath ld/P{11 rp}bd/u{}bd/U{}bd/A{pop}bd/q/@gs ld
/Q/@gr ld/&{}bd/@j{@sv @np}bd/@J{@rs}bd/g{1 exch sub 0 0 0 4 roll 3
create_cmyk_color set_solid_fill/$fil 0 def}bd/G{1 sub neg 0 0 0 4 -1 roll
create_cmyk_color set_outline}bd/i{dup 0 ne{setflat}{pop}ifelse}bd/v{4 -2 roll
2 copy 6 -2 roll c}bd/V/v ld/y{2 copy c}bd/Y/y ld/@w{matrix rotate/$ptm xd
matrix scale $ptm dup concatmatrix/$ptm xd 1 eq{$ptm exch dup concatmatrix
/$ptm xd}if/patternScallingMatrix where{pop patternScallingMatrix $ptm matrix
concatmatrix/$ptm xd}if 1 w}bd/@g{1 eq dup/$sdf xd{/$scp xd/$sca xd/$scf xd}if
}bd/@G{1 eq dup/$SDF xd{/$SCP xd/$SCA xd/$SCF xd}if}bd/@D{2 index 0 eq{$dsf 3 1
roll 4 -1 roll pop}if 3 copy exch $Scra add exch load SetScr/$dsp xd/$dsa xd
/$dsf xd}bd/$ngx{$SDF{$SCF SeparationMode/Composite eq{$SCA}{$dsa}ifelse $SCP
@ss}if}bd/min{2 copy le{pop}{exch pop}ifelse}bd/max{2 copy ge{pop}{exch pop}
ifelse}bd/in_range{3 -1 roll min max}bd/InRange/in_range load bd/@sqr{dup 0 rl
dup 0 exch rl neg 0 rl @cp}bd/currentscale{1 0 dtransform matrix defaultmatrix
idtransform dup mul exch dup mul add sqrt 0 1 dtransform matrix defaultmatrix
idtransform dup mul exch dup mul add sqrt}bd/@unscale{}bd/wDstChck{2 1 roll dup
3 -1 roll eq{1 add}if}bd/@dot{dup mul exch dup mul add 1 exch sub}bd/@lin{exch
pop abs 1 exch sub}bd/cmyk2rgb{3{dup 5 -1 roll add 1 exch sub dup 0 lt{pop 0}
if exch}repeat pop}bd/rgb2cmyk{3{1 exch sub 3 1 roll}repeat 3 copy min min 3{
dup 5 -1 roll sub neg exch}repeat}bd/rgb2g{2 index .299 mul 2 index .587 mul
add 1 index .114 mul add 4 1 roll pop pop pop}bd/WaldoColor_5 where{pop}{
/CorelImage systemdict/image get def/CorelSetGray systemdict/setgray get def
/CorelGetGray systemdict/currentgray get def/CorelSetTransfer systemdict
/settransfer get def/CorelGetTransfer systemdict/currenttransfer get def
/SetRgb/setrgbcolor ld/GetRgb/currentrgbcolor ld/SetGry/setgray ld/GetGry
/currentgray ld/SetRgb2 systemdict/setrgbcolor get def/GetRgb2 systemdict
/currentrgbcolor get def/SetHsb systemdict/sethsbcolor get def/GetHsb
systemdict/currenthsbcolor get def/rgb2hsb{SetRgb2 GetHsb}bd/hsb2rgb{3 -1 roll
dup floor sub 3 1 roll SetHsb GetRgb2}bd/setcmykcolor where{pop/LumSepsDict
where{pop/SetCmyk_5{LumSepsDict/setcmykcolor get exec}def}{/AldusSepsDict where
{pop/SetCmyk_5{AldusSepsDict/setcmykcolor get exec}def}{/SetCmyk_5
/setcmykcolor ld}ifelse}ifelse}{/SetCmyk_5{cmyk2rgb SetRgb}bd}ifelse
/currentcmykcolor where{pop/GetCmyk/currentcmykcolor ld}{/GetCmyk{GetRgb
rgb2cmyk}bd}ifelse/setoverprint where{pop}{/setoverprint{/CurrentOverprint xd}
bd}ifelse/currentoverprint where{pop}{/currentoverprint{CurrentOverprint}bd}
ifelse/colorimage where{pop/ColorImage{colorimage}def}{/ColorImage{/ncolors xd
/$multi xd $multi true eq{ncolors 3 eq{/daqB xd/daqG xd/daqR xd pop pop exch
pop abs{daqR pop daqG pop daqB pop}repeat}{/daqK xd/daqY xd/daqM xd/daqC xd pop
pop exch pop abs{daqC pop daqM pop daqY pop daqK pop}repeat}ifelse}{/dataaq xd
{dataaq ncolors dup 3 eq{/$dat xd 0 1 $dat length 3 div 1 sub{dup 3 mul $dat 1
index get 255 div $dat 2 index 1 add get 255 div $dat 3 index 2 add get 255 div
rgb2g 255 mul cvi exch pop $dat 3 1 roll put}for $dat 0 $dat length 3 idiv
getinterval pop}{4 eq{/$dat xd 0 1 $dat length 4 div 1 sub{dup 4 mul $dat 1
index get 255 div $dat 2 index 1 add get 255 div $dat 3 index 2 add get 255 div
$dat 4 index 3 add get 255 div cmyk2rgb rgb2g 255 mul cvi exch pop $dat 3 1
roll put}for $dat 0 $dat length ncolors idiv getinterval}if}ifelse}image}
ifelse}bd}ifelse/setcmykcolor{create_cmyk_color/separate_set_color dexec
/$ffpnt xd}bd/currentcmykcolor{GetCmyk}bd/sethsbcolor{hsb2rgb setrgbcolor}bd
/currenthsbcolor{currentrgbcolor rgb2hsb}bd/setgray{dup dup setrgbcolor}bd
/currentgray{currentrgbcolor rgb2g}bd/InsideDCS false def/IMAGE/image ld/image
{InsideDCS{IMAGE}{/EPSDict where{pop SeparationMode/Composite eq{IMAGE}{dup
type/dicttype eq{dup/ImageType get 1 ne{IMAGE}{dup dup/BitsPerComponent get 8
eq exch/BitsPerComponent get 1 eq or currentcolorspace 0 get DocGrayScaleSpace
eq and{SeparationPlateName(Black)eq{IMAGE}{dup/DataSource get/TCC xd/Height get
abs{TCC pop}repeat}ifelse}{IMAGE}ifelse}ifelse}{2 index 1 ne{
SeparationPlateName(Black)eq{IMAGE}{/TCC xd pop pop exch pop abs{TCC pop}
repeat}ifelse}{IMAGE}ifelse}ifelse}ifelse}{IMAGE}ifelse}ifelse}bd}ifelse/$fm 0
def/wfill{1 $fm eq{fill}{eofill}ifelse}bd/@Pf{@sv SeparationMode/Composite eq{
true}{$Psc 0 ne or $ink_5 3 eq or}ifelse{0 J 0 j[]0 d FillOverprint
safe_setoverprint FillColor/separate_set_color dexec pop $ctm setmatrix 72 1000
div dup matrix scale dup concat dup Bburx exch Bbury exch itransform ceiling
cvi/Bbury xd ceiling cvi/Bburx xd Bbllx exch Bblly exch itransform floor cvi
/Bblly xd floor cvi/Bbllx xd $Prm aload pop $Psn load exec}{1 SetGry wfill}
ifelse @rs @np}bd/F{matrix currentmatrix $sdf{$scf $sca $scp @ss}if
FillOverprint safe_setoverprint $fil 1 eq{CorelPtrnDoFill}{$fil 2 eq
{gradient_fill}{$fil 3 eq{@Pf}{get_simulate_devicen FillColor/ColorModel get
/DeviceN eq and{0 1 FillColor/num get 1 sub{dup 0 gt{true safe_setoverprint}if
FillColor/get_ink_as_spot dexec/set_color dexec @gs wfill @gr}for}{FillColor
/separate_set_color dexec{wfill}{@np}ifelse}ifelse}ifelse}ifelse}ifelse $sdf{
$dsf $dsa $dsp @ss}if setmatrix}bd/f{@cp F}bd/S{matrix currentmatrix $ctm
setmatrix $SDF{$SCF $SCA $SCP @ss}if OutlineOverprint safe_setoverprint
get_simulate_devicen OutlineColor/ColorModel get/DeviceN eq and{0 1
OutlineColor/num get 1 sub{dup 0 gt{true safe_setoverprint}if OutlineColor
/get_ink_as_spot dexec/set_color dexec matrix currentmatrix $ptm concat @gs
stroke @gr setmatrix}for}{OutlineColor/separate_set_color dexec{matrix
currentmatrix $ptm concat stroke setmatrix}{@np}ifelse}ifelse $SDF{$dsf $dsa
$dsp @ss}if setmatrix}bd/s{@cp S}bd/B{@gs F @gr S}bd/b{@cp B}bd/_E{5 array
astore exch cvlit xd}bd/@cc{currentfile $dat readhexstring pop}bd/@sm{/$ctm
$ctm currentmatrix def}bd/@E{/Bbury xd/Bburx xd/Bblly xd/Bbllx xd}bd/@c{@cp}bd
/@P{/$fil 3 def/$Psn xd/$Psc xd array astore/$Prm xd}bd/tcc{@cc}def/@B{@gs S
@gr F}bd/@b{@cp @B}bd/init_separation{/SeparationMode where{pop}{
/SeparationMode/Composite def}ifelse/SeparationPlateName where{pop}{
/SeparationPlateName null def}ifelse/SeparateInColor where{pop}{
/SeparateInColor false def}ifelse SeparationMode/Composite eq{
/SeparationPlateIndex -1 def}{/SeparationPlateIndex 4 def}ifelse/CmykPlates[
/Cyan/Magenta/Yellow/Black]def 0 1 3{dup CmykPlates exch get
SeparationPlateName eq{/SeparationPlateIndex xd exit}if pop}for
/CurrentInkName_5 SeparationPlateName def/$ink_5 SeparationPlateIndex def
SeparationMode/OnHost eq{/SepMode_5 2 def}{/SepMode_5 0 def}ifelse/SepsColor
SeparateInColor def}bd/init_separation_from_legacy{EpsFile{/SepMode_5 where{
pop SepMode_5 2 eq{/SeparationMode/OnHost def}{/SeparationMode/Composite def}
ifelse}if/CurrentInkName_5 where{pop/SeparationPlateName CurrentInkName_5 def}
if/$ink_5 where{pop/SeparationPlateIndex $ink_5 def}if/SepsColor where{pop
/SeparateInColor SepsColor def}if}if}bd init_separation_from_legacy/@whi{@gs
-72000 dup m -72000 72000 l 72000 dup l 72000 -72000 l @cp 1 SetGry fill @gr}
bd/@neg{[{1 exch sub}/exec cvx currenttransfer/exec cvx]cvx settransfer @whi}
bd/deflevel 0 def/@sax{/deflevel deflevel 1 add def}bd/@eax{/deflevel deflevel
dup 0 gt{1 sub}if def deflevel 0 gt{/eax load}{eax}ifelse}bd/eax{{exec}forall}
bd/@rax{deflevel 0 eq{@rs @sv}if}bd systemdict/pdfmark known not{/pdfmark
/cleartomark ld}if/wclip{1 $fm eq{clip}{eoclip}ifelse}bd
%----------------------------------------------------------------------------
% Raster images support
/set_image_clipping{concat 3 index 3 index m 3 index 1 index l 2 copy l 1 index
3 index l 3 index 3 index l clip pop pop pop pop}bd/set_image_parameters{/$frg
xd/$bkg xd/$ury xd/$urx xd/$lly xd/$llx xd/$ncl xd/$bts xd/$hei xd/$wid xd}bd
/output_image{/@cc xd @sm @gs set_image_clipping set_image_parameters
SeparationMode/OnHost eq EpsFile and{separate_output_image}{
output_composite_image}ifelse @gr $ctm setmatrix}bd/output_composite_image{
DocPsLevel 3 eq MaskedImage true eq and{output_masked_image}{$bts 1 gt
DocPsLevel 2 eq DocPsLevel 3 eq or and{DevicenImage{output_devicen_image}{
output_color_image}ifelse}{$bts 1 eq{output_monochrome_image}{
output_color_image_level1}ifelse}ifelse}ifelse}bd/separate_output_image{@gs
$bts 1 gt{separate_output_color_image}{separate_output_monochrome_image}ifelse
@gr not{currentoverprint not{1.0 SetGry wfill}if}if @np}bd/set_decode_filter{
ImageCompression/JPEG eq{/DCTDecode filter}{ImageCompression/RLE eq{
/RunLengthDecode filter}{ImageCompression/LZW eq{/LZWDecode filter}if}ifelse}
ifelse}bd/create_datasource{currentfile DevicenImage not get_ps_level 2 gt or{
/ASCII85Decode filter}if set_decode_filter}bd/set_image_strip_parameters{
/stripRows xd/numPlanes xd/stripBuffer $wid $bts mul $ncl mul stripRows mul 8
div ceiling cvi dup 65535 gt{pop $ncl $bts mul}if string def DevicenImage{
/readstrip{DataSource stripBuffer readhexstring pop}bd}{/readstrip{DataSource
stripBuffer readstring pop}bd}ifelse/stripHeight $lly $ury sub stripRows mul
$hei div def/stripOutBuffer $wid stripRows mul numPlanes mul 65535 min string
def/strip_img_dict 8 dict def strip_img_dict begin/ImageType 1 def/Width $wid
def/Height stripRows def/BitsPerComponent $bts def/Decode[numPlanes{0 1}
repeat]def/ImageMatrix[$wid 0 0 stripRows neg 0 $hei 0 gt{stripRows}{0}ifelse]
def/DataSource stripOutBuffer def end}bd/separate_output_color_image{
/plateContent false def $ncl 1 eq{SeparationPlateName/Black eq{
output_color_image/plateContent true def}if}{@np/DataSource create_datasource
def SeparateInColor{DocCmykSpace setcolorspace/numPlanes 4 def}{
DocGrayScaleSpace setcolorspace/numPlanes 1 def}ifelse/stripRows 65535 $wid
$bts mul $ncl numPlanes max mul 8 div div floor cvi 1 max def numPlanes
stripRows set_image_strip_parameters/maxStripIndex $hei stripRows div ceiling
cvi 1 sub def 0 1 maxStripIndex{/stripIndex exch def/$t_ury $ury stripHeight
stripIndex mul add ceiling cvi def/$t_lly $t_ury stripHeight add ceiling cvi
def stripIndex maxStripIndex eq{numPlanes $hei maxStripIndex stripRows mul sub
set_image_strip_parameters/$t_lly $t_ury stripHeight add ceiling cvi def}if
readstrip pop @gs 0 1 $wid stripRows mul 1 sub{/pixelIndex xd stripBuffer
pixelIndex $ncl mul $ncl getinterval{255 div}forall DevicenImage{
ImageDevicenSpace create_devicen_color}{$ncl 3 eq{create_rgb_color}{
create_cmyk_color}ifelse}ifelse/separate_color dexec{/plateContent true def
begin color aload pop end}{pop numPlanes[numPlanes 1 eq{1}{0}ifelse]cvx repeat
}ifelse numPlanes array astore/pixelSepIndex pixelIndex numPlanes mul def
/posColorant 0 def{stripOutBuffer pixelSepIndex posColorant add 3 -1 roll 255
mul cvi put/posColorant posColorant 1 add def}forall}for $llx $t_lly Tl $urx
$llx sub $t_ury $t_lly sub scale strip_img_dict image @gr}for $SDF{$dsf $dsa
$dsp @ss}if}ifelse plateContent}bd/separate_output_monochrome_image{
SeparationPlateName/Black eq{output_monochrome_image true}{false}ifelse}bd
/output_monochrome_image{$frg 1 eq{OutlineOverprint safe_setoverprint
ImageForegroundColor/separate_set_color dexec{1}{0}ifelse/$frg xd}{/$frg false
def}ifelse $bkg 1 eq{@gs $ctm setmatrix F @gr}if @np/$dat $wid $bts mul 8 div
ceiling cvi 65535 min string def $bkg $frg add 1 ge{$SDF{$SCF $SCA $SCP @ss}if
$llx $lly Tl $urx $llx sub $ury $lly sub scale $bkg 1 eq{FillColor
/separate_set_color dexec pop}if $wid $hei abs $bts 1 eq{$bkg 1 ge}{$bts 1 ge}
ifelse[$wid 0 0 $hei neg 0 $hei 0 gt{$hei}{0}ifelse]/tcc load $bts 1 eq
{imagemask}{image}ifelse $SDF{$dsf $dsa $dsp @ss}if}{$hei abs{tcc pop}repeat}
ifelse}bd/output_color_image_level1{@np $ngx $llx $lly Tl $urx $llx sub $ury
$lly sub scale $wid $hei abs $bts[$wid 0 0 $hei neg 0 $hei 0 gt{$hei}{0}ifelse
]/$dat $wid $bts mul $ncl mul 8 div ceiling cvi 65535 min string def $msimage
false eq $ncl 1 eq or{/@cc load false $ncl ColorImage}{$wid $bts mul 8 div
ceiling cvi $ncl 3 eq{dup dup/$dat1 exch string def/$dat2 exch string def
/$dat3 exch string def/@cc1 load/@cc2 load/@cc3 load}{dup dup dup/$dat1 exch
string def/$dat2 exch string def/$dat3 exch string def/$dat4 exch string def
/@cc1 load/@cc2 load/@cc3 load/@cc4 load}ifelse true $ncl ColorImage}ifelse
$SDF{$dsf $dsa $dsp @ss}if}bd/@cc1{currentfile $dat1 readhexstring pop}bd/@cc2
{currentfile $dat2 readhexstring pop}bd/@cc3{currentfile $dat3 readhexstring
pop}bd/@cc4{currentfile $dat4 readhexstring pop}bd/$msimage false def
/ImageCompression/None def/MaskedImage false def/DevicenImage false def
/output_color_image{@np $ncl 1 eq{DocGrayScaleSpace}{$ncl 3 eq{DocRgbSpace}
{DocCmykSpace}ifelse}ifelse setcolorspace/$dat $wid $bts mul $ncl mul 8 div
ceiling cvi 65535 min string def $ngx $llx $lly Tl $urx $llx sub $ury $lly sub
scale 8 dict begin/ImageType 1 def/Width $wid def/Height $hei abs def
/BitsPerComponent $bts def/Decode[$ncl{0 1}repeat]def/ImageMatrix[$wid 0 0 $hei
neg 0 $hei 0 gt{$hei}{0}ifelse]def/DataSource create_datasource def currentdict
end image $SDF{$dsf $dsa $dsp @ss}if}bd/simulate_devicen_image{@np $ngx
/colorantNames ImageDevicenSpace 1 get def/altColorSpace ImageDevicenSpace 2
get def/tintProc ImageDevicenSpace 3 get def/DataSource create_datasource def
/stripRows 65535 $wid $bts mul $ncl mul 8 div div floor cvi 1 max def 1
stripRows set_image_strip_parameters/maxStripIndex $hei stripRows div ceiling
cvi 1 sub def 0 1 maxStripIndex{/stripIndex exch def/$t_ury $ury stripHeight
stripIndex mul add ceiling cvi def/$t_lly $t_ury stripHeight add ceiling cvi
def stripIndex maxStripIndex eq{1 $hei maxStripIndex stripRows mul sub
set_image_strip_parameters/$t_lly $t_ury stripHeight add ceiling cvi def}if
readstrip pop @gs $llx $t_lly Tl $urx $llx sub $t_ury $t_lly sub scale 0 1 $ncl
1 sub{@gs/inkIndex exch def 0 1 stripOutBuffer length 1 sub{dup $ncl mul
inkIndex add stripBuffer exch get stripOutBuffer 3 1 roll put}for[/Separation
colorantNames inkIndex get altColorSpace{$ncl 1 sub{0}repeat $ncl inkIndex roll
tintProc}]setcolorspace inkIndex 0 gt{true setoverprint}if strip_img_dict image
@gr}for @gr}for $SDF{$dsf $dsa $dsp @ss}if}bd/output_devicen_image_alt{@np $ngx
/colorantNames ImageDevicenSpace 1 get def/altColorSpace ImageDevicenSpace 2
get def/tintProc ImageDevicenSpace 3 get def altColorSpace DocCmykSpace eq{
/numAltChannels 4 def}{altColorSpace DocRgbSpace eq{/numAltChannels 3 def}{
/numAltChannels 1 def}ifelse}ifelse/DataSource create_datasource def
altColorSpace setcolorspace/stripRows 65535 $wid $bts mul $ncl numAltChannels
max mul 8 div div floor cvi 1 max def numAltChannels stripRows
set_image_strip_parameters/maxStripIndex $hei stripRows div ceiling cvi 1 sub
def 0 1 maxStripIndex{/stripIndex exch def/$t_ury $ury stripHeight stripIndex
mul add ceiling cvi def/$t_lly $t_ury stripHeight add ceiling cvi def
stripIndex maxStripIndex eq{numAltChannels $hei maxStripIndex stripRows mul sub
set_image_strip_parameters/$t_lly $t_ury stripHeight add ceiling cvi def}if
readstrip pop @gs 0 1 stripBuffer length $ncl div cvi 1 sub{/pixelIndex xd
stripBuffer pixelIndex $ncl mul $ncl getinterval{255 div}forall tintProc exec
numAltChannels array astore/pixelAltIndex pixelIndex numAltChannels mul def
/posColorant 0 def{stripOutBuffer pixelAltIndex posColorant add 3 -1 roll 255
mul cvi put/posColorant posColorant 1 add def}forall}for $llx $t_lly Tl $urx
$llx sub $t_ury $t_lly sub scale strip_img_dict image @gr}for $SDF{$dsf $dsa
$dsp @ss}if}bd/output_devicen_image_native{@np $ngx ImageDevicenSpace
setcolorspace/scanline $wid $bts mul $ncl mul 8 div ceiling cvi 65535 min
string def/readscanline{currentfile scanline readhexstring pop}bind def $llx
$lly Tl $urx $llx sub $ury $lly sub scale 8 dict begin/ImageType 1 def/Width
$wid def/Height $hei abs def/BitsPerComponent $bts def/Decode[$ncl{0 1}repeat]
def/ImageMatrix[$wid 0 0 $hei neg 0 $hei 0 gt{$hei}{0}ifelse]def/DataSource{
readscanline}def currentdict end image $SDF{$dsf $dsa $dsp @ss}if}bd
/output_devicen_image{Level3{output_devicen_image_native}{get_simulate_devicen
{simulate_devicen_image}{output_devicen_image_alt}ifelse}ifelse}bd
/output_masked_image{/$dat $wid $bts mul $ncl mul 8 div ceiling cvi 65535 min
string def $ngx DevicenImage{ImageDevicenSpace}{$ncl 1 eq{DocGrayScaleSpace}{
$ncl 3 eq{DocRgbSpace}{DocCmykSpace}ifelse}ifelse}ifelse setcolorspace $llx
$lly Tl $urx $llx sub $ury $lly sub scale/ImageDataDict 8 dict def
ImageDataDict begin/ImageType 1 def/Width $wid def/Height $hei abs def
/BitsPerComponent $bts def/Decode[$ncl{0 1}repeat]def/ImageMatrix[$wid 0 0 $hei
neg 0 $hei 0 gt{$hei}{0}ifelse]def/DataSource create_datasource def end
/MaskedImageDict 7 dict def MaskedImageDict begin/ImageType 3 def
/InterleaveType 3 def/MaskDict ImageMaskDict def/DataDict ImageDataDict def end
MaskedImageDict image $SDF{$dsf $dsa $dsp @ss}if}bd/set_image_mask{/$mbts xd
/$mhei xd/$mwid xd/ImageMaskDict 8 dict def ImageMaskDict begin/ImageType 1 def
/Width $mwid def/Height $mhei abs def/BitsPerComponent $mbts def/DataSource
maskstream def/ImageMatrix[$mwid 0 0 $mhei neg 0 $mhei 0 gt{$mhei}{0}ifelse]
def/Decode[1 0]def end}bd/@daq{dup type/arraytype eq{aload pop}if}bd
/skip_image_body{8 rp/$ury xd/$urx xd/$lly xd/$llx xd/$ncl xd/$bts xd/$hei xd
/$wid xd DevicenImage not ImageCompression/JPEG eq or ImageCompression/RLE eq
or ImageCompression/LZW eq or{create_datasource flushfile}{/scanline $wid $bts
mul $ncl mul 8 div ceiling cvi dup 65535 gt{pop $bts $ncl mul}if string def
/upperBound $wid $hei mul $bts mul $ncl mul scanline length div cvi 1 sub def 0
1 upperBound{pop currentfile scanline readhexstring pop pop}for}ifelse}bd
/disable_raster_output{deflevel 0 eq{@rs}if/output_image load
/output_image_original exch def/output_image/skip_image_body load def
globaldict begin/bSkipDeviceNImage true def end deflevel 0 eq{@sv}if}def
/enable_raster_output{deflevel 0 eq{@rs}if/bSkipDeviceNImage where{
/bSkipDeviceNImage undef/output_image/output_image_original load def}if
deflevel 0 eq{@sv}if}def
end
%%EndResource
%%EndProlog
%%BeginSetup
wCorel16Dict begin
@BeginSysCorelDict
/$dcm matrix currentmatrix def
@ssa
1.00 setflat
/$fst 128 def
%%EndSetup

%%Page: 1 1
%%ViewingOrientation: 1 0 0 1
%%BoundingBox: 0 0 595 841
%LogicalPage: 1
%%BeginPageSetup
@sv
@sm
@sv
%%EndPageSetup
@rax %Note: Object
0.00000 0.00000 360.00000 15.99987 @E
/$fm 0 def
0.00000 15.99987 m
360.00000 15.99987 L
360.00000 0.00000 L
0.00000 0.00000 L
0.00000 15.99987 L
@c
N

@rax %Note: Object
101.00069 1.99984 114.00094 14.00003 @E
/$fm 0 def
 0 O 0 @g
0.9608 0.6863 0.0000  create_rgb_color set_solid_fill
109.00091 9.00000 m
105.00094 9.00000 L
105.00094 11.99991 L
104.00088 11.99991 L
104.00088 14.00003 L
103.00082 14.00003 L
103.00082 11.99991 L
102.00076 11.99991 L
102.00076 9.00000 L
101.00069 9.00000 L
101.00069 1.99984 L
114.00094 1.99984 L
114.00094 10.99984 L
109.00091 10.99984 L
109.00091 9.00000 L
@c
103.00082 10.99984 m
104.00088 10.99984 L
104.00088 10.00006 L
103.00082 10.00006 L
103.00082 10.99984 L
@c
110.00069 9.99978 m
111.00076 9.99978 L
111.00076 9.00000 L
110.00069 9.00000 L
110.00069 9.99978 L
@c
112.00082 9.99978 m
113.00088 9.99978 L
113.00088 9.00000 L
112.00082 9.00000 L
112.00082 9.99978 L
@c
110.00069 7.99994 m
111.00076 7.99994 L
111.00076 7.00016 L
110.00069 7.00016 L
110.00069 7.99994 L
@c
112.00082 7.99994 m
113.00088 7.99994 L
113.00088 7.00016 L
112.00082 7.00016 L
112.00082 7.99994 L
@c
110.00069 5.99981 m
111.00076 5.99981 L
111.00076 5.00003 L
110.00069 5.00003 L
110.00069 5.99981 L
@c
112.00082 5.99981 m
113.00088 5.99981 L
113.00088 5.00003 L
112.00082 5.00003 L
112.00082 5.99981 L
@c
110.00069 3.99997 m
111.00076 3.99997 L
111.00076 3.00019 L
110.00069 3.00019 L
110.00069 3.99997 L
@c
112.00082 3.99997 m
113.00088 3.99997 L
113.00088 3.00019 L
112.00082 3.00019 L
112.00082 3.99997 L
@c
102.00076 3.99997 m
103.00082 3.99997 L
103.00082 3.00019 L
102.00076 3.00019 L
102.00076 3.99997 L
@c
104.00088 3.99997 m
105.00094 3.99997 L
105.00094 3.00019 L
104.00088 3.00019 L
104.00088 3.99997 L
@c
102.00076 5.99981 m
103.00082 5.99981 L
103.00082 5.00003 L
102.00076 5.00003 L
102.00076 5.99981 L
@c
104.00088 5.99981 m
105.00094 5.99981 L
105.00094 5.00003 L
104.00088 5.00003 L
104.00088 5.99981 L
@c
102.00076 7.99994 m
103.00082 7.99994 L
103.00082 7.00016 L
102.00076 7.00016 L
102.00076 7.99994 L
@c
104.00088 7.99994 m
105.00094 7.99994 L
105.00094 7.00016 L
104.00088 7.00016 L
104.00088 7.99994 L
@c
107.00079 3.99997 m
108.00085 3.99997 L
108.00085 3.00019 L
107.00079 3.00019 L
107.00079 3.99997 L
@c
107.00079 5.99981 m
108.00085 5.99981 L
108.00085 5.00003 L
107.00079 5.00003 L
107.00079 5.99981 L
@c
107.00079 7.99994 m
108.00085 7.99994 L
108.00085 7.00016 L
107.00079 7.00016 L
107.00079 7.99994 L
@c
F

@rax %Note: Object
21.00019 1.99984 32.00003 13.03313 @E
/$fm 0 def
 0 O 0 @g
1.0000 0.7843 0.0000  create_rgb_color set_solid_fill
24.42643 9.39231 m
24.42643 11.07609 25.46702 12.53934 27.00000 12.99997 C
23.73506 13.35572 21.00019 10.79745 21.00019 7.49906 C
21.00019 4.46202 23.46236 1.99984 26.50054 1.99984 C
29.08743 1.99984 31.41241 3.59433 32.00003 5.99981 C
29.00013 3.99997 24.42643 5.07090 24.42643 9.39231 C
@c
F

@rax %Note: Object
320.99868 2.99991 335.99849 12.99940 @E
/$fm 0 def
 0 O 0 @g
0.9804 0.5882 0.4902  create_rgb_color set_solid_fill
330.99846 8.49997 m
330.99846 8.22387 330.77452 7.99994 330.49843 7.99994 C
330.22233 7.99994 329.99839 8.22387 329.99839 8.49997 C
329.99839 9.49975 329.49836 9.99978 328.49830 9.99978 C
328.22220 9.99978 327.99827 10.22372 327.99827 10.49981 C
327.99827 10.77591 328.22220 10.99984 328.49830 10.99984 C
329.99839 10.99984 330.99846 9.99978 330.99846 8.49997 C
@c
334.99843 7.99965 m
333.56778 5.70302 331.18469 3.99997 328.49830 3.99997 c
325.80935 3.99997 323.43165 5.70302 321.99846 7.99965 C
323.43165 10.29628 325.80935 11.99934 328.49830 11.99934 c
331.18469 11.99934 333.56778 10.29628 334.99843 7.99965 C
@c
320.99868 7.99965 m
320.99868 8.21735 321.06359 8.41691 321.16592 8.59833 C
322.70683 11.22661 325.52816 12.99940 328.50227 12.99940 c
331.47128 12.99940 334.29033 11.22661 335.83096 8.59833 C
335.93102 8.41691 335.99849 8.21735 335.99849 7.99965 c
335.99849 7.78195 335.93102 7.58239 335.83096 7.39843 C
334.29033 4.77269 331.47128 2.99991 328.50227 2.99991 c
325.52816 2.99991 322.70683 4.76504 321.16592 7.39843 C
321.06359 7.58239 320.99868 7.78195 320.99868 7.99965 c
@c
328.49830 11.99991 m
330.43124 11.99991 331.99824 10.43291 331.99824 8.49997 c
331.99824 6.56702 330.43124 5.00003 328.49830 5.00003 c
326.56535 5.00003 324.99836 6.56702 324.99836 8.49997 c
324.99836 10.43291 326.56535 11.99991 328.49830 11.99991 c
@c
F

@rax %Note: Object
81.00085 1.99984 94.00082 12.99997 @E
/$fm 0 def
 0 O 0 @g
0.7255 0.5294 0.3922  create_rgb_color set_solid_fill
85.00082 3.99997 m
85.00082 6.99987 L
87.00066 6.99987 L
87.00066 3.99997 L
85.00082 3.99997 L
@c
92.00069 6.99987 m
94.00082 6.99987 L
92.00069 9.00000 L
92.00069 12.99997 L
91.00091 12.99997 L
91.00091 9.99978 L
88.00072 12.99997 L
87.00094 12.99997 L
81.00085 6.99987 L
83.00069 6.99987 L
83.00098 1.99984 L
86.00088 1.99984 89.00107 1.99984 92.00098 1.99984 C
92.00069 6.99987 L
@c
88.00101 3.99997 m
88.00101 6.99987 L
90.00085 6.99987 L
90.00085 3.99997 L
88.00101 3.99997 L
@c
F

@rax %Note: Object
41.00031 1.99984 49.96800 14.00003 @E
/$fm 1 def
 0 O 0 @g
0.7373 0.5686 0.9020  create_rgb_color set_solid_fill
44.00050 3.99997 m
47.00041 3.99997 L
47.00041 1.99984 L
44.00050 1.99984 L
44.00050 3.99997 L
@c
47.00041 5.00003 m
44.00050 5.00003 L
44.00050 5.99981 43.80718 7.44520 46.00035 9.00000 c
48.74769 10.94769 44.40841 12.80750 44.00050 10.00006 C
41.00031 10.00006 L
41.24183 12.58526 42.99364 14.00003 45.50031 14.00003 c
49.21030 14.00003 52.09002 10.68180 47.89644 7.76835 c
46.92699 7.09483 47.00041 6.67559 47.00041 5.00003 C
@c
F

@rax %Note: Object
123.00094 5.00117 134.00107 12.99883 @E
/$fm 0 def
 0 O 0 @g
1.0000 0.6275 0.5098  create_rgb_color set_solid_fill
134.00107 12.49937 m
134.00107 12.77235 133.78450 12.99883 133.52343 12.99883 c
133.19518 12.99883 131.93178 11.93698 130.75795 11.93698 c
130.52806 11.93698 130.30923 11.97638 130.10145 12.08608 C
129.06113 12.59263 128.14157 12.99883 126.96973 12.99883 c
125.90702 12.99883 124.76438 12.56230 123.82243 12.06283 C
123.62797 11.96022 123.41169 11.85052 123.23310 11.72693 C
123.09024 11.61723 123.00094 11.48428 123.00094 11.29748 c
123.00094 5.50035 l
123.00094 5.22737 123.21751 5.00117 123.47858 5.00117 c
123.56787 5.00117 123.64384 5.02441 123.72661 5.07118 C
124.75757 5.64973 126.00567 6.21014 127.19310 6.21014 c
128.82217 6.21014 129.48094 5.11767 130.85603 5.11767 c
131.83370 5.11767 132.76913 5.54712 133.61244 6.02334 C
133.82929 6.14013 134.00107 6.24983 134.00107 6.53896 c
134.00107 12.49937 l
@c
F

@rax %Note: Object
121.00110 1.99984 122.00088 12.99997 @E
/$fm 0 def
 0 O 0 @g
0.7020 0.7020 0.7020  create_rgb_color set_solid_fill
121.00110 12.99997 m
122.00088 12.99997 L
122.00088 1.99984 L
121.00110 1.99984 L
121.00110 12.99997 L
@c
F

@rax %Note: Object
140.95077 2.00239 152.00022 14.00003 @E
/$fm 0 def
 0 O 0 @g
0.3529 0.7059 1.0000  create_rgb_color set_solid_fill
151.00016 11.30343 m
145.00517 9.73247 L
145.00517 9.73247 145.00035 4.56520 145.00035 3.86561 c
145.00035 3.31824 144.21997 2.43468 143.33046 2.14243 C
142.28192 1.79745 141.24019 2.11209 141.00009 2.85222 c
140.76000 3.59206 141.42132 4.47250 142.48035 4.81861 C
142.97074 4.97849 143.59039 4.99068 144.00028 4.87899 C
144.00028 11.69575 l
144.00028 11.83805 144.11339 11.99906 144.24520 12.03335 c
151.76013 13.98926 L
151.89024 14.03008 152.00022 13.94901 152.00022 13.80699 C
152.00022 13.80699 152.00022 5.65795 152.00022 4.87729 c
152.00022 4.09720 151.22069 3.43871 150.33061 3.14589 c
149.28094 2.80063 148.24148 3.12661 148.00025 3.86561 c
147.76186 4.59553 148.41836 5.48532 149.48050 5.83200 c
149.96778 5.99102 150.58998 5.99386 151.00016 5.89238 C
151.00016 11.30343 L
@c
F

@rax %Note: Object
60.99987 2.00013 73.00091 14.00003 @E
/$fm 0 def
 0 O 0 @g
0.4706 0.7843 0.2745  create_rgb_color set_solid_fill
60.99987 3.99997 m
61.00044 2.00013 L
63.01219 3.98154 L
64.00318 4.52098 L
65.31420 4.54054 73.00091 1.20557 73.00091 14.00003 C
68.10860 12.59348 66.10507 12.88772 64.19339 10.47487 C
62.58189 8.45433 62.95238 6.48283 63.16243 5.29597 C
60.99987 3.99997 L
@c
68.00003 8.49997 m
66.65868 7.79357 65.52964 6.62683 65.00013 6.00009 c
64.13017 4.97112 63.99609 5.49354 64.66677 7.00441 C
66.09033 9.33647 70.01745 10.05165 70.99994 11.99991 C
71.77465 13.13972 72.29225 12.66123 72.00000 11.99991 C
70.86898 9.72425 69.64328 9.36283 68.00003 8.49997 C
@c
F

@rax %Note: Object
220.99890 1.99984 230.99896 14.00003 @E
/$fm 0 def
 0 O 0 @g
0.5020 0.5020 0.5020  create_rgb_color set_solid_fill
220.99890 11.99991 m
220.99890 2.99991 L
221.99896 1.99984 L
229.00507 1.99984 L
229.00507 12.00586 L
222.49899 11.99991 L
223.49877 12.99997 L
230.00400 13.00479 L
230.00400 3.00104 L
230.99896 4.00195 L
230.99896 13.99975 L
222.99902 14.00003 L
220.99890 11.99991 L
@c
227.99877 2.99991 m
221.99896 2.99991 L
221.99896 10.99984 L
227.99877 10.99984 L
227.99877 2.99991 L
@c
F

@rax %Note: Object
160.99909 1.99984 172.99899 13.99691 @E
/$fm 0 def
 0 O 0 @g
1.0000 0.8235 0.0000  create_rgb_color set_solid_fill
166.99890 13.99691 m
163.68520 13.99691 160.99909 11.31194 160.99909 7.99909 C
160.99909 4.68510 163.68520 1.99984 166.99890 1.99984 C
170.31288 1.99984 172.99899 4.68510 172.99899 7.99909 C
172.99899 11.31194 170.31288 13.99691 166.99890 13.99691 C
@c
F

@rax %Note: Object
163.99928 9.00000 169.99909 10.99984 @E
/$fm 0 def
 0 O 0 @g
0.5686 0.3529 0.0980  create_rgb_color set_solid_fill
164.99906 10.99956 m
165.55124 10.99956 165.99883 10.55197 165.99883 9.99978 c
165.99883 9.44759 165.55124 9.00000 164.99906 9.00000 c
164.44687 9.00000 163.99928 9.44759 163.99928 9.99978 c
163.99928 10.55197 164.44687 10.99956 164.99906 10.99956 c
@c
168.99902 10.99984 m
169.55121 10.99984 169.99909 10.55225 169.99909 10.00006 c
169.99909 9.44787 169.55121 9.00028 168.99902 9.00028 c
168.44683 9.00028 167.99896 9.44787 167.99896 10.00006 c
167.99896 10.55225 168.44683 10.99984 168.99902 10.99984 c
@c
F

@rax %Note: Object
164.00239 5.00003 169.98180 7.01745 @E
/$fm 0 def
 0 O 0 @g
0.5686 0.3529 0.0980  create_rgb_color set_solid_fill
169.98180 7.01745 m
169.98180 6.01739 L
169.15691 5.36202 168.08939 5.00003 166.98331 5.00003 c
165.88517 5.00003 164.82501 5.35691 164.00239 6.00378 C
164.00239 7.00384 L
164.82501 6.35698 165.88517 6.00009 166.98331 6.00009 c
168.08939 6.00009 169.15691 6.36208 169.98180 7.01745 C
@c
F

@rax %Note: Object
200.99849 7.99994 206.99830 13.99975 @E
/$fm 0 def
 0 O 0 @g
0.9216 0.7451 0.5490  create_rgb_color set_solid_fill
203.99839 13.99975 m
205.65524 13.99975 206.99830 12.65669 206.99830 10.99984 c
206.99830 9.34299 205.65524 7.99994 203.99839 7.99994 c
202.34154 7.99994 200.99849 9.34299 200.99849 10.99984 c
200.99849 12.65669 202.34154 13.99975 203.99839 13.99975 c
@c
F

@rax %Note: Object
209.99849 1.99984 215.99830 7.99965 @E
/$fm 0 def
 0 O 0 @g
0.9216 0.7451 0.5490  create_rgb_color set_solid_fill
212.99839 7.99965 m
214.65524 7.99965 215.99830 6.65660 215.99830 4.99975 c
215.99830 3.34290 214.65524 1.99984 212.99839 1.99984 c
211.34154 1.99984 209.99849 3.34290 209.99849 4.99975 c
209.99849 6.65660 211.34154 7.99965 212.99839 7.99965 c
@c
F

@rax %Note: Object
200.99849 2.99991 206.99830 7.99994 @E
/$fm 0 def
 0 O 0 @g
0.4902 0.8431 0.9216  create_rgb_color set_solid_fill
206.99830 2.99991 m
200.99849 7.99994 L
200.99849 2.99991 L
206.99830 7.99994 L
206.99830 2.99991 L
@c
F

@rax %Note: Object
209.99849 7.99994 215.99830 12.99997 @E
/$fm 0 def
 0 O 0 @g
1.0000 0.6275 0.6275  create_rgb_color set_solid_fill
215.99830 7.99994 m
209.99849 12.99997 L
209.99849 7.99994 L
215.99830 12.99997 L
215.99830 7.99994 L
@c
F

@rax %Note: Object
190.99871 1.99984 196.99909 12.99997 @E
/$fm 0 def
 0 O 0 @g
0.9216 0.7451 0.5490  create_rgb_color set_solid_fill
192.99883 12.99997 m
194.10350 12.99997 194.99896 11.65294 194.99896 10.50009 c
194.99896 9.34724 194.59672 8.76699 193.99890 8.33471 C
193.99890 7.88967 193.99890 7.44491 193.99890 6.99987 C
196.99994 6.00009 196.99994 6.00009 196.99880 1.99984 C
195.66539 1.99984 194.33225 1.99984 192.99883 1.99984 C
192.99997 5.00003 192.99997 5.50006 191.00041 6.49984 C
192.00047 6.99987 L
191.99877 8.33471 L
191.40094 8.76699 190.99871 9.34724 190.99871 10.50009 c
190.99871 11.65294 191.89417 12.99997 192.99883 12.99997 c
@c
F

@rax %Note: Object
180.99893 1.99984 191.99906 14.00003 @E
/$fm 0 def
 0 O 0 @g
0.9216 0.7451 0.5490  create_rgb_color set_solid_fill
184.99918 6.99987 m
181.00006 5.00003 181.00006 5.00003 180.99893 1.99984 C
184.66554 1.99984 188.33216 1.99984 191.99877 1.99984 C
191.99991 5.00003 191.99991 5.00003 187.99909 6.99987 C
187.99909 8.60003 L
188.60598 9.14740 188.99858 10.01877 188.99858 11.00013 c
188.99858 12.65698 187.87946 14.00003 186.49871 14.00003 c
185.11795 14.00003 183.99883 12.65698 183.99883 11.00013 c
183.99883 10.01820 184.39172 9.14655 184.99918 8.59918 C
184.99918 6.99987 L
@c
F

@rax %Note: Object
240.99817 1.99984 254.99906 14.00003 @E
/$fm 0 def
 0 O 0 @g
0.5020 0.5020 0.5020  create_rgb_color set_solid_fill
249.99846 8.99972 m
245.99849 8.99972 L
245.99849 7.99994 L
249.99846 7.99994 L
249.99846 8.99972 L
@c
249.99846 10.99984 m
245.99849 10.99984 L
245.99849 10.00006 L
249.99846 10.00006 L
249.99846 10.99984 L
@c
251.99830 12.99969 m
253.49896 12.99997 L
253.99786 12.49994 L
253.99786 11.00013 L
251.99830 11.00013 L
251.99830 12.99969 L
@c
242.49912 2.99991 m
247.99805 2.99991 L
247.99805 4.99946 L
245.99820 4.99946 243.99808 4.99946 241.99824 4.99946 C
241.99909 3.49994 L
242.49912 2.99991 L
@c
244.99814 5.99953 m
248.99811 5.99953 L
248.99811 2.99991 L
250.49906 2.99991 L
250.99909 3.49994 L
250.99795 12.99997 L
245.49902 12.99997 L
244.99899 12.49994 L
244.99814 5.99953 L
@c
244.99899 14.00003 m
251.99802 14.00003 L
251.99802 13.99975 L
253.99899 14.00003 L
254.99906 12.99997 L
254.99792 10.00006 L
251.99802 10.00006 L
251.99915 2.99991 L
250.99909 1.99984 L
241.99909 1.99984 L
240.99817 2.91373 L
240.99817 5.99953 L
243.99808 5.99953 L
243.99893 12.99997 L
244.99899 14.00003 L
@c
F

@rax %Note: Object
340.99625 1.99984 355.99720 13.00025 @E
/$fm 0 def
 0 O 0 @g
0.0000 0.0000 0.0000  create_rgb_color set_solid_fill
350.99660 9.00000 m
350.99688 9.00000 L
350.99688 10.10466 351.89235 11.00013 352.99701 11.00013 C
352.99701 10.00006 L
352.44482 10.00006 351.99694 9.55219 351.99694 9.00000 c
351.99694 8.44781 352.44482 7.99994 352.99701 7.99994 C
354.10167 7.99994 354.99713 8.89540 354.99713 10.00006 c
354.99713 11.10472 354.10167 12.00019 352.99701 12.00019 C
343.99644 12.00019 L
342.89178 12.00019 341.99631 11.10472 341.99631 10.00006 c
341.99631 8.89540 342.89178 7.99994 343.99644 7.99994 C
344.54863 7.99994 344.99650 8.44781 344.99650 9.00000 c
344.99650 9.55219 344.54863 10.00006 343.99644 10.00006 C
343.99644 11.00013 L
345.10110 11.00013 345.99657 10.10466 345.99657 9.00000 C
345.99628 8.97420 L
345.99628 1.99984 L
344.99650 1.99984 L
344.99650 7.26746 L
344.70227 7.09739 344.36069 6.99987 343.99644 6.99987 C
342.33959 6.99987 340.99625 8.34321 340.99625 10.00006 c
340.99625 11.65691 342.33959 13.00025 343.99644 13.00025 C
352.99701 13.00025 L
354.65386 13.00025 355.99720 11.65691 355.99720 10.00006 c
355.99720 8.34321 354.65386 6.99987 352.99701 6.99987 C
352.63247 6.99987 352.29061 7.09767 351.99638 7.26803 C
351.99638 1.99984 L
350.99660 1.99984 L
350.99660 9.00000 L
@c
346.99635 9.00000 m
347.99641 9.00000 L
347.99641 1.99984 L
346.99635 1.99984 L
346.99635 9.00000 L
@c
348.99647 9.00000 m
349.99625 9.00000 L
349.99625 1.99984 L
348.99647 1.99984 L
348.99647 9.00000 L
@c
F

@rax %Note: Object
260.99773 2.00013 273.99770 14.00003 @E
/$fm 0 def
 0 O 0 @g
0.4510 0.8039 1.0000  create_rgb_color set_solid_fill
261.99751 14.00003 m
272.99792 14.00003 l
273.54784 14.00003 273.99770 13.54961 273.99770 13.00025 c
273.99770 6.99987 l
273.99770 6.45165 273.54614 6.00009 272.99792 6.00009 c
267.99789 6.00009 L
263.99792 2.00013 L
263.99792 6.00009 L
261.99751 6.00009 l
261.44872 6.00009 260.99773 6.44995 260.99773 6.99987 c
260.99773 13.00025 l
260.99773 13.55017 261.44759 14.00003 261.99751 14.00003 c
@c
F

@rax %Note: Object
280.99757 1.99984 289.99757 14.00003 @E
/$fm 0 def
 0 O 0 @g
0.8824 0.8039 0.3922  create_rgb_color set_solid_fill
280.99757 14.00003 m
289.99757 14.00003 L
289.99757 1.99984 L
285.49786 6.49984 L
280.99757 1.99984 L
280.99757 14.00003 L
@c
F

@rax %Note: Object
300.99827 1.99984 309.99827 14.00003 @E
/$fm 0 def
 0 O 0 @g
0.8824 0.8039 0.3922  create_rgb_color set_solid_fill
300.99827 14.00003 m
309.99827 14.00003 L
309.99827 1.99984 L
305.49855 6.49984 L
300.99827 1.99984 L
300.99827 14.00003 L
@c
301.99833 4.50000 m
301.99833 12.99997 L
308.99820 12.99997 L
308.99820 4.50000 L
305.49827 7.99994 L
301.99833 4.50000 L
@c
F

@rax %Note: Object
1.00006 1.99984 13.04107 13.00025 @E
/$fm 0 def
 0 O 0 @g
1.0000 0.5882 0.5882  create_rgb_color set_solid_fill
7.01603 11.43524 m
4.98898 14.55846 1.00006 12.50929 1.00006 9.62702 c
1.00006 7.41345 4.46287 5.50658 7.01603 1.99984 C
9.58054 5.50658 13.04107 7.41345 13.04107 9.62702 c
13.04107 12.50929 9.18567 14.55846 7.01603 11.43524 C
@c
F

%%PageTrailer
@rs
@rs
%%Trailer
@EndSysCorelDict
end
%%DocumentSuppliedResources: procset wCorel16Dict 16.0 0
%%EOF

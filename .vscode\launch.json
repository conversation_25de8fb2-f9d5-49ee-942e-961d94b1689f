{"version": "0.2.0", "configurations": [{"name": "Next.js: debug client-side", "type": "chrome", "request": "launch", "url": "http://localhost:3000", "webRoot": "${workspaceFolder}/admin"}, {"name": "Next.js: debug server-side", "type": "node-terminal", "request": "launch", "command": "cd admin; npm run dev"}, {"name": "Next.js: debug full stack", "type": "node-terminal", "request": "launch", "command": "cd admin; npm run dev", "serverReadyAction": {"pattern": "- Local:\\s+(https?://.+)$", "uriFormat": "%s", "action": "debugWithChrome"}}]}
body, html {
	margin:0;
	padding:0;
	font-family:"Times New Roman", Times, serif;
	background:#fff;
	}

td	{
	color:#000;
	font-size:16px;
	text-align:left;
	}

p {
	font-size:1.125em;
	padding:0;
	margin:0 0 1.5em 0;
	line-height:1.5em;
	}
	
p.autor {
	color:#777;
	margin:0 0 0.25em 0;
	font-style:italic;
	}	
	
p.search {
	font-size:1em;
	margin:0 0 1.25em 0;
	color:#666;
	}
	
p.date {
	font-size:0.875em;;
	color:#777;
	margin:0 0 0.25em 0;
	}

p.medium {
	font-size:1em;
	padding:0 0 1em 0;
	margin:0;
	line-height:1.5em;
	}
	
p.small {
	font-size:0.875em;
	padding:0;
	margin:0;
	}					
	
img {
	border:0;
	}	

a	{
	color:#000;
	text-decoration:underline;
	}

a:hover	{
	text-decoration:none;
	color:#c00;
	}
	
h1 {
	font-variant:small-caps;
	font-size:1.5em;
	font-weight:normal;
	padding:0;
	margin:0 0 2px 0;
	border-bottom:3px solid #ccccc2;
	}
	
h2 {
	font-size:1.5em;
	font-weight:bold;
	margin:0 0 0.75em 0;
	line-height:1.1em;
	padding:0;
	}

h3 {
	font-size:1.125em;
	font-weight:bold;
	margin:0 0 0.25em 0;
	line-height:1.1em;
	padding:0;
	}
	
h4 {
	font-size:1em;
	font-weight:bold;
	margin:0 0 0.25em 0;
	line-height:1.1em;
	padding:0;
	}
	
input, textarea {
	font-family:Arial, Helvetica, sans-serif;
	font-size:0.75em;
	margin:0 0 0.25em 0;
	background:#fff;
	border:1px solid #aaa;
	}
	
.top {
	font-size:1em;
	}

.box {
	width:860px;
	border-collapse:collapse;
	}
	
.box td {
	padding:0;
/*	border:1px solid #00CC00;*/
	}
	
.box td.left {
	width:32%;
	padding:1em 2em 2em 30px;
	}
	
.box td.right {
	width:68%;
	padding:1em 2em 2em 6em;
	}
	
.box td.left_search {
	padding:3.5em 0 0 30px;
	}
	
.box td.right_header {
	padding:1.5em 0 0 0;
	}
	
.box td.right_header_content {
	padding:3.5em 0 0 6em;
	}	
	
.box td.header {
	background:#ebebe1
	url("back_feather.jpg")
	bottom right
	no-repeat;
	height:170px;
	color:#444;
	}
	
.box td.guestbook {
	background:#ebebe1
	url("back_guest.jpg")
	bottom right
	no-repeat;
	height:170px;
	color:#444;
	}	

.box td.catalog_fon {
	background:#ebebe1;
	}

.box td.footer {
	font-size:0.875em;
	padding:0 1em 4em 1em;
	line-height:1.5em;
	}
	
.mainmenu, .profile {
	font-variant:small-caps;
	font-size:14px;
	padding:0;
	margin:0;
	}

.mainmenu {
	float:left;
	}
		
.profile {
	text-align:right;
	}	

.mainmenu a, .mainmenu_current, .profile a, .profile_current {
	padding:0.25em 0.5em 0.25em 0.5em;
	margin:0 1.5em 0 0.5em;
	line-height:1em;
	color:#58a;
	}
	
.mainmenu a:hover, .profile a:hover {
	color:#c00;
	}	

.mainmenu_current, .profile_current {
	background:#ebebe1;
	color:#000;
	}

.logo {
	padding:20px 0 0 50px;
	float:left;
	}
	
.logo_left_text {
	float:left;
	font-size:22px;
	font-style:italic;
	padding:30px 0 0 90px;
	line-height:1.1em;
	color:#444;
	}
	
.logo_left_text_padding {
	padding:0 0 0 5em;
	}		
	
.q_left {
	float:left;
	display:inline;
	font-size:18px;
	margin:20px 0 0 30px;
	line-height:1.5em;
	background:
	url("q-l.gif")
	bottom left
	no-repeat;
	padding:0 0 0 38px;
	}
	
.q_right {
	float:left;
	background:
	url("q-r.gif")
	top left
	no-repeat;
	margin:20px 0 0 15px;
	padding:0 0 0 30px;
	}
	
.q_autor {
	text-align:right;
	font-style:italic;
	}
	
.catalog {
	margin:1em 0 0.75em 0;
	text-align:center;
	}

.catalog a, .catalog_current  {
	padding:0 0.25em 0 0.25em;
	margin:0;
	color:#666;
	}
	
.catalog a:hover {
	color:#c00;
	}

.catalog_current {
	color:#c00;
	font-weight:bold;
	}
	
.catalog_hole {
	margin:0 2em 0 0;
	}	

.left_spacer {
	background:
	url("left.gif")
	1.5em center
	no-repeat;
	height:4em;
	}

.left_header {
	font-variant:small-caps;
	font-weight:bold;
	padding:0 0 0.5em 0;
	}		

.left_header a {
	color:#58a;
	}		

.left_header a:hover {
	color:#c00;
	}		
	
.left_text {
	padding:0;
	margin:0 0 0.5em 0;
	line-height:1.2em;
	}

.left_text a {
	padding:0;
	margin:0 0 0.5em 0;
	line-height:1.2em;
	display:block;
	color:#666;
	}
	
.left_text a:hover {
	color:#c00;
	}		
	
.h_left {
	background:
	url("h-l.gif")
	left 0.3em
	no-repeat;
	padding:0;
	margin-left:2em;
	}
	
.h_right {
	background:
	url("h-r.gif")
	right 0.3em
	no-repeat;
	padding:0 2em 0 2em;
	line-height:1.75em;
	}
	
.h_bold_line {
	font-size:0.9em;
	padding:0;
	margin:0 0 2px 0;
	border-bottom:3px solid #ccccc2;
	}

.h_line {
	clear:both;
	border-bottom:1px solid #ccccc2;
	margin:0;
	padding:0;
	}	

.autor_menu {
	margin:0 0 1.5em 0;
	}

.autor_menu a, .autor_menu_current {
	padding:0 0.5em 0.25em 0.5em;
	margin:0 1em 0 0;
	color:#58a;
	}

.autor_menu a:hover {
	color:#c00;
	}

.autor_menu_current {
	background:#EBEBE1;
	color:#000;
	}

.list {
	font-size:1.125em;
	padding:0 0 1.5em 0;
	}
	
.list a {
	padding:0;
	margin:0 0 0.5em 0;
	display:block;
	}
	
.page {
	margin:0 0 1em 0;
	font-size:0.875em;
	}

.page a, .page_current  {
	padding:0 0.25em 0 0.25em;
	margin:0;
	color:#58a;
	}

.page a:hover {
	color:#C00;
	}

.page_current {
	background:#ebebe1;
	color:#C00;
	}
	
.ctrl {
	margin:0 0 1em 0;
	font-size:0.875em;
	}
	
.ctrl a {
	color:#58a;
	}
	
.ctrl a:hover {
	color:#c00;
	}									

.spacer_line {
	clear:both;
	border-bottom:1px solid #ccccc2;
	font-size:8px;
	margin:0 0 2px 0;
	}
	
.spacer_logo {
	clear:both;
	font-size:0.9em;
	}	
	
.copy {
	color:#777;
	text-align:right;
	}
	
.footer_menu {
	float:left;
	color:#777;
	}
	
.footer_menu a {
	color:#58a;
	}
	
.footer_menu a:hover {
	color:#c00;
	}
	
.answer	{
	background:#ebebe1;
	padding:1em 1em 0 1em;
	margin:0 0 1.5em 0;
	}
	
.post_box {
	padding:1em 0 0 0;
	}
	
.search_box {
	width:120px;
	}	
	
.post_header {
	width:200px;
	}
	
.post_text {
	width:400px;
	height:100px;
	}
	
.noprint {
	padding:0;
	margin:0;
	}	
	
.print {
	display:none;	
	}		
							
"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { signIn } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

const formSchema = z.object({
  username: z.string().min(1, "Кулланучы исеме кирәк"),
  password: z.string().min(1, "Серсүз кирәк"),
});

export function LoginForm() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      username: "",
      password: "",
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsLoading(true);
    setError(null);

    try {
      console.log("Attempting to sign in with:", { username: values.username });

      // Используем NextAuth для аутентификации
      const result = await signIn("Credentials", {
        username: values.username,
        password: values.password,
        redirect: false,
        callbackUrl: "http://localhost:3000/dashboard"
      });

      console.log("Sign in result:", result);

      if (result?.error) {
        setError("Кулланучы исеме яки серсүз дөрес түгел");
        console.error("Sign in error:", result.error);
      } else {
        // Устанавливаем временный cookie для поддержки аутентификации
        document.cookie = "admin-auth=true; path=/; max-age=86400";

        if (result?.url) {
          // Если есть URL для перенаправления, используем его
          router.push(result.url);
        } else {
          // Иначе перенаправляем на дашборд
          router.push("/dashboard");
        }
      }
    } catch (error) {
      console.error("Sign in exception:", error);
      setError("Системага керү вакытында хата килеп чыкты");
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="username"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Кулланучы исеме</FormLabel>
              <FormControl>
                <Input placeholder="admin" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Серсүз</FormLabel>
              <FormControl>
                <Input type="password" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        {error && (
          <p className="text-sm font-medium text-destructive">
            {error}
          </p>
        )}
        <Button type="submit" className="w-full" disabled={isLoading}>
          {isLoading ? "Керү..." : "Керү"}
        </Button>
      </form>
    </Form>
  );
}

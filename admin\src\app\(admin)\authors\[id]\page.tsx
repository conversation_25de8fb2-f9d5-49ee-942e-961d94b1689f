"use client";

import { AuthorForm } from "@/components/authors/author-form";
import { useAuthor } from "@/lib/hooks/use-authors";
import { useParams } from "next/navigation";
import { useDynamicTitle } from "@/lib/utils/use-dynamic-title";
import { useMemo } from "react";

export default function EditAuthorPage() {
  const params = useParams();
  const id = params.id as string;
  const { data: author, isLoading, error } = useAuthor(id);

  // Мемоизируем заголовок, чтобы он не менялся при каждом рендере
  const pageTitle = useMemo(() => {
    return author ? `${author.displayName} үзгәртү` : "Шагыйрь үзгәртү";
  }, [author?.displayName]);

  // Устанавливаем заголовок страницы
  useDynamicTitle(pageTitle);

  if (isLoading) {
    return <div className="text-center py-4">Йөкләнә...</div>;
  }

  if (error || !author) {
    return (
      <div className="text-center py-4 text-red-500">
        Хата: {error?.message || "Шагыйрь табылмады"}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">Шагыйрьне үзгәртү: {author.displayName}</h1>
      <AuthorForm author={author} />
    </div>
  );
}

﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using Shigriyat.Api.Dtos;
using Shigriyat.Api.Extensions;
using Shigriyat.Data;
using Raven.Client.Documents.Queries;

namespace Shigriyat.Api.Controllers
{
    [ApiController]
    [Route("authors")]
    public sealed class AuthorsController : ControllerBase
    {
        private readonly ILogger<AuthorsController> _logger;
        private readonly IAsyncDocumentSession _session;

        public AuthorsController(ILogger<AuthorsController> logger, IAsyncDocumentSession documentSession)
        {
            _logger = logger;
            _session = documentSession;
        }

        [HttpGet]
        public async Task<IEnumerable<AuthorDto>> Get()
        {
            var authors = (await _session
                .Query<Author>()
                .Select(a => new { a.Id, a.DisplayName })
                .ToListAsync())
                .Select(a => new AuthorDto(a.Id, a.Id.TrimPrefix(Author.IdPrefix), a.DisplayName));

            return authors;
        }

        [HttpGet("allWorks")]
        public async Task<IEnumerable<object>> GetAllWorks()
        {
            var works = await (
                from w in _session.Query<Work>()
                    let author = RavenQuery.Load<Author>(w.AuthorId)
                    select new { w, author }
                )
                .ToListAsync();

            return works.Select(t => new WorkDto(t.author, t.w));
        }

        [HttpGet("works")]
        public async Task<IEnumerable<WorkDto>> GetWorks(string id)
        {
            var authorId = Author.IdPrefix + id;
            var works = await _session
                .Query<Work>()
                .Include(w => w.AuthorId)
                .Where(a => a.AuthorId == authorId)
                .ToListAsync();
            var author = await _session.LoadAsync<Author>(authorId);

            return works.Select(w => new WorkDto(author, w)).ToArray();
        }

        [HttpGet("work")]
        public async Task<WorkDto> GetWork(string workId)
        {
            var id = Work.IdPrefix + workId;
            var work = await _session
                .Query<Work>()
                .Where(a => a.Id == id)
                .Include(a => a.AuthorId)
                .SingleOrDefaultAsync();
            var author = await _session.LoadAsync<Author>(work.AuthorId);

            return new WorkDto(author, work);
        }
    }
}
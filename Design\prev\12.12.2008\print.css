body, html {
	margin:0;
	padding:0;
	font-family:"Times New Roman", Times, serif;
	background:#fff;
	}

td	{
	color:#000;
	font-size:16px;
	text-align:left;
	}

p {
	font-size:1.125em;
	padding:0;
	margin:0 0 1.5em 0;
	line-height:1.5em;
	}
	
p.autor {
	color:#777;
	margin:0 0 0.25em 0;
	font-style:italic;
	}	
	
p.search {
	display:none;
	}
	
p.date {
	font-size:0.875em;;
	color:#777;
	margin:0 0 0.25em 0;
	}

p.medium {
	font-size:1em;
	padding:0 0 1em 0;
	margin:0;
	line-height:1.5em;
	}
	
p.small {
	font-size:0.875em;
	padding:0;
	margin:0;
	}					
	
img {
	border:0;
	}	

a	{
	color:#000;
	text-decoration:none;
	}

a:hover	{
	text-decoration:none;
	color:#c00;
	}
	
h1 {
	font-variant:small-caps;
	font-size:1.5em;
	font-weight:normal;
	padding:0;
	margin:0 0 2px 0;
	border-bottom:3px solid #000;
	}
	
h2 {
	font-size:1.5em;
	font-weight:bold;
	margin:0 0 0.75em 0;
	line-height:1.1em;
	padding:0;
	}

h3 {
	font-size:1.125em;
	font-weight:bold;
	margin:0 0 0.25em 0;
	line-height:1.1em;
	padding:0;
	}
	
h4 {
	font-size:1em;
	font-weight:bold;
	margin:0 0 0.25em 0;
	line-height:1.1em;
	padding:0;
	}
	
input, textarea {
	font-family:Arial, Helvetica, sans-serif;
	font-size:0.75em;
	margin:0 0 0.25em 0;
	background:#fff;
	border:1px solid #aaa;
	}
	
.top {
	display:none;
	}

.box {
	width:100%;
	border-collapse:collapse;
	}
	
.box td {
	padding:0;
	}
	
.box td.left {
	width:0;
	}
	
.box td.right {
	width:100%;
	padding:1em 0 0 0;
	}
	
.box td.left_search {
	padding:0;
	}
	
.box td.right_header {
	padding:0;
	}
	
.box td.right_header_content {
	padding:0;
	}	
	
.box td.header {
	background:#fff;
	padding:0;
	}
	
.box td.guestbook {
	background:#fff;
	padding:0;
	}	

.box td.catalog_fon {
	background:#ebebe1;
	}

.box td.footer {
	font-size:0.875em;
	padding:0;
	line-height:1.5em;
	}
	
.mainmenu, .profile {
	display:none;
	}

.logo {
	display:none;
	}
	
.logo_left_text {
	display:none;
	}
	
.logo_left_text_padding {
	padding:0 0 0 5em;
	}		
	
.q_left {
	display:none;
	}
	
.q_right {
	display:none;
	}
	
.q_autor {
	text-align:right;
	font-style:italic;
	}
	
.catalog {
	display:none;
	}

.catalog a, .catalog_current  {
	padding:0 0.25em 0 0.25em;
	margin:0;
	color:#666;
	}
	
.catalog a:hover {
	color:#c00;
	}

.catalog_current {
	color:#c00;
	font-weight:bold;
	}
	
.catalog_hole {
	margin:0 2em 0 0;
	}	

.left_spacer {
	display:none;
	}

.left_header {
	display:none;
	}		

.left_header a {
	display:none;
	}		

.left_header a:hover {
	color:#c00;
	}		
	
.left_text {
	display:none;
	}

.left_text a {
	display:none;
	}
	
.left_text a:hover {
	color:#c00;
	}		
	
.h_left {
	padding:0;
	margin:0;
	}
	
.h_right {
	padding:0;
	line-height:1.75em;
	margin:0;
	}
	
.h_bold_line {
	font-size:0.9em;
	padding:0;
	margin:0 0 2px 0;
	border-bottom:3px solid #ccccc2;
	}

.h_line {
	clear:both;
	border-bottom:1px solid #000;
	margin:0;
	padding:0;
	}	

.autor_menu {
	margin:0 0 1.5em 0;
	}

.autor_menu a {
	display:none;
	}

.autor_menu a:hover {
	color:#c00;
	}

.autor_menu_current {
	color:#000;
	padding:0 0.5em 0.25em 0;
	margin:0 1em 0 0;
	}

.list {
	font-size:1.125em;
	padding:0 0 1.5em 0;
	}
	
.list a {
	padding:0;
	margin:0 0 0.5em 0;
	display:block;
	}
	
.page {
	display:none;
	}

.page a, .page_current  {
	padding:0 0.25em 0 0.25em;
	margin:0;
	color:#58a;
	}

.page a:hover {
	color:#C00;
	}

.page_current {
	background:#ebebe1;
	color:#C00;
	}
	
.ctrl {
	display:none;
	}
	
.ctrl a {
	color:#58a;
	}
	
.ctrl a:hover {
	color:#c00;
	}									

.spacer_line {
	display:none;
	}
	
.spacer_logo {
	display:none;
	}	
	
.copy {
	display:none;
	
	}
	
.footer_menu {
	display:none;
	}
	
.footer_menu a {
	color:#58a;
	}
	
.footer_menu a:hover {
	color:#c00;
	}
	
.answer	{
	background:#fff;
	padding:1em 1em 0 1em;
	margin:0 0 1.5em 0;
	}
	
.post_box {
	padding:1em 0 0 0;
	}
	
.search_box {
	width:120px;
	}	
	
.post_header {
	width:200px;
	}
	
.post_text {
	width:400px;
	height:100px;
	}
	
.noprint {
	display:none;
	}
	
.print {
	border-top:1px solid #000;
	padding:0.5em 0 0 0;
	margin:1em 0 0 0;
	}			
							
using System;
using System.Threading.Tasks;
using HotChocolate;
using HotChocolate.Authorization;
using HotChocolate.Types;
using JetBrains.Annotations;
using Raven.Client.Documents.Session;
using Shigriyat.Api.GraphQL.Inputs;
using Shigriyat.Api.GraphQL.Payloads;
using Shigriyat.Data;

namespace Shigriyat.Api.GraphQL
{
    [ExtendObjectType(Name = "Mutation")]
    [UsedImplicitly]
    public class AdminMutations
    {
        //[Authorize(Roles = ["Admin"])]
        public async Task<CreateAuthorPayload> CreateAuthor(
            CreateAuthorInput input,
            [Service] IAsyncDocumentSession session)
        {
            var author = new Author
            {
                UrlPart = input.UrlPart,
                Name = input.Name,
                SurName = input.SurName,
                LastName = input.LastName,
                DisplayName = input.DisplayName,
                Biography = input.Biography,
                BirthDate = input.BirthDate,
                DeathDate = input.DeathDate,
                AddedDate = DateTime.UtcNow,
                ModifiedDate = DateTime.UtcNow
            };

            await session.StoreAsync(author);
            await session.SaveChangesAsync();

            return new CreateAuthorPayload(author);
        }

        //[Authorize(Roles = ["Admin"])]
        public async Task<UpdateAuthorPayload> UpdateAuthor(
            string id,
            UpdateAuthorInput input,
            [Service] IAsyncDocumentSession session)
        {
            var author = await session.LoadAsync<Author>(id);

            if (author == null)
            {
                throw new Exception($"Author with ID {id} not found");
            }

            if (input.Name != null)
                author.Name = input.Name;

            if (input.SurName != null)
                author.SurName = input.SurName;

            if (input.LastName != null)
                author.LastName = input.LastName;

            if (input.DisplayName != null)
                author.DisplayName = input.DisplayName;

            if (input.Biography != null)
                author.Biography = input.Biography;

            if (input.BirthDate.HasValue)
                author.BirthDate = input.BirthDate;

            if (input.DeathDate.HasValue)
                author.DeathDate = input.DeathDate;

            author.ModifiedDate = DateTime.UtcNow;

            await session.SaveChangesAsync();

            return new UpdateAuthorPayload(author);
        }

        //[Authorize(Roles = ["Admin"])]
        public async Task<DeleteAuthorPayload> DeleteAuthor(
            string id,
            [Service] IAsyncDocumentSession session)
        {
            var author = await session.LoadAsync<Author>(id);

            if (author == null)
            {
                throw new Exception($"Author with ID {id} not found");
            }

            session.Delete(author);
            await session.SaveChangesAsync();

            return new DeleteAuthorPayload(true);
        }

        //[Authorize(Roles = ["Admin"])]
        public async Task<CreateWorkPayload> CreateWork(
            CreateWorkInput input,
            [Service] IAsyncDocumentSession session)
        {
            var author = await session.LoadAsync<Author>(input.AuthorId);

            if (author == null)
            {
                throw new Exception($"Author with ID {input.AuthorId} not found");
            }

            var work = new Work
            {
                AuthorId = input.AuthorId,
                Title = input.Title,
                Content = input.Content,
                Genres = input.Genres,
                Comments = input.Comments,
                Source = input.Source,
                PublishedDate = input.PublishedDate ?? DateTime.UtcNow,
                PublishedBy = "Admin",
                ModifiedDate = DateTime.UtcNow,
                ModifiedBy = "Admin"
            };

            await session.StoreAsync(work);
            await session.SaveChangesAsync();

            return new CreateWorkPayload(work);
        }

        //[Authorize(Roles = ["Admin"])]
        public async Task<UpdateWorkPayload> UpdateWork(
            string id,
            UpdateWorkInput input,
            [Service] IAsyncDocumentSession session)
        {
            var work = await session.LoadAsync<Work>(id);

            if (work == null)
            {
                throw new Exception($"Work with ID {id} not found");
            }

            if (input.AuthorId != null)
            {
                var author = await session.LoadAsync<Author>(input.AuthorId);

                if (author == null)
                {
                    throw new Exception($"Author with ID {input.AuthorId} not found");
                }

                work.AuthorId = input.AuthorId;
            }

            if (input.Title != null)
                work.Title = input.Title;

            if (input.Content != null)
            {
                work.Content = input.Content;
                // Set ContentFormat to Blocks for new content from BlockNote editor
                work.ContentFormat = ContentFormat.Blocks;
            }

            if (input.Genres != null)
                work.Genres = input.Genres;

            if (input.Comments != null)
                work.Comments = input.Comments;

            if (input.Source != null)
                work.Source = input.Source;

            if (input.PublishedDate.HasValue)
                work.PublishedDate = input.PublishedDate.Value;

            work.ModifiedDate = DateTime.UtcNow;
            work.ModifiedBy = "Admin";

            await session.SaveChangesAsync();

            return new UpdateWorkPayload(work);
        }

        //[Authorize(Roles = ["Admin"])]
        public async Task<DeleteWorkPayload> DeleteWork(
            string id,
            [Service] IAsyncDocumentSession session)
        {
            var work = await session.LoadAsync<Work>(id);

            if (work == null)
            {
                throw new Exception($"Work with ID {id} not found");
            }

            session.Delete(work);
            await session.SaveChangesAsync();

            return new DeleteWorkPayload(true);
        }
    }
}

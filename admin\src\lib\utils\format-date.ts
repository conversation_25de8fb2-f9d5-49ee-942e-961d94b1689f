// Названия месяцев на татарском языке
const tatarMonths = [
  'гыйнвар', // январь
  'февраль', // февраль
  'март', // март
  'апрель', // апрель
  'май', // май
  'июнь', // июнь
  'июль', // июль
  'август', // август
  'сентябрь', // сентябрь
  'октябрь', // октябрь
  'ноябрь', // ноябрь
  'декабрь', // декабрь
];

export function formatDate(dateString: string): string {
  if (!dateString) return "-";

  const date = new Date(dateString);

  if (isNaN(date.getTime())) {
    return "-";
  }

  // Форматируем дату в формате "день месяц год"
  const day = date.getDate();
  const month = tatarMonths[date.getMonth()];
  const year = date.getFullYear();

  return `${day} ${month} ${year}`;
}

export function formatDateForInput(dateString: string): string {
  if (!dateString) return "";

  const date = new Date(dateString);

  if (isNaN(date.getTime())) {
    return "";
  }

  return date.toISOString().split("T")[0];
}

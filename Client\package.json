{"name": "s<PERSON><PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001 --turbopack --turbopack", "build": "next build", "start": "next start -p 3001"}, "dependencies": {"@apollo/client": "^3.6.9", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/line-clamp": "^0.3.1", "graphql": "^16.5.0", "next": "^15.2.4", "postcss-flexbugs-fixes": "^5.0.2", "postcss-preset-env": "^10.0.2", "react": "^19.1.0", "react-dom": "^19.1.0", "sass": "^1.37.5", "sharp": "^0.33.5"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.1", "@types/node": "18.11.9", "@types/react": "18.0.24", "autoprefixer": "^10.4.20", "eslint": "^7.32.0", "eslint-config-next": "^15.0.1", "eslint-plugin-react": "^7.35.0", "globals": "^15.9.0", "postcss": "^8.4.41", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.6", "tailwindcss": "^3.4.10", "typescript": "5.5.4"}}
import { Block, PartialBlock, BlockNoteEditor } from '@blocknote/core';
import { toast } from 'sonner';

/**
 * Checks if a string is HTML by looking for HTML tags
 * @param str String to check
 * @returns True if the string appears to be HTML
 */
function isHtml(str: string): boolean {
  // Simple heuristic: check if string contains HTML tags
  const htmlTagRegex = /<[^>]*>/;
  return htmlTagRegex.test(str.trim());
}

/**
 * Checks if a string is JSON by trying to parse it
 * @param str String to check
 * @returns True if the string is valid JSON
 */
function isJson(str: string): boolean {
  try {
    JSON.parse(str);
    return true;
  } catch {
    return false;
  }
}

/**
 * Checks if a string is a BlockNote JSON value
 * @param str String to check
 * @returns True if the string appears to be BlockNote JSON
 */
export function isBlockNoteJson(str: string): boolean {
  if (!isJson(str)) return false;
  
  try {
    const parsed = JSON.parse(str);
    return true;
  } catch {
    return false;
  }
}

/**
 * Detects the format of content string
 * @param content Content string to analyze
 * @param contentFormat Optional format hint from server
 * @returns 'html' or 'json'
 */
export function detectContentFormat(content: string, contentFormat?: string): 'html' | 'json' {
  // If we have a format hint from server, use it
  if (contentFormat === 'Html') return 'html';
  if (contentFormat === 'Blocks') return 'json';
  
  // Use heuristics to detect format
  if (isBlockNoteJson(content)) return 'json';
  if (isHtml(content)) return 'html';
  
  // Default to HTML for plain text or unknown format
  return 'html';
}

/**
 * Converts HTML to BlockNote blocks using the editor's async parser
 * @param html HTML string
 * @param editor BlockNoteEditor instance
 * @returns Promise resolving to array of BlockNote blocks (partial blocks)
 */
export async function htmlToBlockNote(html: string, editor: BlockNoteEditor): Promise<PartialBlock[]> {
  console.log('[htmlToBlockNote] Input HTML:', html);
  try {
    const blocks = await editor.tryParseHTMLToBlocks(html);
    console.log('[htmlToBlockNote] Parsed blocks:', blocks);
    if (!blocks || blocks.length === 0) {
      return [{ type: 'paragraph', content: [] }];
    }
    return blocks;
  } catch (e) {
    console.error('[htmlToBlockNote] Error parsing HTML to BlockNote blocks:', e);
    toast.error('Хаталы HTML: Эчтәлекне редакторга күчереп булмады.');
    return [{ type: 'paragraph', content: [] }];
  }
}


/**
 * Prepares content for saving to server
 * @param blocks BlockNote blocks (can be partial)
 * @returns JSON string representation of blocks
 */
export function prepareContentForSave(blocks: PartialBlock[]): string {
  return JSON.stringify(blocks);
}

/**
 * Prepares content for loading into BlockNote editor (async)
 * @param content Content string from server
 * @param editor BlockNoteEditor instance
 * @param contentFormat Optional format hint from server
 * @returns Promise resolving to array of BlockNote blocks (partial blocks for editor)
 */
export async function prepareContentForEditor(content: string, editor: BlockNoteEditor, contentFormat?: string): Promise<PartialBlock[]> {
  console.log('[prepareContentForEditor] Raw content:', content);
  if (!content || content.trim() === '') {
    console.log('[prepareContentForEditor] Content is empty, returning default paragraph.');
    return [{ type: 'paragraph', content: [] }];
  }

  const format = detectContentFormat(content, contentFormat);
  console.log('[prepareContentForEditor] Detected format:', format);

  if (format === 'json') {
    try {
      const parsed = JSON.parse(content) as PartialBlock[];
      console.log('[prepareContentForEditor] Parsed JSON blocks:', parsed);
      return parsed;
    } catch (error) {
      console.error('[prepareContentForEditor] Error parsing BlockNote JSON:', error);
      // Fallback to HTML conversion
      return await htmlToBlockNote(content, editor);
    }
  } else if (format === 'html') {
    const blocks = await htmlToBlockNote(content, editor);
    console.log('[prepareContentForEditor] Blocks from HTML:', blocks);
    return blocks;
  } else {
    // Fallback: treat as plain text
    console.log('[prepareContentForEditor] Treating as plain text.');
    return [{ type: 'paragraph', content: [{ type: 'text', text: content, styles: {} }] }];
  }
}

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getWorks, getWork, createWork, updateWork, deleteWork } from "@/lib/api/works";
import { Work, CreateWorkInput, UpdateWorkInput } from "@/lib/graphql/generated/graphql";

export function useWorks(page: number = 1, pageSize: number = 10, filter?: string, authorId?: string) {
  return useQuery({
    queryKey: ["works", { page, pageSize, filter, authorId }],
    queryFn: () => getWorks(page, pageSize, filter, authorId),
    staleTime: 5000, // Данные считаются актуальными в течение 5 секунд
    placeholderData: (previousData) => previousData, // Используем предыдущие данные как заполнитель
  });
}

export function useWork(id: string) {
  return useQuery({
    queryKey: ["works", id],
    queryFn: () => getWork(id),
    enabled: !!id,
  });
}

export function useCreateWork() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateWorkInput) => createWork(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["works"] });
    },
  });
}

export function useUpdateWork() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateWorkInput }) =>
      updateWork(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ["works"] });
      queryClient.invalidateQueries({ queryKey: ["works", variables.id] });
    },
  });
}

export function useDeleteWork() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deleteWork(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["works"] });
    },
  });
}

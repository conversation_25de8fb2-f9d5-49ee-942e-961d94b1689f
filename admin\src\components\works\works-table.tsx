"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useWorks, useDeleteWork } from "@/lib/hooks/use-works";
import { formatDate } from "@/lib/utils/format-date";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { toast } from "sonner";
import { Pagination } from "@/components/ui/pagination";
import { AuthorSelect } from "@/components/authors/author-select";
import { cn } from "@/lib/utils";
import { ChevronsUpDown } from "lucide-react";

interface WorksTableProps {
  initialAuthorId?: string;
  onAuthorChange?: (authorId: string) => void;
}

export function WorksTable({ initialAuthorId, onAuthorChange }: WorksTableProps) {
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [workToDelete, setWorkToDelete] = useState<string | null>(null);
  const [pageSizeDropdownOpen, setPageSizeDropdownOpen] = useState(false);
  const [authorId, setAuthorId] = useState<string>(initialAuthorId || "");
  const [debouncedAuthorId, setDebouncedAuthorId] = useState<string>(initialAuthorId || "");

  const searchInputRef = useRef<HTMLInputElement>(null);
  const pageSizeRef = useRef<HTMLDivElement>(null);
  const deleteWork = useDeleteWork();

  // Дебаунсинг для поиска
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Дебаунсинг для фильтра по автору
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedAuthorId(authorId);
    }, 300);

    return () => clearTimeout(timer);
  }, [authorId]);

  // Сброс страницы при изменении поискового запроса или фильтра по автору
  useEffect(() => {
    setCurrentPage(1);
  }, [debouncedSearchTerm, debouncedAuthorId]);

  // Удаляем эффект, который вызывал бесконечный цикл
  // Теперь мы будем вызывать onAuthorChange только при явном изменении автора в компоненте AuthorSelect

  // Получаем список произведений с учетом фильтров
  const { data, isLoading, error, refetch } = useWorks(currentPage, pageSize, debouncedSearchTerm, debouncedAuthorId);

  const works = data?.works || [];
  const totalCount = data?.totalCount || 0;
  const totalPages = Math.ceil(totalCount / pageSize);

  if (isLoading) {
    return <div className="text-center py-4">Йөкләнә...</div>;
  }

  if (error) {
    return <div className="text-center py-4 text-red-500">Хата: {error.message}</div>;
  }

  const handleDelete = async () => {
    if (workToDelete) {
      try {
        await deleteWork.mutateAsync(workToDelete);
        toast.success("Әсәр уңышлы бетерелде");
        setWorkToDelete(null);

        // Сохраняем фокус после обновления данных
        const activeElement = document.activeElement;
        await refetch();
        if (activeElement === searchInputRef.current) {
          searchInputRef.current?.focus();
        }
      } catch (error) {
        toast.error("Хата: " + (error instanceof Error ? error.message : "Билгесез хата"));
      }
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 w-full sm:w-auto">
          <div className="flex flex-col md:flex-row gap-2 w-full">
            <Input
              ref={searchInputRef}
              placeholder="Эзләү..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full sm:w-auto max-w-sm"
            />

            <div className="relative w-full sm:w-auto max-w-sm">
              <AuthorSelect
                value={authorId}
                onChange={(newAuthorId) => {
                  setAuthorId(newAuthorId);
                  setCurrentPage(1); // Сбрасываем страницу при изменении фильтра

                  // Вызываем onAuthorChange только при явном изменении автора
                  if (onAuthorChange) {
                    onAuthorChange(newAuthorId);
                  }
                }}
              />
            </div>
          </div>

          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground whitespace-nowrap">Биттә күрсәтү:</span>
            <div className="relative" ref={pageSizeRef}>
              <Button
                variant="outline"
                className="w-16 h-9 px-3 flex justify-between items-center"
                onClick={() => setPageSizeDropdownOpen(!pageSizeDropdownOpen)}
              >
                {pageSize}
                <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50" />
              </Button>

              {pageSizeDropdownOpen && (
                <div className="absolute top-full mt-1 w-16 rounded-md border bg-popover text-popover-foreground shadow-md z-[9999]">
                  <div className="p-1">
                    {[5, 10, 20, 50].map((size) => (
                      <div
                        key={size}
                        className={cn(
                          "relative flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground",
                          pageSize === size && "bg-accent text-accent-foreground"
                        )}
                        onClick={() => {
                          setPageSize(size);
                          setCurrentPage(1);
                          setPageSizeDropdownOpen(false);
                        }}
                      >
                        {size}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
        <Button onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          // Если выбран автор, добавляем его в URL
          const url = authorId
            ? `/works/create?authorId=${encodeURIComponent(authorId)}`
            : "/works/create";
          router.push(url);
        }}>
          Яңа әсәр өстәү
        </Button>
      </div>

      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Исем</TableHead>
              <TableHead>Басылган көне</TableHead>
              <TableHead>Үзгәртелгән көне</TableHead>
              <TableHead className="text-right">Гамәлләр</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {works?.length ? (
              works.map((work) => (
                <TableRow key={work.id}>
                  <TableCell className="font-medium">{work.title}</TableCell>
                  <TableCell>{work.publishedDate ? formatDate(work.publishedDate) : "-"}</TableCell>
                  <TableCell>{formatDate(work.modifiedDate)}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          // Извлекаем ID из полного ID работы (убираем префикс "works/")
                          const workId = work.id.replace('works/', '');
                          router.push(`/works/${workId}`);
                        }}
                      >
                        Үзгәртү
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          setWorkToDelete(work.id);
                        }}
                      >
                        Бетерү
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={4} className="text-center">
                  Әсәрләр табылмады
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <Dialog open={!!workToDelete} onOpenChange={(open) => !open && setWorkToDelete(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Әсәрне бетерергә телисезме?</DialogTitle>
            <DialogDescription>
              Бу гамәлне кире кайтарып булмый. Әсәр бетереләчәк.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setWorkToDelete(null)}>
              Юк
            </Button>
            <Button variant="destructive" onClick={handleDelete} disabled={deleteWork.isPending}>
              {deleteWork.isPending ? "Бетерелә..." : "Әйе, бетерергә"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {totalPages > 1 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={setCurrentPage}
        />
      )}

      <div className="text-sm text-muted-foreground mt-2 text-center">
        Барлыгы {totalCount} әсәр, күрсәтелә {(currentPage - 1) * pageSize + 1}-{Math.min(currentPage * pageSize, totalCount)}
      </div>
    </div>
  );
}

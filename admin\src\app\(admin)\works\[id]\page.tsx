"use client";

import { WorkForm } from "@/components/works/work-form";
import { useWork } from "@/lib/hooks/use-works";
import { useParams } from "next/navigation";
import { useDynamicTitle } from "@/lib/utils/use-dynamic-title";
import { useMemo } from "react";

export default function EditWorkPage() {
  const params = useParams();
  const id = params.id as string;
  const { data: work, isLoading, error } = useWork(id);

  // Мемоизируем заголовок, чтобы он не менялся при каждом рендере
  const pageTitle = useMemo(() => {
    return work ? `${work.title} үзгәртү` : "Әсәр үзгәртү";
  }, [work?.title]);

  // Устанавливаем заголовок страницы
  useDynamicTitle(pageTitle);

  if (isLoading) {
    return <div className="text-center py-4">Йөкләнә...</div>;
  }

  if (error || !work) {
    return (
      <div className="text-center py-4 text-red-500">
        Хата: {error?.message || "Әсәр табылмады"}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">Әсәрне үзгәртү: {work.title}</h1>
      <WorkForm work={work} />
    </div>
  );
}

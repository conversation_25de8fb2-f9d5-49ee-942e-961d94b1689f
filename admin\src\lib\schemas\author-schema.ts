import { z } from "zod";

export const authorSchema = z.object({
  name: z.string().min(1, "Исем кирәк"),
  surName: z.string().optional(),
  lastName: z.string().optional(),
  displayName: z.string().min(1, "Күрсәтелә торган исем кирәк"),
  urlPart: z.string().min(1, "URL өлеше кирәк").regex(/^[a-z0-9-]+$/, "URL өлеше латин хәрефләре, саннар һәм "-" символыннан гына торырга тиеш"),
  biography: z.string().optional(),
  birthDate: z.string().optional(),
  deathDate: z.string().optional(),
});

export type AuthorFormValues = z.infer<typeof authorSchema>;

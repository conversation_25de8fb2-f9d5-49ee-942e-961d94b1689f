"use client"

import { useState, useEffect } from "react"
import { ThemeProvider as NextThemesProvider } from "next-themes"
import type { ThemeProviderProps } from "next-themes"

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  // Используем состояние для предотвращения ошибок гидратации
  const [mounted, setMounted] = useState(false)

  // Устанавливаем mounted в true после первого рендера
  useEffect(() => {
    setMounted(true)
  }, [])

  // Если компонент не смонтирован, возвращаем только детей без провайдера
  if (!mounted) {
    return <>{children}</>
  }

  return (
    <NextThemesProvider
      {...props}
      enableSystem={false}
      enableColorScheme={false}
      disableTransitionOnChange
      defaultTheme="light"
    >
      {children}
    </NextThemesProvider>
  )
}

﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@ViewBag.Title</title>
    <link href="~/Content/bootstrap.css" type="text/css" rel="stylesheet" />
    <link href="~/Content/bootstrap-responsive.css" type="text/css" rel="stylesheet" />
    @Styles.Render("~/Content/css")
    @RenderSection("styles", required: false)
</head>
    <body class="@RenderSection("bodyClass", false)">
        <div class="navbar navbar-static-top">
            <div class="navbar-inner">
                <button type="button" class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse">
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>

                <a class="brand hidden-phone" href="#">
                    <img src="~/Content/images/logo.png" width="130" height="40" />
                </a>

                <form class="navbar-search pull-right" action="">
                    <div class="input-append">
                        <input type="text" class="span3" placeholder="Шагыйрь, шигырь яки жанр">
                        <span class="add-on">
                            <span class="icon-search"></span>
                        </span>
                    </div>
                </form>

                <div class="nav-collapse collapse">
                    <ul class="nav">
                        <li class="divider-vertical"></li>
                        <li class="divider-vertical"></li>

                        <li><a href="#">Избранное</a></li>

                        <li class="divider-vertical"></li>
                        <li class="divider-vertical"></li>

                        <li><a href="#about">Керу</a></li>
                        <li><a href="#contact">Регистрация</a></li>
                    </ul>
                </div>
                <!--/.nav-collapse -->
            </div>
        </div>

        <div class="container-fluid">
            @RenderBody()
        </div>

        <footer class="container-fluid">
            <div class="row-fluid">
                <div class="span3">
                    <p>
                        © 2006 — 2013, Шигърият.ру<br />
                        <a href="#">Проект турында</a>
                    </p>
                </div>
                <div class="span3">
                    Пишите письма <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
                <div class="span3 offset3">
                    Әгәр дә сез орфографик хата тапсагыз, хаталы сүзне тычкан ярдәмендә билгеләгез һәм Ctrl+Enter төймәләренә басыгыз.
                    <img src="http://shigriyat.ru/content/orphus.gif" />
                </div>
            </div>
        </footer>

        @Scripts.Render("~/bundles/jquery")
        <script src="~/Scripts/bootstrap.js"></script>
        @RenderSection("scripts", required: false)
    </body>
</html>

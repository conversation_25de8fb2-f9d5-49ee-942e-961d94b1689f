// Этот файл является примером использования сгенерированных типов
// После генерации типов, вы можете импортировать их и использовать в своем коде

import { useQuery, useMutation } from '@tanstack/react-query';
import { 
  GetAuthorsQuery, 
  GetAuthorsQueryVariables,
  GetAuthorQuery,
  GetAuthorQueryVariables,
  CreateAuthorMutation,
  CreateAuthorMutationVariables,
  UpdateAuthorMutation,
  UpdateAuthorMutationVariables,
  DeleteAuthorMutation,
  DeleteAuthorMutationVariables
} from './types'; // Путь к сгенерированным типам

import { 
  GET_AUTHORS, 
  GET_AUTHOR, 
  CREATE_AUTHOR, 
  UPDATE_AUTHOR, 
  DELETE_AUTHOR 
} from './queries';

// Пример функции для выполнения GraphQL запроса
async function fetchGraphQL<TData, TVariables>(
  query: string, 
  variables?: TVariables
): Promise<TData> {
  const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3556';
  
  const response = await fetch(`${API_URL}/graphql`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      // Добавьте заголовок авторизации, если необходимо
      // 'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      query,
      variables
    })
  });

  const json = await response.json();

  if (json.errors) {
    throw new Error(json.errors[0].message);
  }

  return json.data;
}

// Пример хука для получения списка авторов с использованием сгенерированных типов
export function useAuthorsWithTypes(page: number = 1, pageSize: number = 10) {
  const skip = (page - 1) * pageSize;
  
  return useQuery<GetAuthorsQuery>({
    queryKey: ['authors', { page, pageSize }],
    queryFn: () => fetchGraphQL<GetAuthorsQuery, GetAuthorsQueryVariables>(
      GET_AUTHORS,
      { skip, take: pageSize }
    )
  });
}

// Пример хука для получения одного автора с использованием сгенерированных типов
export function useAuthorWithTypes(id: string) {
  return useQuery<GetAuthorQuery>({
    queryKey: ['author', id],
    queryFn: () => fetchGraphQL<GetAuthorQuery, GetAuthorQueryVariables>(
      GET_AUTHOR,
      { id }
    ),
    enabled: !!id
  });
}

// Пример хука для создания автора с использованием сгенерированных типов
export function useCreateAuthorWithTypes() {
  return useMutation<
    CreateAuthorMutation,
    Error,
    CreateAuthorMutationVariables['input']
  >({
    mutationFn: (input) => fetchGraphQL<
      CreateAuthorMutation, 
      CreateAuthorMutationVariables
    >(CREATE_AUTHOR, { input })
  });
}

// Пример хука для обновления автора с использованием сгенерированных типов
export function useUpdateAuthorWithTypes() {
  return useMutation<
    UpdateAuthorMutation,
    Error,
    { id: string; input: UpdateAuthorMutationVariables['input'] }
  >({
    mutationFn: ({ id, input }) => fetchGraphQL<
      UpdateAuthorMutation, 
      UpdateAuthorMutationVariables
    >(UPDATE_AUTHOR, { id, input })
  });
}

// Пример хука для удаления автора с использованием сгенерированных типов
export function useDeleteAuthorWithTypes() {
  return useMutation<
    DeleteAuthorMutation,
    Error,
    string
  >({
    mutationFn: (id) => fetchGraphQL<
      DeleteAuthorMutation, 
      DeleteAuthorMutationVariables
    >(DELETE_AUTHOR, { id })
  });
}

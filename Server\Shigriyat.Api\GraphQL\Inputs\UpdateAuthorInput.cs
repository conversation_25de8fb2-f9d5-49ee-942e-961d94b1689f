using System;
using JetBrains.Annotations;

namespace Shigriyat.Api.GraphQL.Inputs
{
    [UsedImplicitly]
    public class UpdateAuthorInput
    {
        public string Name { get; set; }
        public string SurName { get; set; }
        public string LastName { get; set; }
        public string DisplayName { get; set; }
        public string Biography { get; set; }
        public DateTime? BirthDate { get; set; }
        public DateTime? DeathDate { get; set; }
    }
}

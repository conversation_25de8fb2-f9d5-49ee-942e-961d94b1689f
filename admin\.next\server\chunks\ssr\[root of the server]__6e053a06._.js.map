{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,wbAAC,8XAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/components/ui/form.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport {\n  Controller,\n  FormProvider,\n  useFormContext,\n  useFormState,\n  type ControllerProps,\n  type FieldPath,\n  type FieldValues,\n} from \"react-hook-form\"\n\nimport { cn } from \"@/lib/utils\"\nimport { Label } from \"@/components/ui/label\"\n\nconst Form = FormProvider\n\ntype FormFieldContextValue<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n> = {\n  name: TName\n}\n\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\n  {} as FormFieldContextValue\n)\n\nconst FormField = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n>({\n  ...props\n}: ControllerProps<TFieldValues, TName>) => {\n  return (\n    <FormFieldContext.Provider value={{ name: props.name }}>\n      <Controller {...props} />\n    </FormFieldContext.Provider>\n  )\n}\n\nconst useFormField = () => {\n  const fieldContext = React.useContext(FormFieldContext)\n  const itemContext = React.useContext(FormItemContext)\n  const { getFieldState } = useFormContext()\n  const formState = useFormState({ name: fieldContext.name })\n  const fieldState = getFieldState(fieldContext.name, formState)\n\n  if (!fieldContext) {\n    throw new Error(\"useFormField should be used within <FormField>\")\n  }\n\n  const { id } = itemContext\n\n  return {\n    id,\n    name: fieldContext.name,\n    formItemId: `${id}-form-item`,\n    formDescriptionId: `${id}-form-item-description`,\n    formMessageId: `${id}-form-item-message`,\n    ...fieldState,\n  }\n}\n\ntype FormItemContextValue = {\n  id: string\n}\n\nconst FormItemContext = React.createContext<FormItemContextValue>(\n  {} as FormItemContextValue\n)\n\nfunction FormItem({ className, ...props }: React.ComponentProps<\"div\">) {\n  const id = React.useId()\n\n  return (\n    <FormItemContext.Provider value={{ id }}>\n      <div\n        data-slot=\"form-item\"\n        className={cn(\"grid gap-2\", className)}\n        {...props}\n      />\n    </FormItemContext.Provider>\n  )\n}\n\nfunction FormLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  const { error, formItemId } = useFormField()\n\n  return (\n    <Label\n      data-slot=\"form-label\"\n      data-error={!!error}\n      className={cn(\"data-[error=true]:text-destructive\", className)}\n      htmlFor={formItemId}\n      {...props}\n    />\n  )\n}\n\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\n\n  return (\n    <Slot\n      data-slot=\"form-control\"\n      id={formItemId}\n      aria-describedby={\n        !error\n          ? `${formDescriptionId}`\n          : `${formDescriptionId} ${formMessageId}`\n      }\n      aria-invalid={!!error}\n      {...props}\n    />\n  )\n}\n\nfunction FormDescription({ className, ...props }: React.ComponentProps<\"p\">) {\n  const { formDescriptionId } = useFormField()\n\n  return (\n    <p\n      data-slot=\"form-description\"\n      id={formDescriptionId}\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction FormMessage({ className, ...props }: React.ComponentProps<\"p\">) {\n  const { error, formMessageId } = useFormField()\n  const body = error ? String(error?.message ?? \"\") : props.children\n\n  if (!body) {\n    return null\n  }\n\n  return (\n    <p\n      data-slot=\"form-message\"\n      id={formMessageId}\n      className={cn(\"text-destructive text-sm\", className)}\n      {...props}\n    >\n      {body}\n    </p>\n  )\n}\n\nexport {\n  useFormField,\n  Form,\n  FormItem,\n  FormLabel,\n  FormControl,\n  FormDescription,\n  FormMessage,\n  FormField,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AAUA;AACA;AAhBA;;;;;;;AAkBA,MAAM,OAAO,uPAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,+YAAA,CAAA,gBAAmB,AAAD,EACzC,CAAC;AAGH,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,wbAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,wbAAC,uPAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;AAEA,MAAM,eAAe;IACnB,MAAM,eAAe,CAAA,GAAA,+YAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,+YAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,uPAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,YAAY,CAAA,GAAA,uPAAA,CAAA,eAAY,AAAD,EAAE;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;AAMA,MAAM,gCAAkB,CAAA,GAAA,+YAAA,CAAA,gBAAmB,AAAD,EACxC,CAAC;AAGH,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,MAAM,KAAK,CAAA,GAAA,+YAAA,CAAA,QAAW,AAAD;IAErB,qBACE,wbAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,wbAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAC3B,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,UAAU,EACjB,SAAS,EACT,GAAG,OAC8C;IACjD,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,wbAAC,iIAAA,CAAA,QAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,GAAG,OAA0C;IAClE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,wbAAC,oSAAA,CAAA,OAAI;QACH,aAAU;QACV,IAAI;QACJ,oBACE,CAAC,QACG,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAE7C,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAkC;IACzE,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,wbAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAkC;IACrE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,wbAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,wbAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/lib/api/authors.ts"], "sourcesContent": ["import { getSession } from \"next-auth/react\";\r\nimport { gql } from \"graphql-tag\";\r\nimport {\r\n  GetAuthorsQuery,\r\n  GetAuthorsQueryVariables,\r\n  GetAuthorQuery,\r\n  GetAuthorQueryVariables,\r\n  CreateAuthorMutation,\r\n  CreateAuthorMutationVariables,\r\n  UpdateAuthorMutation,\r\n  UpdateAuthorMutationVariables,\r\n  DeleteAuthorMutation,\r\n  DeleteAuthorMutationVariables,\r\n  Author,\r\n  CreateAuthorInput,\r\n  UpdateAuthorInput\r\n} from \"@/lib/graphql/generated/graphql\";\r\n\r\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3556\";\r\n\r\nasync function fetchWithAuth(url: string, options: RequestInit = {}) {\r\n  const session = await getSession();\r\n\r\n  return fetch(url, {\r\n    ...options,\r\n    headers: {\r\n      ...options.headers,\r\n      \"Content-Type\": \"application/json\",\r\n      Authorization: `Bearer ${session?.accessToken}`,\r\n    },\r\n  });\r\n}\r\n\r\n// GraphQL запросы и мутации\r\nconst GET_AUTHORS = gql`\r\n  query GetAuthors($skip: Int, $take: Int, $where: AuthorFilterInput) {\r\n    authors(skip: $skip, take: $take, where: $where) {\r\n      items {\r\n        id\r\n        urlPart\r\n        displayName\r\n        birthDate\r\n        deathDate\r\n        addedDate\r\n        modifiedDate\r\n      }\r\n      totalCount\r\n    }\r\n  }\r\n`;\r\n\r\nconst GET_AUTHOR = gql`\r\n  query GetAuthor($id: String!) {\r\n    author(id: $id) {\r\n      id\r\n      urlPart\r\n      name\r\n      surName\r\n      lastName\r\n      displayName\r\n      biography\r\n      birthDate\r\n      deathDate\r\n      addedDate\r\n      modifiedDate\r\n    }\r\n  }\r\n`;\r\n\r\nconst CREATE_AUTHOR = gql`\r\n  mutation CreateAuthor($input: CreateAuthorInput!) {\r\n    createAuthor(input: $input) {\r\n      author {\r\n        id\r\n        urlPart\r\n        displayName\r\n      }\r\n    }\r\n  }\r\n`;\r\n\r\nconst UPDATE_AUTHOR = gql`\r\n  mutation UpdateAuthor($id: String!, $input: UpdateAuthorInput!) {\r\n    updateAuthor(id: $id, input: $input) {\r\n      author {\r\n        id\r\n        urlPart\r\n        displayName\r\n      }\r\n    }\r\n  }\r\n`;\r\n\r\nconst DELETE_AUTHOR = gql`\r\n  mutation DeleteAuthor($id: String!) {\r\n    deleteAuthor(id: $id) {\r\n      success\r\n    }\r\n  }\r\n`;\r\n\r\nexport async function getAuthors(page: number = 1, pageSize: number = 10, filter?: string): Promise<{ authors: Author[], totalCount: number }> {\r\n  const skip = (page - 1) * pageSize;\r\n\r\n  // Создаем объект фильтрации для GraphQL запроса\r\n  const where = filter ? { displayName: { contains: filter } } : undefined;\r\n\r\n  const response = await fetchWithAuth(`${API_URL}/graphql`, {\r\n    method: \"POST\",\r\n    body: JSON.stringify({\r\n      query: GET_AUTHORS.loc?.source.body,\r\n      variables: { skip, take: pageSize, where }\r\n    }),\r\n  });\r\n\r\n  const data = await response.json();\r\n\r\n  if (data.errors) {\r\n    throw new Error(data.errors[0].message);\r\n  }\r\n\r\n  const result = data.data as GetAuthorsQuery;\r\n  return {\r\n    authors: result.authors.items as Author[],\r\n    totalCount: result.authors.totalCount\r\n  };\r\n}\r\n\r\nexport async function getAuthor(id: string): Promise<Author> {\r\n  const response = await fetchWithAuth(`${API_URL}/graphql`, {\r\n    method: \"POST\",\r\n    body: JSON.stringify({\r\n      query: GET_AUTHOR.loc?.source.body,\r\n      variables: { id },\r\n    }),\r\n  });\r\n\r\n  const data = await response.json();\r\n\r\n  if (data.errors) {\r\n    throw new Error(data.errors[0].message);\r\n  }\r\n\r\n  const result = data.data as GetAuthorQuery;\r\n  return result.author as Author;\r\n}\r\n\r\nexport async function createAuthor(input: CreateAuthorInput): Promise<Author> {\r\n  const response = await fetchWithAuth(`${API_URL}/graphql`, {\r\n    method: \"POST\",\r\n    body: JSON.stringify({\r\n      query: CREATE_AUTHOR.loc?.source.body,\r\n      variables: { input },\r\n    }),\r\n  });\r\n\r\n  const data = await response.json();\r\n\r\n  if (data.errors) {\r\n    throw new Error(data.errors[0].message);\r\n  }\r\n\r\n  const result = data.data as CreateAuthorMutation;\r\n  return result.createAuthor.author as Author;\r\n}\r\n\r\nexport async function updateAuthor(id: string, input: UpdateAuthorInput): Promise<Author> {\r\n  const response = await fetchWithAuth(`${API_URL}/graphql`, {\r\n    method: \"POST\",\r\n    body: JSON.stringify({\r\n      query: UPDATE_AUTHOR.loc?.source.body,\r\n      variables: { id, input },\r\n    }),\r\n  });\r\n\r\n  const data = await response.json();\r\n\r\n  if (data.errors) {\r\n    throw new Error(data.errors[0].message);\r\n  }\r\n\r\n  const result = data.data as UpdateAuthorMutation;\r\n  return result.updateAuthor.author as Author;\r\n}\r\n\r\nexport async function deleteAuthor(id: string): Promise<boolean> {\r\n  const response = await fetchWithAuth(`${API_URL}/graphql`, {\r\n    method: \"POST\",\r\n    body: JSON.stringify({\r\n      query: DELETE_AUTHOR.loc?.source.body,\r\n      variables: { id },\r\n    }),\r\n  });\r\n\r\n  const data = await response.json();\r\n\r\n  if (data.errors) {\r\n    throw new Error(data.errors[0].message);\r\n  }\r\n\r\n  const result = data.data as DeleteAuthorMutation;\r\n  return result.deleteAuthor.success;\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAiBA,MAAM,UAAU,6DAAmC;AAEnD,eAAe,cAAc,GAAW,EAAE,UAAuB,CAAC,CAAC;IACjE,MAAM,UAAU,MAAM,CAAA,GAAA,mWAAA,CAAA,aAAU,AAAD;IAE/B,OAAO,MAAM,KAAK;QAChB,GAAG,OAAO;QACV,SAAS;YACP,GAAG,QAAQ,OAAO;YAClB,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,SAAS,aAAa;QACjD;IACF;AACF;AAEA,4BAA4B;AAC5B,MAAM,cAAc,8IAAA,CAAA,MAAG,CAAC;;;;;;;;;;;;;;;AAexB,CAAC;AAED,MAAM,aAAa,8IAAA,CAAA,MAAG,CAAC;;;;;;;;;;;;;;;;AAgBvB,CAAC;AAED,MAAM,gBAAgB,8IAAA,CAAA,MAAG,CAAC;;;;;;;;;;AAU1B,CAAC;AAED,MAAM,gBAAgB,8IAAA,CAAA,MAAG,CAAC;;;;;;;;;;AAU1B,CAAC;AAED,MAAM,gBAAgB,8IAAA,CAAA,MAAG,CAAC;;;;;;AAM1B,CAAC;AAEM,eAAe,WAAW,OAAe,CAAC,EAAE,WAAmB,EAAE,EAAE,MAAe;IACvF,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;IAE1B,gDAAgD;IAChD,MAAM,QAAQ,SAAS;QAAE,aAAa;YAAE,UAAU;QAAO;IAAE,IAAI;IAE/D,MAAM,WAAW,MAAM,cAAc,GAAG,QAAQ,QAAQ,CAAC,EAAE;QACzD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;YACnB,OAAO,YAAY,GAAG,EAAE,OAAO;YAC/B,WAAW;gBAAE;gBAAM,MAAM;gBAAU;YAAM;QAC3C;IACF;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,KAAK,MAAM,EAAE;QACf,MAAM,IAAI,MAAM,KAAK,MAAM,CAAC,EAAE,CAAC,OAAO;IACxC;IAEA,MAAM,SAAS,KAAK,IAAI;IACxB,OAAO;QACL,SAAS,OAAO,OAAO,CAAC,KAAK;QAC7B,YAAY,OAAO,OAAO,CAAC,UAAU;IACvC;AACF;AAEO,eAAe,UAAU,EAAU;IACxC,MAAM,WAAW,MAAM,cAAc,GAAG,QAAQ,QAAQ,CAAC,EAAE;QACzD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;YACnB,OAAO,WAAW,GAAG,EAAE,OAAO;YAC9B,WAAW;gBAAE;YAAG;QAClB;IACF;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,KAAK,MAAM,EAAE;QACf,MAAM,IAAI,MAAM,KAAK,MAAM,CAAC,EAAE,CAAC,OAAO;IACxC;IAEA,MAAM,SAAS,KAAK,IAAI;IACxB,OAAO,OAAO,MAAM;AACtB;AAEO,eAAe,aAAa,KAAwB;IACzD,MAAM,WAAW,MAAM,cAAc,GAAG,QAAQ,QAAQ,CAAC,EAAE;QACzD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;YACnB,OAAO,cAAc,GAAG,EAAE,OAAO;YACjC,WAAW;gBAAE;YAAM;QACrB;IACF;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,KAAK,MAAM,EAAE;QACf,MAAM,IAAI,MAAM,KAAK,MAAM,CAAC,EAAE,CAAC,OAAO;IACxC;IAEA,MAAM,SAAS,KAAK,IAAI;IACxB,OAAO,OAAO,YAAY,CAAC,MAAM;AACnC;AAEO,eAAe,aAAa,EAAU,EAAE,KAAwB;IACrE,MAAM,WAAW,MAAM,cAAc,GAAG,QAAQ,QAAQ,CAAC,EAAE;QACzD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;YACnB,OAAO,cAAc,GAAG,EAAE,OAAO;YACjC,WAAW;gBAAE;gBAAI;YAAM;QACzB;IACF;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,KAAK,MAAM,EAAE;QACf,MAAM,IAAI,MAAM,KAAK,MAAM,CAAC,EAAE,CAAC,OAAO;IACxC;IAEA,MAAM,SAAS,KAAK,IAAI;IACxB,OAAO,OAAO,YAAY,CAAC,MAAM;AACnC;AAEO,eAAe,aAAa,EAAU;IAC3C,MAAM,WAAW,MAAM,cAAc,GAAG,QAAQ,QAAQ,CAAC,EAAE;QACzD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;YACnB,OAAO,cAAc,GAAG,EAAE,OAAO;YACjC,WAAW;gBAAE;YAAG;QAClB;IACF;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,KAAK,MAAM,EAAE;QACf,MAAM,IAAI,MAAM,KAAK,MAAM,CAAC,EAAE,CAAC,OAAO;IACxC;IAEA,MAAM,SAAS,KAAK,IAAI;IACxB,OAAO,OAAO,YAAY,CAAC,OAAO;AACpC", "debugId": null}}, {"offset": {"line": 403, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/lib/hooks/use-authors.ts"], "sourcesContent": ["import { useQuery, useMutation, useQueryClient } from \"@tanstack/react-query\";\r\nimport { getAuthors, getAuthor, createAuthor, updateAuthor, deleteAuthor } from \"@/lib/api/authors\";\r\nimport { Author, CreateAuthorInput, UpdateAuthorInput } from \"@/lib/graphql/generated/graphql\";\r\n\r\nexport function useAuthors(page: number = 1, pageSize: number = 10, filter?: string) {\r\n  return useQuery({\r\n    queryKey: [\"authors\", { page, pageSize, filter }],\r\n    queryFn: () => getAuthors(page, pageSize, filter),\r\n    staleTime: 5000, // Данные считаются актуальными в течение 5 секунд\r\n    placeholderData: (previousData) => previousData, // Используем предыдущие данные как заполнитель\r\n  });\r\n}\r\n\r\nexport function useAuthor(id: string) {\r\n  return useQuery({\r\n    queryKey: [\"author\", id],\r\n    queryFn: () => getAuthor(id),\r\n    enabled: !!id,\r\n  });\r\n}\r\n\r\nexport function useCreateAuthor() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (data: CreateAuthorInput) => createAuthor(data),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"authors\"] });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useUpdateAuthor() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: ({ id, data }: { id: string; data: UpdateAuthorInput }) =>\r\n      updateAuthor(id, data),\r\n    onSuccess: (_, variables) => {\r\n      queryClient.invalidateQueries({ queryKey: [\"authors\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"authors\", variables.id] });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useDeleteAuthor() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (id: string) => deleteAuthor(id),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"authors\"] });\r\n    },\r\n  });\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;AAAA;AACA;;;AAGO,SAAS,WAAW,OAAe,CAAC,EAAE,WAAmB,EAAE,EAAE,MAAe;IACjF,OAAO,CAAA,GAAA,2QAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAW;gBAAE;gBAAM;gBAAU;YAAO;SAAE;QACjD,SAAS,IAAM,CAAA,GAAA,4HAAA,CAAA,aAAU,AAAD,EAAE,MAAM,UAAU;QAC1C,WAAW;QACX,iBAAiB,CAAC,eAAiB;IACrC;AACF;AAEO,SAAS,UAAU,EAAU;IAClC,OAAO,CAAA,GAAA,2QAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAU;SAAG;QACxB,SAAS,IAAM,CAAA,GAAA,4HAAA,CAAA,YAAS,AAAD,EAAE;QACzB,SAAS,CAAC,CAAC;IACb;AACF;AAEO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sRAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8QAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,OAA4B,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD,EAAE;QACtD,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAU;YAAC;QACxD;IACF;AACF;AAEO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sRAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8QAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,EAAE,EAAE,EAAE,IAAI,EAA2C,GAChE,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD,EAAE,IAAI;QACnB,WAAW,CAAC,GAAG;YACb,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAU;YAAC;YACtD,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAW,UAAU,EAAE;iBAAC;YAAC;QACtE;IACF;AACF;AAEO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sRAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8QAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,KAAe,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD,EAAE;QACzC,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAU;YAAC;QACxD;IACF;AACF", "debugId": null}}, {"offset": {"line": 492, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/lib/schemas/author-schema.ts"], "sourcesContent": ["import { z } from \"zod\";\n\nexport const authorSchema = z.object({\n  name: z.string().min(1, \"Исем кирәк\"),\n  surName: z.string().optional(),\n  lastName: z.string().optional(),\n  displayName: z.string().min(1, \"Күрсәтелә торган исем кирәк\"),\n  urlPart: z.string().min(1, \"URL өлеше кирәк\").regex(/^[a-z0-9-]+$/, \"URL өлеше латин хәрефләре, саннар һәм \"-\" символыннан гына торырга тиеш\"),\n  biography: z.string().optional(),\n  birthDate: z.string().optional(),\n  deathDate: z.string().optional(),\n});\n\nexport type AuthorFormValues = z.infer<typeof authorSchema>;\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,eAAe,qLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,MAAM,qLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,SAAS,qLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC5B,UAAU,qLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,aAAa,qLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC/B,SAAS,qLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,mBAAmB,KAAK,CAAC,gBAAgB,2CAAyC;IAC7G,WAAW,qLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,WAAW,qLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,WAAW,qLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAChC", "debugId": null}}, {"offset": {"line": 513, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/lib/utils/format-date.ts"], "sourcesContent": ["// Названия месяцев на татарском языке\nconst tatarMonths = [\n  'гыйнвар', // январь\n  'февраль', // февраль\n  'март', // март\n  'апрель', // апрель\n  'май', // май\n  'июнь', // июнь\n  'июль', // июль\n  'август', // август\n  'сентябрь', // сентябрь\n  'октябрь', // октябрь\n  'ноябрь', // ноябрь\n  'декабрь', // декабрь\n];\n\nexport function formatDate(dateString: string): string {\n  if (!dateString) return \"-\";\n\n  const date = new Date(dateString);\n\n  if (isNaN(date.getTime())) {\n    return \"-\";\n  }\n\n  // Форматируем дату в формате \"день месяц год\"\n  const day = date.getDate();\n  const month = tatarMonths[date.getMonth()];\n  const year = date.getFullYear();\n\n  return `${day} ${month} ${year}`;\n}\n\nexport function formatDateForInput(dateString: string): string {\n  if (!dateString) return \"\";\n\n  const date = new Date(dateString);\n\n  if (isNaN(date.getTime())) {\n    return \"\";\n  }\n\n  return date.toISOString().split(\"T\")[0];\n}\n"], "names": [], "mappings": "AAAA,sCAAsC;;;;;AACtC,MAAM,cAAc;IAClB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAAS,WAAW,UAAkB;IAC3C,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,IAAI,KAAK;IAEtB,IAAI,MAAM,KAAK,OAAO,KAAK;QACzB,OAAO;IACT;IAEA,8CAA8C;IAC9C,MAAM,MAAM,KAAK,OAAO;IACxB,MAAM,QAAQ,WAAW,CAAC,KAAK,QAAQ,GAAG;IAC1C,MAAM,OAAO,KAAK,WAAW;IAE7B,OAAO,GAAG,IAAI,CAAC,EAAE,MAAM,CAAC,EAAE,MAAM;AAClC;AAEO,SAAS,mBAAmB,UAAkB;IACnD,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,IAAI,KAAK;IAEtB,IAAI,MAAM,KAAK,OAAO,KAAK;QACzB,OAAO;IACT;IAEA,OAAO,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;AACzC", "debugId": null}}, {"offset": {"line": 558, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/lib/utils/transliterate.ts"], "sourcesContent": ["/**\n * Транслитерация кириллицы в латиницу для создания URL-частей\n * @param text Текст на кириллице\n * @returns Транслитерированный текст на латинице\n */\nexport function transliterate(text: string): string {\n  const charMap: Record<string, string> = {\n    // Татарские буквы\n    'ә': 'a', 'Ә': 'a',\n    'ө': 'o', 'Ө': 'o',\n    'ү': 'u', 'Ү': 'u',\n    'җ': 'j', 'Җ': 'j',\n    'ң': 'n', 'Ң': 'n',\n    'һ': 'h', 'Һ': 'h',\n    'ғ': 'g', 'Ғ': 'g',\n    \n    // Русские буквы\n    'а': 'a', 'А': 'a',\n    'б': 'b', 'Б': 'b',\n    'в': 'v', 'В': 'v',\n    'г': 'g', 'Г': 'g',\n    'д': 'd', 'Д': 'd',\n    'е': 'e', 'Е': 'e',\n    'ё': 'e', 'Ё': 'e',\n    'ж': 'zh', 'Ж': 'zh',\n    'з': 'z', 'З': 'z',\n    'и': 'i', 'И': 'i',\n    'й': 'y', 'Й': 'y',\n    'к': 'k', 'К': 'k',\n    'л': 'l', 'Л': 'l',\n    'м': 'm', 'М': 'm',\n    'н': 'n', 'Н': 'n',\n    'о': 'o', 'О': 'o',\n    'п': 'p', 'П': 'p',\n    'р': 'r', 'Р': 'r',\n    'с': 's', 'С': 's',\n    'т': 't', 'Т': 't',\n    'у': 'u', 'У': 'u',\n    'ф': 'f', 'Ф': 'f',\n    'х': 'kh', 'Х': 'kh',\n    'ц': 'ts', 'Ц': 'ts',\n    'ч': 'ch', 'Ч': 'ch',\n    'ш': 'sh', 'Ш': 'sh',\n    'щ': 'sch', 'Щ': 'sch',\n    'ъ': '', 'Ъ': '',\n    'ы': 'y', 'Ы': 'y',\n    'ь': '', 'Ь': '',\n    'э': 'e', 'Э': 'e',\n    'ю': 'yu', 'Ю': 'yu',\n    'я': 'ya', 'Я': 'ya',\n  };\n\n  // Заменяем символы согласно карте\n  let result = '';\n  for (let i = 0; i < text.length; i++) {\n    const char = text[i];\n    result += charMap[char] || char;\n  }\n\n  // Заменяем пробелы и другие символы на дефисы\n  result = result\n    .toLowerCase()\n    .replace(/[^a-z0-9]+/g, '-')  // Заменяем все не-латинские символы и не-цифры на дефис\n    .replace(/^-+|-+$/g, '')      // Удаляем дефисы в начале и конце строки\n    .replace(/-{2,}/g, '-');      // Заменяем множественные дефисы на один\n\n  return result;\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AACM,SAAS,cAAc,IAAY;IACxC,MAAM,UAAkC;QACtC,kBAAkB;QAClB,KAAK;QAAK,KAAK;QACf,KAAK;QAAK,KAAK;QACf,KAAK;QAAK,KAAK;QACf,KAAK;QAAK,KAAK;QACf,KAAK;QAAK,KAAK;QACf,KAAK;QAAK,KAAK;QACf,KAAK;QAAK,KAAK;QAEf,gBAAgB;QAChB,KAAK;QAAK,KAAK;QACf,KAAK;QAAK,KAAK;QACf,KAAK;QAAK,KAAK;QACf,KAAK;QAAK,KAAK;QACf,KAAK;QAAK,KAAK;QACf,KAAK;QAAK,KAAK;QACf,KAAK;QAAK,KAAK;QACf,KAAK;QAAM,KAAK;QAChB,KAAK;QAAK,KAAK;QACf,KAAK;QAAK,KAAK;QACf,KAAK;QAAK,KAAK;QACf,KAAK;QAAK,KAAK;QACf,KAAK;QAAK,KAAK;QACf,KAAK;QAAK,KAAK;QACf,KAAK;QAAK,KAAK;QACf,KAAK;QAAK,KAAK;QACf,KAAK;QAAK,KAAK;QACf,KAAK;QAAK,KAAK;QACf,KAAK;QAAK,KAAK;QACf,KAAK;QAAK,KAAK;QACf,KAAK;QAAK,KAAK;QACf,KAAK;QAAK,KAAK;QACf,KAAK;QAAM,KAAK;QAChB,KAAK;QAAM,KAAK;QAChB,KAAK;QAAM,KAAK;QAChB,KAAK;QAAM,KAAK;QAChB,KAAK;QAAO,KAAK;QACjB,KAAK;QAAI,KAAK;QACd,KAAK;QAAK,KAAK;QACf,KAAK;QAAI,KAAK;QACd,KAAK;QAAK,KAAK;QACf,KAAK;QAAM,KAAK;QAChB,KAAK;QAAM,KAAK;IAClB;IAEA,kCAAkC;IAClC,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QACpC,MAAM,OAAO,IAAI,CAAC,EAAE;QACpB,UAAU,OAAO,CAAC,KAAK,IAAI;IAC7B;IAEA,8CAA8C;IAC9C,SAAS,OACN,WAAW,GACX,OAAO,CAAC,eAAe,KAAM,wDAAwD;KACrF,OAAO,CAAC,YAAY,IAAS,yCAAyC;KACtE,OAAO,CAAC,UAAU,MAAW,wCAAwC;IAExE,OAAO;AACT", "debugId": null}}, {"offset": {"line": 684, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/lib/utils/content-format.ts"], "sourcesContent": ["import { Block, PartialBlock, BlockNoteEditor } from '@blocknote/core';\r\nimport { toast } from 'sonner';\r\n\r\n/**\r\n * Checks if a string is HTML by looking for HTML tags\r\n * @param str String to check\r\n * @returns True if the string appears to be HTML\r\n */\r\nfunction isHtml(str: string): boolean {\r\n  // Simple heuristic: check if string contains HTML tags\r\n  const htmlTagRegex = /<[^>]*>/;\r\n  return htmlTagRegex.test(str.trim());\r\n}\r\n\r\n/**\r\n * Checks if a string is JSON by trying to parse it\r\n * @param str String to check\r\n * @returns True if the string is valid JSON\r\n */\r\nfunction isJson(str: string): boolean {\r\n  try {\r\n    JSON.parse(str);\r\n    return true;\r\n  } catch {\r\n    return false;\r\n  }\r\n}\r\n\r\n/**\r\n * Checks if a string is a BlockNote JSON value\r\n * @param str String to check\r\n * @returns True if the string appears to be BlockNote JSON\r\n */\r\nexport function isBlockNoteJson(str: string): boolean {\r\n  if (!isJson(str)) return false;\r\n  \r\n  try {\r\n    const parsed = JSON.parse(str);\r\n    return true;\r\n  } catch {\r\n    return false;\r\n  }\r\n}\r\n\r\n/**\r\n * Detects the format of content string\r\n * @param content Content string to analyze\r\n * @param contentFormat Optional format hint from server\r\n * @returns 'html' or 'json'\r\n */\r\nexport function detectContentFormat(content: string, contentFormat?: string): 'html' | 'json' {\r\n  // If we have a format hint from server, use it\r\n  if (contentFormat === 'Html') return 'html';\r\n  if (contentFormat === 'Blocks') return 'json';\r\n  \r\n  // Use heuristics to detect format\r\n  if (isBlockNoteJson(content)) return 'json';\r\n  if (isHtml(content)) return 'html';\r\n  \r\n  // Default to HTML for plain text or unknown format\r\n  return 'html';\r\n}\r\n\r\n/**\r\n * Converts HTML to BlockNote blocks using the editor's async parser\r\n * @param html HTML string\r\n * @param editor BlockNoteEditor instance\r\n * @returns Promise resolving to array of BlockNote blocks (partial blocks)\r\n */\r\nexport async function htmlToBlockNote(html: string, editor: BlockNoteEditor): Promise<PartialBlock[]> {\r\n  console.log('[htmlToBlockNote] Input HTML:', html);\r\n  try {\r\n    const blocks = await editor.tryParseHTMLToBlocks(html);\r\n    console.log('[htmlToBlockNote] Parsed blocks:', blocks);\r\n    if (!blocks || blocks.length === 0) {\r\n      return [{ type: 'paragraph', content: [] }];\r\n    }\r\n    return blocks;\r\n  } catch (e) {\r\n    console.error('[htmlToBlockNote] Error parsing HTML to BlockNote blocks:', e);\r\n    toast.error('Хаталы HTML: Эчтәлекне редакторга күчереп булмады.');\r\n    return [{ type: 'paragraph', content: [] }];\r\n  }\r\n}\r\n\r\n\r\n/**\r\n * Prepares content for saving to server\r\n * @param blocks BlockNote blocks (can be partial)\r\n * @returns JSON string representation of blocks\r\n */\r\nexport function prepareContentForSave(blocks: PartialBlock[]): string {\r\n  return JSON.stringify(blocks);\r\n}\r\n\r\n/**\r\n * Prepares content for loading into BlockNote editor (async)\r\n * @param content Content string from server\r\n * @param editor BlockNoteEditor instance\r\n * @param contentFormat Optional format hint from server\r\n * @returns Promise resolving to array of BlockNote blocks (partial blocks for editor)\r\n */\r\nexport async function prepareContentForEditor(content: string, editor: BlockNoteEditor, contentFormat?: string): Promise<PartialBlock[]> {\r\n  console.log('[prepareContentForEditor] Raw content:', content);\r\n  if (!content || content.trim() === '') {\r\n    console.log('[prepareContentForEditor] Content is empty, returning default paragraph.');\r\n    return [{ type: 'paragraph', content: [] }];\r\n  }\r\n\r\n  const format = detectContentFormat(content, contentFormat);\r\n  console.log('[prepareContentForEditor] Detected format:', format);\r\n\r\n  if (format === 'json') {\r\n    try {\r\n      const parsed = JSON.parse(content) as PartialBlock[];\r\n      console.log('[prepareContentForEditor] Parsed JSON blocks:', parsed);\r\n      return parsed;\r\n    } catch (error) {\r\n      console.error('[prepareContentForEditor] Error parsing BlockNote JSON:', error);\r\n      // Fallback to HTML conversion\r\n      return await htmlToBlockNote(content, editor);\r\n    }\r\n  } else if (format === 'html') {\r\n    const blocks = await htmlToBlockNote(content, editor);\r\n    console.log('[prepareContentForEditor] Blocks from HTML:', blocks);\r\n    return blocks;\r\n  } else {\r\n    // Fallback: treat as plain text\r\n    console.log('[prepareContentForEditor] Treating as plain text.');\r\n    return [{ type: 'paragraph', content: [{ type: 'text', text: content, styles: {} }] }];\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AACA;;AAEA;;;;CAIC,GACD,SAAS,OAAO,GAAW;IACzB,uDAAuD;IACvD,MAAM,eAAe;IACrB,OAAO,aAAa,IAAI,CAAC,IAAI,IAAI;AACnC;AAEA;;;;CAIC,GACD,SAAS,OAAO,GAAW;IACzB,IAAI;QACF,KAAK,KAAK,CAAC;QACX,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAOO,SAAS,gBAAgB,GAAW;IACzC,IAAI,CAAC,OAAO,MAAM,OAAO;IAEzB,IAAI;QACF,MAAM,SAAS,KAAK,KAAK,CAAC;QAC1B,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAQO,SAAS,oBAAoB,OAAe,EAAE,aAAsB;IACzE,+CAA+C;IAC/C,IAAI,kBAAkB,QAAQ,OAAO;IACrC,IAAI,kBAAkB,UAAU,OAAO;IAEvC,kCAAkC;IAClC,IAAI,gBAAgB,UAAU,OAAO;IACrC,IAAI,OAAO,UAAU,OAAO;IAE5B,mDAAmD;IACnD,OAAO;AACT;AAQO,eAAe,gBAAgB,IAAY,EAAE,MAAuB;IACzE,QAAQ,GAAG,CAAC,iCAAiC;IAC7C,IAAI;QACF,MAAM,SAAS,MAAM,OAAO,oBAAoB,CAAC;QACjD,QAAQ,GAAG,CAAC,oCAAoC;QAChD,IAAI,CAAC,UAAU,OAAO,MAAM,KAAK,GAAG;YAClC,OAAO;gBAAC;oBAAE,MAAM;oBAAa,SAAS,EAAE;gBAAC;aAAE;QAC7C;QACA,OAAO;IACT,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,6DAA6D;QAC3E,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACZ,OAAO;YAAC;gBAAE,MAAM;gBAAa,SAAS,EAAE;YAAC;SAAE;IAC7C;AACF;AAQO,SAAS,sBAAsB,MAAsB;IAC1D,OAAO,KAAK,SAAS,CAAC;AACxB;AASO,eAAe,wBAAwB,OAAe,EAAE,MAAuB,EAAE,aAAsB;IAC5G,QAAQ,GAAG,CAAC,0CAA0C;IACtD,IAAI,CAAC,WAAW,QAAQ,IAAI,OAAO,IAAI;QACrC,QAAQ,GAAG,CAAC;QACZ,OAAO;YAAC;gBAAE,MAAM;gBAAa,SAAS,EAAE;YAAC;SAAE;IAC7C;IAEA,MAAM,SAAS,oBAAoB,SAAS;IAC5C,QAAQ,GAAG,CAAC,8CAA8C;IAE1D,IAAI,WAAW,QAAQ;QACrB,IAAI;YACF,MAAM,SAAS,KAAK,KAAK,CAAC;YAC1B,QAAQ,GAAG,CAAC,iDAAiD;YAC7D,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2DAA2D;YACzE,8BAA8B;YAC9B,OAAO,MAAM,gBAAgB,SAAS;QACxC;IACF,OAAO,IAAI,WAAW,QAAQ;QAC5B,MAAM,SAAS,MAAM,gBAAgB,SAAS;QAC9C,QAAQ,GAAG,CAAC,+CAA+C;QAC3D,OAAO;IACT,OAAO;QACL,gCAAgC;QAChC,QAAQ,GAAG,CAAC;QACZ,OAAO;YAAC;gBAAE,MAAM;gBAAa,SAAS;oBAAC;wBAAE,MAAM;wBAAQ,MAAM;wBAAS,QAAQ,CAAC;oBAAE;iBAAE;YAAC;SAAE;IACxF;AACF", "debugId": null}}, {"offset": {"line": 811, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/components/editor/blocknote-editor.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useMemo, useRef, useState } from 'react';\r\nimport { BlockNoteEditor, PartialBlock } from '@blocknote/core';\r\nimport { BlockNoteView } from '@blocknote/mantine';\r\nimport { useCreateBlockNote } from '@blocknote/react';\r\nimport '@blocknote/core/fonts/inter.css';\r\nimport '@blocknote/mantine/style.css';\r\nimport { prepareContentForEditor } from '@/lib/utils/content-format';\r\n\r\ninterface BlockNoteEditorProps {\r\n  /**\r\n   * Initial content for the editor\r\n   */\r\n  initialContent?: PartialBlock[];\r\n  \r\n  /**\r\n   * Called when the editor content changes\r\n   */\r\n  onChange?: (blocks: PartialBlock[]) => void;\r\n  \r\n  /**\r\n   * Whether the editor is editable\r\n   */\r\n  editable?: boolean;\r\n  \r\n  /**\r\n   * Placeholder text to display when editor is empty\r\n   */\r\n  placeholder?: string;\r\n  \r\n  /**\r\n   * Additional CSS classes\r\n   */\r\n  className?: string;\r\n}\r\n\r\nexport function BlockNoteEditorComponent({\r\n  initialContent,\r\n  onChange,\r\n  editable = true,\r\n  placeholder = \"Эчтәлек языгыз...\",\r\n  className = \"\",\r\n}: BlockNoteEditorProps) {\r\n  // Create the editor instance\r\n  const editor = useCreateBlockNote({\r\n    initialContent,\r\n  });\r\n\r\n  // Handle content changes\r\n  const handleChange = () => {\r\n    if (onChange) {\r\n      onChange(editor.document);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={`blocknote-editor ${className}`} style={{ position: 'relative', zIndex: 1 }}>\r\n      <BlockNoteView\r\n        editor={editor}\r\n        onChange={handleChange}\r\n        editable={editable}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\n// Export a simpler interface for form usage\r\nexport interface EditorFieldProps {\r\n  /**\r\n   * Current value of the editor (array of blocks)\r\n   */\r\n  value?: PartialBlock[];\r\n  \r\n  /**\r\n   * Called when the editor value changes\r\n   */\r\n  onChange?: (value: PartialBlock[]) => void;\r\n  \r\n  /**\r\n   * Placeholder text to display when editor is empty\r\n   */\r\n  placeholder?: string;\r\n  \r\n  /**\r\n   * Additional CSS classes\r\n   */\r\n  className?: string;\r\n}\r\n\r\nexport function BlockNoteEditorField({\r\n  value,\r\n  onChange,\r\n  placeholder = \"Эчтәлек языгыз...\",\r\n  className = \"\",\r\n}: EditorFieldProps) {\r\n  // Ensure we have a default value\r\n  const initialContent = useMemo(() => {\r\n    if (value && value.length > 0) {\r\n      return value;\r\n    }\r\n    return [\r\n      {\r\n        type: 'paragraph',\r\n        content: [],\r\n      },\r\n    ] as PartialBlock[];\r\n  }, [value]);\r\n\r\n  return (\r\n    <BlockNoteEditorComponent\r\n      initialContent={initialContent}\r\n      onChange={onChange}\r\n      placeholder={placeholder}\r\n      className={className}\r\n    />\r\n  );\r\n}\r\n\r\nexport function SafeBlockNoteEditorField({ value, onChange, contentFormat, ...props }: {\r\n  value: string | PartialBlock[] | undefined,\r\n  onChange: (blocks: PartialBlock[]) => void,\r\n  contentFormat?: string,\r\n  [key: string]: any\r\n}) {\r\n  const [blocks, setBlocks] = useState<PartialBlock[] | undefined>(undefined);\r\n\r\n  useEffect(() => {\r\n    let cancelled = false;\r\n    async function loadBlocks() {\r\n      if (typeof value === 'string') {\r\n        const tempEditor = BlockNoteEditor.create();\r\n        const parsed = await prepareContentForEditor(value, tempEditor, contentFormat);\r\n        if (!cancelled) setBlocks(parsed);\r\n      } else if (Array.isArray(value)) {\r\n        setBlocks(value);\r\n      } else {\r\n        setBlocks([\r\n          {\r\n            type: 'paragraph',\r\n            content: [],\r\n          },\r\n        ]);\r\n      }\r\n    }\r\n    loadBlocks();\r\n    return () => { cancelled = true; };\r\n  }, [value, contentFormat]);\r\n\r\n  if (!blocks) return <div>Loading...</div>;\r\n\r\n  return <BlockNoteEditorField value={blocks} onChange={onChange} {...props} />;\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AAGA;AARA;;;;;;;;;AAqCO,SAAS,yBAAyB,EACvC,cAAc,EACd,QAAQ,EACR,WAAW,IAAI,EACf,cAAc,mBAAmB,EACjC,YAAY,EAAE,EACO;IACrB,6BAA6B;IAC7B,MAAM,SAAS,CAAA,GAAA,qXAAA,CAAA,qBAAkB,AAAD,EAAE;QAChC;IACF;IAEA,yBAAyB;IACzB,MAAM,eAAe;QACnB,IAAI,UAAU;YACZ,SAAS,OAAO,QAAQ;QAC1B;IACF;IAEA,qBACE,wbAAC;QAAI,WAAW,CAAC,iBAAiB,EAAE,WAAW;QAAE,OAAO;YAAE,UAAU;YAAY,QAAQ;QAAE;kBACxF,cAAA,wbAAC,mXAAA,CAAA,gBAAa;YACZ,QAAQ;YACR,UAAU;YACV,UAAU;;;;;;;;;;;AAIlB;AAyBO,SAAS,qBAAqB,EACnC,KAAK,EACL,QAAQ,EACR,cAAc,mBAAmB,EACjC,YAAY,EAAE,EACG;IACjB,iCAAiC;IACjC,MAAM,iBAAiB,CAAA,GAAA,+YAAA,CAAA,UAAO,AAAD,EAAE;QAC7B,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;YAC7B,OAAO;QACT;QACA,OAAO;YACL;gBACE,MAAM;gBACN,SAAS,EAAE;YACb;SACD;IACH,GAAG;QAAC;KAAM;IAEV,qBACE,wbAAC;QACC,gBAAgB;QAChB,UAAU;QACV,aAAa;QACb,WAAW;;;;;;AAGjB;AAEO,SAAS,yBAAyB,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,OAK7E;IACC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,+YAAA,CAAA,WAAQ,AAAD,EAA8B;IAEjE,CAAA,GAAA,+YAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY;QAChB,eAAe;YACb,IAAI,OAAO,UAAU,UAAU;gBAC7B,MAAM,aAAa,mTAAA,CAAA,kBAAe,CAAC,MAAM;gBACzC,MAAM,SAAS,MAAM,CAAA,GAAA,wIAAA,CAAA,0BAAuB,AAAD,EAAE,OAAO,YAAY;gBAChE,IAAI,CAAC,WAAW,UAAU;YAC5B,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ;gBAC/B,UAAU;YACZ,OAAO;gBACL,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS,EAAE;oBACb;iBACD;YACH;QACF;QACA;QACA,OAAO;YAAQ,YAAY;QAAM;IACnC,GAAG;QAAC;QAAO;KAAc;IAEzB,IAAI,CAAC,QAAQ,qBAAO,wbAAC;kBAAI;;;;;;IAEzB,qBAAO,wbAAC;QAAqB,OAAO;QAAQ,UAAU;QAAW,GAAG,KAAK;;;;;;AAC3E", "debugId": null}}, {"offset": {"line": 940, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/components/authors/author-form.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useForm } from \"react-hook-form\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  Form,\r\n  FormControl,\r\n  FormDescription,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  FormMessage,\r\n} from \"@/components/ui/form\";\r\nimport { EditorField } from \"@/components/editor/author-editor-field\";\r\nimport { BlockNoteEditorField } from \"@/components/editor/blocknote-editor\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { useCreateAuthor, useUpdateAuthor } from \"@/lib/hooks/use-authors\";\r\nimport { authorSchema } from \"@/lib/schemas/author-schema\";\r\nimport { Author } from \"@/lib/types\";\r\nimport { useState } from \"react\";\r\nimport { toast, Toaster } from \"sonner\";\r\nimport { formatDateForInput } from \"@/lib/utils/format-date\";\r\nimport { transliterate } from \"@/lib/utils/transliterate\";\r\nimport { DndProvider } from 'react-dnd';\r\nimport { HTML5Backend } from 'react-dnd-html5-backend';\r\nimport { BlockNoteEditor } from '@blocknote/core';\r\nimport { useEffect } from 'react';\r\nimport { PartialBlock } from '@blocknote/core';\r\nimport { prepareContentForEditor, isBlockNoteJson } from '@/lib/utils/content-format';\r\nimport { SafeBlockNoteEditorField } from '@/components/editor/blocknote-editor';\r\n\r\ninterface AuthorFormProps {\r\n  author?: Author;\r\n}\r\n\r\nexport function AuthorForm({ author }: AuthorFormProps) {\r\n  const router = useRouter();\r\n  const createAuthor = useCreateAuthor();\r\n  const updateAuthor = useUpdateAuthor();\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n\r\n  const form = useForm<typeof authorSchema._type>({\r\n    resolver: zodResolver(authorSchema),\r\n    defaultValues: author\r\n      ? {\r\n          name: author.name,\r\n          surName: author.surName || \"\",\r\n          lastName: author.lastName || \"\",\r\n          displayName: author.displayName,\r\n          urlPart: author.urlPart || \"\",\r\n          biography: author.biography || \"\",\r\n          birthDate: author.birthDate ? formatDateForInput(author.birthDate) : \"\",\r\n          deathDate: author.deathDate ? formatDateForInput(author.deathDate) : \"\",\r\n        }\r\n      : {\r\n          name: \"\",\r\n          surName: \"\",\r\n          lastName: \"\",\r\n          displayName: \"\",\r\n          urlPart: \"\",\r\n          biography: \"\",\r\n          birthDate: \"\",\r\n          deathDate: \"\",\r\n        },\r\n  });\r\n\r\n  async function onSubmit(values: typeof authorSchema._type) {\r\n    setIsSubmitting(true);\r\n    try {\r\n      // Преобразуем строковые даты в объекты Date или null\r\n      const formattedValues = {\r\n        ...values,\r\n        content: values.content, // Сохраняем JSON напрямую        \r\n        birthDate: values.birthDate ? new Date(values.birthDate).toISOString() : null,\r\n        deathDate: values.deathDate ? new Date(values.deathDate).toISOString() : null,\r\n      };\r\n\r\n      if (author) {\r\n        // При редактировании не отправляем urlPart, так как он не должен изменяться\r\n        const { urlPart, ...updateData } = formattedValues;\r\n        await updateAuthor.mutateAsync({\r\n          id: author.id,\r\n          data: updateData,\r\n        });\r\n        toast.success(\"Шагыйрь мәгълүматлары уңышлы үзгәртелде\");\r\n      } else {\r\n        // При создании отправляем urlPart\r\n        await createAuthor.mutateAsync(formattedValues);\r\n        toast.success(\"Яңа шагыйрь уңышлы өстәлде\");\r\n      }\r\n      router.push(\"/authors\");\r\n    } catch (error) {\r\n      console.error(\"Error submitting form:\", error);\r\n      toast.error(\"Хата: \" + (error instanceof Error ? error.message : \"Билгесез хата\"));\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  }\r\n\r\n  return (\r\n    <DndProvider backend={HTML5Backend}>\r\n    <Form {...form}>\r\n      <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-8\">\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n          <FormField\r\n            control={form.control}\r\n            name=\"name\"\r\n            render={({ field }) => (\r\n              <FormItem>\r\n                <FormLabel>Исем</FormLabel>\r\n                <FormControl>\r\n                  <Input placeholder=\"Габдулла\" {...field} />\r\n                </FormControl>\r\n                <FormMessage />\r\n              </FormItem>\r\n            )}\r\n          />\r\n          <FormField\r\n            control={form.control}\r\n            name=\"surName\"\r\n            render={({ field }) => (\r\n              <FormItem>\r\n                <FormLabel>Фамилия</FormLabel>\r\n                <FormControl>\r\n                  <Input placeholder=\"Тукай\" {...field} />\r\n                </FormControl>\r\n                <FormMessage />\r\n              </FormItem>\r\n            )}\r\n          />\r\n          <FormField\r\n            control={form.control}\r\n            name=\"lastName\"\r\n            render={({ field }) => (\r\n              <FormItem>\r\n                <FormLabel>Отчество</FormLabel>\r\n                <FormControl>\r\n                  <Input placeholder=\"Мөхәммәтгариф улы\" {...field} />\r\n                </FormControl>\r\n                <FormMessage />\r\n              </FormItem>\r\n            )}\r\n          />\r\n          <FormField\r\n            control={form.control}\r\n            name=\"displayName\"\r\n            render={({ field }) => (\r\n              <FormItem>\r\n                <FormLabel>Күрсәтелә торган исем</FormLabel>\r\n                <FormControl>\r\n                  <Input\r\n                    placeholder=\"Габдулла Тукай\"\r\n                    {...field}\r\n                    onChange={(e) => {\r\n                      field.onChange(e);\r\n                      // Если это новый автор, автоматически генерируем urlPart\r\n                      if (!author) {\r\n                        const transliteratedValue = transliterate(e.target.value);\r\n                        form.setValue(\"urlPart\", transliteratedValue);\r\n                      }\r\n                    }}\r\n                  />\r\n                </FormControl>\r\n                <FormDescription>\r\n                  Сайтта күрсәтелә торган тулы исем\r\n                </FormDescription>\r\n                <FormMessage />\r\n              </FormItem>\r\n            )}\r\n          />\r\n          <FormField\r\n            control={form.control}\r\n            name=\"urlPart\"\r\n            render={({ field }) => (\r\n              <FormItem>\r\n                <FormLabel>URL өлеше</FormLabel>\r\n                <FormControl>\r\n                  <Input\r\n                    placeholder=\"gabdulla-tukay\"\r\n                    {...field}\r\n                    readOnly={!!author} // Только для чтения при редактировании\r\n                    className={author ? \"bg-muted cursor-not-allowed\" : \"\"}\r\n                  />\r\n                </FormControl>\r\n                <FormDescription>\r\n                  {author\r\n                    ? \"URL өлешен үзгәртеп булмый\"\r\n                    : \"Латин хәрефләре, саннар һәм дефис символыннан гына торырга тиеш\"}\r\n                </FormDescription>\r\n                <FormMessage />\r\n              </FormItem>\r\n            )}\r\n          />\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n          <FormField\r\n            control={form.control}\r\n            name=\"birthDate\"\r\n            render={({ field }) => (\r\n              <FormItem>\r\n                <FormLabel>Туган көне</FormLabel>\r\n                <FormControl>\r\n                  <Input type=\"date\" {...field} />\r\n                </FormControl>\r\n                <FormMessage />\r\n              </FormItem>\r\n            )}\r\n          />\r\n          <FormField\r\n            control={form.control}\r\n            name=\"deathDate\"\r\n            render={({ field }) => (\r\n              <FormItem>\r\n                <FormLabel>Үлгән көне</FormLabel>\r\n                <FormControl>\r\n                  <Input type=\"date\" {...field} />\r\n                </FormControl>\r\n                <FormMessage />\r\n              </FormItem>\r\n            )}\r\n          />\r\n        </div>\r\n\r\n        <FormField\r\n          control={form.control}\r\n          name=\"biography\"\r\n          render={({ field }) => (\r\n              <FormItem>\r\n                <FormLabel>Биография</FormLabel>\r\n                <FormControl>\r\n                  <div className=\"space-y-4\">\r\n                    <SafeBlockNoteEditorField\r\n                      value={field.value}\r\n                      onChange={(blocks: PartialBlock[]) => {\r\n                        const jsonContent = JSON.stringify(blocks);\r\n                        field.onChange(jsonContent);\r\n                      }}\r\n                      placeholder=\"Шагыйрь турында мәгълүмат...\"\r\n                    />\r\n                    <Toaster />\r\n                  </div>\r\n                </FormControl>\r\n                <FormMessage />\r\n              </FormItem>\r\n          )}\r\n        />\r\n\r\n        <div className=\"flex justify-end gap-4\">\r\n          <Button\r\n            type=\"button\"\r\n            variant=\"outline\"\r\n            onClick={() => router.push(\"/authors\")}\r\n          >\r\n            Кире кайту\r\n          </Button>\r\n          <Button type=\"submit\" disabled={isSubmitting}>\r\n            {isSubmitting\r\n              ? \"Саклана...\"\r\n              : author\r\n              ? \"Үзгәртүләрне саклау\"\r\n              : \"Шагыйрьне өстәү\"}\r\n          </Button>\r\n        </div>\r\n      </form>\r\n    </Form>\r\n    </DndProvider>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAWA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAKA;AA/BA;;;;;;;;;;;;;;;;;AAqCO,SAAS,WAAW,EAAE,MAAM,EAAmB;IACpD,MAAM,SAAS,CAAA,GAAA,4UAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,+YAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,OAAO,CAAA,GAAA,uPAAA,CAAA,UAAO,AAAD,EAA6B;QAC9C,UAAU,CAAA,GAAA,+RAAA,CAAA,cAAW,AAAD,EAAE,yIAAA,CAAA,eAAY;QAClC,eAAe,SACX;YACE,MAAM,OAAO,IAAI;YACjB,SAAS,OAAO,OAAO,IAAI;YAC3B,UAAU,OAAO,QAAQ,IAAI;YAC7B,aAAa,OAAO,WAAW;YAC/B,SAAS,OAAO,OAAO,IAAI;YAC3B,WAAW,OAAO,SAAS,IAAI;YAC/B,WAAW,OAAO,SAAS,GAAG,CAAA,GAAA,qIAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO,SAAS,IAAI;YACrE,WAAW,OAAO,SAAS,GAAG,CAAA,GAAA,qIAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO,SAAS,IAAI;QACvE,IACA;YACE,MAAM;YACN,SAAS;YACT,UAAU;YACV,aAAa;YACb,SAAS;YACT,WAAW;YACX,WAAW;YACX,WAAW;QACb;IACN;IAEA,eAAe,SAAS,MAAiC;QACvD,gBAAgB;QAChB,IAAI;YACF,qDAAqD;YACrD,MAAM,kBAAkB;gBACtB,GAAG,MAAM;gBACT,SAAS,OAAO,OAAO;gBACvB,WAAW,OAAO,SAAS,GAAG,IAAI,KAAK,OAAO,SAAS,EAAE,WAAW,KAAK;gBACzE,WAAW,OAAO,SAAS,GAAG,IAAI,KAAK,OAAO,SAAS,EAAE,WAAW,KAAK;YAC3E;YAEA,IAAI,QAAQ;gBACV,4EAA4E;gBAC5E,MAAM,EAAE,OAAO,EAAE,GAAG,YAAY,GAAG;gBACnC,MAAM,aAAa,WAAW,CAAC;oBAC7B,IAAI,OAAO,EAAE;oBACb,MAAM;gBACR;gBACA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,kCAAkC;gBAClC,MAAM,aAAa,WAAW,CAAC;gBAC/B,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;YACA,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,WAAW,CAAC,iBAAiB,QAAQ,MAAM,OAAO,GAAG,eAAe;QAClF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,wbAAC,kTAAA,CAAA,cAAW;QAAC,SAAS,+PAAA,CAAA,eAAY;kBAClC,cAAA,wbAAC,gIAAA,CAAA,OAAI;YAAE,GAAG,IAAI;sBACZ,cAAA,wbAAC;gBAAK,UAAU,KAAK,YAAY,CAAC;gBAAW,WAAU;;kCACrD,wbAAC;wBAAI,WAAU;;0CACb,wbAAC,gIAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,wbAAC,gIAAA,CAAA,WAAQ;;0DACP,wbAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,wbAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,wbAAC,iIAAA,CAAA,QAAK;oDAAC,aAAY;oDAAY,GAAG,KAAK;;;;;;;;;;;0DAEzC,wbAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0CAIlB,wbAAC,gIAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,wbAAC,gIAAA,CAAA,WAAQ;;0DACP,wbAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,wbAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,wbAAC,iIAAA,CAAA,QAAK;oDAAC,aAAY;oDAAS,GAAG,KAAK;;;;;;;;;;;0DAEtC,wbAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0CAIlB,wbAAC,gIAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,wbAAC,gIAAA,CAAA,WAAQ;;0DACP,wbAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,wbAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,wbAAC,iIAAA,CAAA,QAAK;oDAAC,aAAY;oDAAqB,GAAG,KAAK;;;;;;;;;;;0DAElD,wbAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0CAIlB,wbAAC,gIAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,wbAAC,gIAAA,CAAA,WAAQ;;0DACP,wbAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,wbAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,wbAAC,iIAAA,CAAA,QAAK;oDACJ,aAAY;oDACX,GAAG,KAAK;oDACT,UAAU,CAAC;wDACT,MAAM,QAAQ,CAAC;wDACf,yDAAyD;wDACzD,IAAI,CAAC,QAAQ;4DACX,MAAM,sBAAsB,CAAA,GAAA,oIAAA,CAAA,gBAAa,AAAD,EAAE,EAAE,MAAM,CAAC,KAAK;4DACxD,KAAK,QAAQ,CAAC,WAAW;wDAC3B;oDACF;;;;;;;;;;;0DAGJ,wbAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;0DAGjB,wbAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0CAIlB,wbAAC,gIAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,wbAAC,gIAAA,CAAA,WAAQ;;0DACP,wbAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,wbAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,wbAAC,iIAAA,CAAA,QAAK;oDACJ,aAAY;oDACX,GAAG,KAAK;oDACT,UAAU,CAAC,CAAC;oDACZ,WAAW,SAAS,gCAAgC;;;;;;;;;;;0DAGxD,wbAAC,gIAAA,CAAA,kBAAe;0DACb,SACG,+BACA;;;;;;0DAEN,wbAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;kCAMpB,wbAAC;wBAAI,WAAU;;0CACb,wbAAC,gIAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,wbAAC,gIAAA,CAAA,WAAQ;;0DACP,wbAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,wbAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,wbAAC,iIAAA,CAAA,QAAK;oDAAC,MAAK;oDAAQ,GAAG,KAAK;;;;;;;;;;;0DAE9B,wbAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0CAIlB,wbAAC,gIAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,wbAAC,gIAAA,CAAA,WAAQ;;0DACP,wbAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,wbAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,wbAAC,iIAAA,CAAA,QAAK;oDAAC,MAAK;oDAAQ,GAAG,KAAK;;;;;;;;;;;0DAE9B,wbAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;kCAMpB,wbAAC,gIAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBACd,wbAAC,gIAAA,CAAA,WAAQ;;kDACP,wbAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,wbAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,wbAAC;4CAAI,WAAU;;8DACb,wbAAC,mJAAA,CAAA,2BAAwB;oDACvB,OAAO,MAAM,KAAK;oDAClB,UAAU,CAAC;wDACT,MAAM,cAAc,KAAK,SAAS,CAAC;wDACnC,MAAM,QAAQ,CAAC;oDACjB;oDACA,aAAY;;;;;;8DAEd,wbAAC,wQAAA,CAAA,UAAO;;;;;;;;;;;;;;;;kDAGZ,wbAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kCAKpB,wbAAC;wBAAI,WAAU;;0CACb,wbAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,SAAS,IAAM,OAAO,IAAI,CAAC;0CAC5B;;;;;;0CAGD,wbAAC,kIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAS,UAAU;0CAC7B,eACG,eACA,SACA,wBACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhB", "debugId": null}}, {"offset": {"line": 1489, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/lib/utils/use-dynamic-title.ts"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useRef } from \"react\";\n\n/**\n * Хук для динамического изменения заголовка страницы в клиентских компонентах\n *\n * @param title Заголовок страницы\n * @param suffix Суффикс заголовка (по умолчанию \"Шигърият.ру Админ\")\n */\nexport function useDynamicTitle(title: string | null | undefined, suffix: string = \"Шигърият.ру Админ\") {\n  // Используем ref для хранения предыдущего заголовка\n  const originalTitleRef = useRef<string | null>(null);\n\n  // Используем ref для отслеживания, был ли уже установлен заголовок\n  const isMountedRef = useRef(false);\n\n  useEffect(() => {\n    // Пропускаем выполнение на сервере\n    if (typeof document === 'undefined') return;\n\n    // Сохраняем оригинальный заголовок только при первом рендере\n    if (!isMountedRef.current) {\n      originalTitleRef.current = document.title;\n      isMountedRef.current = true;\n    }\n\n    // Не меняем заголовок, если title не определен\n    if (!title) return;\n\n    // Устанавливаем новый заголовок\n    const newTitle = suffix ? `${title} | ${suffix}` : title;\n\n    // Меняем заголовок только если он отличается от текущего\n    if (document.title !== newTitle) {\n      document.title = newTitle;\n    }\n\n    // Восстанавливаем оригинальный заголовок при размонтировании компонента\n    return () => {\n      if (originalTitleRef.current) {\n        document.title = originalTitleRef.current;\n      }\n    };\n  }, [title, suffix]);\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAUO,SAAS,gBAAgB,KAAgC,EAAE,SAAiB,mBAAmB;IACpG,oDAAoD;IACpD,MAAM,mBAAmB,CAAA,GAAA,+YAAA,CAAA,SAAM,AAAD,EAAiB;IAE/C,mEAAmE;IACnE,MAAM,eAAe,CAAA,GAAA,+YAAA,CAAA,SAAM,AAAD,EAAE;IAE5B,CAAA,GAAA,+YAAA,CAAA,YAAS,AAAD,EAAE;QACR,mCAAmC;QACnC,IAAI,OAAO,aAAa,aAAa;QAErC,6DAA6D;QAC7D,IAAI,CAAC,aAAa,OAAO,EAAE;YACzB,iBAAiB,OAAO,GAAG,SAAS,KAAK;YACzC,aAAa,OAAO,GAAG;QACzB;QAEA,+CAA+C;QAC/C,IAAI,CAAC,OAAO;QAEZ,gCAAgC;QAChC,MAAM,WAAW,SAAS,GAAG,MAAM,GAAG,EAAE,QAAQ,GAAG;QAEnD,yDAAyD;QACzD,IAAI,SAAS,KAAK,KAAK,UAAU;YAC/B,SAAS,KAAK,GAAG;QACnB;QAEA,wEAAwE;QACxE,OAAO;YACL,IAAI,iBAAiB,OAAO,EAAE;gBAC5B,SAAS,KAAK,GAAG,iBAAiB,OAAO;YAC3C;QACF;IACF,GAAG;QAAC;QAAO;KAAO;AACpB", "debugId": null}}, {"offset": {"line": 1533, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/app/%28admin%29/authors/%5Bid%5D/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { AuthorForm } from \"@/components/authors/author-form\";\nimport { useAuthor } from \"@/lib/hooks/use-authors\";\nimport { useParams } from \"next/navigation\";\nimport { useDynamicTitle } from \"@/lib/utils/use-dynamic-title\";\nimport { useMemo } from \"react\";\n\nexport default function EditAuthorPage() {\n  const params = useParams();\n  const id = params.id as string;\n  const { data: author, isLoading, error } = useAuthor(id);\n\n  // Мемоизируем заголовок, чтобы он не менялся при каждом рендере\n  const pageTitle = useMemo(() => {\n    return author ? `${author.displayName} үзгәртү` : \"Шагыйрь үзгәртү\";\n  }, [author?.displayName]);\n\n  // Устанавливаем заголовок страницы\n  useDynamicTitle(pageTitle);\n\n  if (isLoading) {\n    return <div className=\"text-center py-4\">Йөкләнә...</div>;\n  }\n\n  if (error || !author) {\n    return (\n      <div className=\"text-center py-4 text-red-500\">\n        Хата: {error?.message || \"Шагыйрь табылмады\"}\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <h1 className=\"text-3xl font-bold\">Шагыйрьне үзгәртү: {author.displayName}</h1>\n      <AuthorForm author={author} />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,4UAAA,CAAA,YAAS,AAAD;IACvB,MAAM,KAAK,OAAO,EAAE;IACpB,MAAM,EAAE,MAAM,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD,EAAE;IAErD,gEAAgE;IAChE,MAAM,YAAY,CAAA,GAAA,+YAAA,CAAA,UAAO,AAAD,EAAE;QACxB,OAAO,SAAS,GAAG,OAAO,WAAW,CAAC,QAAQ,CAAC,GAAG;IACpD,GAAG;QAAC,QAAQ;KAAY;IAExB,mCAAmC;IACnC,CAAA,GAAA,8IAAA,CAAA,kBAAe,AAAD,EAAE;IAEhB,IAAI,WAAW;QACb,qBAAO,wbAAC;YAAI,WAAU;sBAAmB;;;;;;IAC3C;IAEA,IAAI,SAAS,CAAC,QAAQ;QACpB,qBACE,wbAAC;YAAI,WAAU;;gBAAgC;gBACtC,OAAO,WAAW;;;;;;;IAG/B;IAEA,qBACE,wbAAC;QAAI,WAAU;;0BACb,wbAAC;gBAAG,WAAU;;oBAAqB;oBAAoB,OAAO,WAAW;;;;;;;0BACzE,wbAAC,+IAAA,CAAA,aAAU;gBAAC,QAAQ;;;;;;;;;;;;AAG1B", "debugId": null}}]}
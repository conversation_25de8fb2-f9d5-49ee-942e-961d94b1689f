"use client";

import { Work } from "@/lib/authors";
import Link from "next/link";
import { useState } from "react";

interface SearchFilterProps {
  works: Work[];
  author: string;
}

export default function SearchFilter({ works, author }: SearchFilterProps) {
  const [filter, setFilter] = useState("");

  const filteredWorks = works.filter((work) =>
    work.title.toLowerCase().includes(filter.toLowerCase())
  );

  return (
    <>
      <input
        type="text"
        placeholder="Шигырь эзләргә..."
        value={filter}
        onChange={(e) => setFilter(e.target.value)}
        className="w-full mb-4 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      />

      <ul className="list-disc list-inside">
        {filteredWorks.map(({ workId, title }) => (
          <li key={workId} className="mb-2">
            <Link href={`/authors/${author}/${workId}`} className="text-xl font-medium underline hover:text-blue-700">
              {title}
            </Link>
          </li>
        ))}
      </ul>
    </>
  );
} 
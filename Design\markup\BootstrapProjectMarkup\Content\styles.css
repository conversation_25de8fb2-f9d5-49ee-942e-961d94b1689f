﻿.fresh-column-header {
    color: rgb(0,175,80);
}

.popular-column-header {
    color: rgb(255,138,112);
}

.fresh-column-header, .popular-column-header {
    font-size: 11px;
    text-transform: uppercase;
    margin-top: 1em;
}

.authors-row {
    padding: 10px 0;
}

    .authors-row .author-block {
        width: 125px;
        padding: 10px;
        float: left;
        border: 2px solid transparent;
        position: relative;
        margin-left: -10px;
        margin-right: 10px;
    }

        .authors-row .author-block .meta-column {
            position: absolute;
            float: right;
            width: 20px;
            height: 130px;
            right: 8px;
        }

            .authors-row .author-block .meta-column .counter {
                position: absolute;
                bottom: 0;
                line-height: 1em;
            }

                .authors-row .author-block .meta-column .counter .counter-value {
                    font-size: 14px;
                }

        .authors-row .author-block .author-name {
            font-size: 1.3em;
            line-height: 1.1em;
            height: 2em;
            padding-bottom: 0.5em;
        }

            .authors-row .author-block .author-name a {
                text-decoration: underline;
            }

    .authors-row .most-bookmarked {
        border: 2px solid rgb(255, 207, 123);
        margin-left: 0;
    }

    .authors-row .most-viewed {
        border: 2px solid rgb(255, 171, 151);
        margin-left: 0;
    }

    .authors-row .most-commented {
        border: 2px solid rgb(127, 208, 255);
        margin-left: 0;
    }

.section-title h2 {
    font-size: 30px;
    font-family: Arial;
    overflow: hidden;
}

    .section-title h2 a {
        white-space: nowrap;
        color: inherit;
    }

.poems-row {
    padding: 10px 0;
}

    .poems-row .poem-block {
        padding: 8px;
        float: left;
        border: 2px solid transparent;
        max-width: 250px;
        overflow-x: hidden;
        position: relative;
        margin-left: -8px;
        margin-right: 10px;
    }

        .poems-row .poem-block .poem-text {
            font-size: 17px;
            white-space: nowrap;
        }

            /*затенение градиентом*/
            .poems-row .poem-block .poem-text:after {
                content: "";
                pointer-events: none;
                position: absolute;
                width: 100px;
                height: 100%;
                top: 0;
                right: 0;
                background-image: -webkit-linear-gradient(right, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
                background-image: -moz-linear-gradient(right, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
                background-image: -ms-linear-gradient(right, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
                background-image: -o-linear-gradient(right, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
                background-image: linear-gradient(to left, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
            }


        .poems-row .poem-block .author-name {
            padding-bottom: .5em;
        }

            .poems-row .poem-block .author-name a {
                color: rgb(153, 153, 153);
                padding-bottom: 0.5em;
                text-decoration: underline;
            }


        .poems-row .poem-block .poem-name {
            font-size: 1.3em;
            line-height: 1.1em;
        }

            .poems-row .poem-block .poem-name a {
                text-decoration: underline;
            }

    .poems-row .most-bookmarked {
        border: 3px double rgb(255, 207, 123);
        /*выделенные блоки могут быть больше*/
        max-width: 350px;
        margin-left: 0;
    }

        /*отменяем затенение градиентом*/
        .poems-row .most-bookmarked .poem-text:after {
            background-image: none;
        }

    .poems-row .most-viewed {
        border: 2px solid rgb(255, 171, 151);
        /*выделенные блоки могут быть больше*/
        max-width: 350px;
        margin-left: 0;
    }

        /*отменяем затенение градиентом*/
        .poems-row .most-viewed .poem-text:after {
            background-image: none;
        }

    .poems-row .most-commented {
        border: 2px solid rgb(127, 208, 255);
        /*выделенные блоки могут быть больше*/
        max-width: 350px;
        margin-left: 0;
    }

        /*отменяем затенение градиентом*/
        .poems-row .most-commented .poem-text:after {
            background-image: none;
        }


    .poems-row .poem-block .block-footer {
        margin: 5px 0 0;
        vertical-align: baseline;
    }

        .poems-row .poem-block .block-footer .counter {
            float: right;
        }

.counter .counter-value {
    vertical-align: top;
    font-size: 14px;
    margin-left: -4px;
}

body {
    /*default font*/
    font-family: "Times New Roman", Times;
    font-size: 15px;
}

.container-fluid {
    margin-top: 4em;
}

.no-top-margin-row {
    margin-top: -4em;
}

footer {
    padding: 2em 0;
}

.navbar .brand {
    padding: 5px 20px;
}

a.script-link {
    color: inherit;
    text-decoration: underline;
    text-decoration-style: dashed;
}



/* bootstrap bugfix: append, prepend add unnesesary bottom margin*/
.navbar .input-append, .navbar .input-prepend {
    margin-bottom: 0;
}

/*additional bootstrap invisible responsive styles*/

.invisible-desktop {
    visibility: hidden !important;
}

.visible-phone {
    visibility: hidden !important;
}

.visible-tablet {
    visibility: hidden !important;
}

.visible-desktop {
    visibility: inherit !important;
}


@media (min-width: 768px) and (max-width: 979px) {
    .invisible-desktop {
        visibility: inherit !important;
    }

    .visible-desktop {
        visibility: hidden !important;
    }

    .visible-tablet {
        visibility: inherit !important;
    }

    .invisible-tablet {
        visibility: hidden !important;
    }
}

@media (max-width: 767px) {
    .invisible-desktop {
        visibility: inherit !important;
    }

    .visible-desktop {
        visibility: hidden !important;
    }

    .visible-phone {
        visibility: inherit !important;
    }

    .invisible-phone {
        visibility: hidden !important;
    }
}

/*author-info*/
.author-info {
    padding-bottom: 3em;
}

    .author-info .photo {
        float: left;
        width: 130px;
    }

        .author-info .photo img {
            margin-right: 2em;
        }

    .author-info .details {
        margin-left: 130px;
    }

        .author-info .details h1 {
            margin-top: 0;
        }

        .author-info .details .author-themes span {
            vertical-align: text-bottom;
            white-space: nowrap;
        }

        .author-info .details .author-registered-since {
            margin: 1em 0;
        }

/*filter*/
.filter h3, .filter ul {
    display: inline;
}

.filter ul {
    margin-left: 3em;
}

    .filter ul li {
        margin-left: .5em;
    }


.filter a, .ajax-link {
    text-decoration: none;
    border-bottom: 1px dashed;
}

.filter .selected {
    background: rgb(245, 238, 208);
    border-radius: 3px;
}

a.edit-link {
    text-decoration: none;
    border-bottom: 1px dashed gray;
    color: gray;
}

.audio-block {
    margin-top: 2em;
}

.poem-page .counter {
    margin-top: 3em;
}

.tag-page .poem-block {
    height: 120px;
}
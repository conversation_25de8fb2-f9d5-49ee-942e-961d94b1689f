import Layout from "@/components/layout";
import { getAuthorWorksList } from "@/lib/authors";
import SearchFilter from "./SearchFilter";

interface AuthorPageProps {
  params: {
    author: string;
  };
}

export default async function AuthorPage({ params }: AuthorPageProps) {
  const { author } = await params;
  const works = await getAuthorWorksList(author);
  const authorName = works.length > 0 ? works[0].authorName : '';

  return (
    <Layout>
      <h2 className="text-3xl font-bold text-gray-800 mb-4">
        {authorName} <span className="text-xl font-normal">шигырьләре</span>
      </h2>

      <SearchFilter works={works} author={author} />      
    </Layout>
  );
} 
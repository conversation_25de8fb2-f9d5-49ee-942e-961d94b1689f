/**
 * Транслитерация кириллицы в латиницу для создания URL-частей
 * @param text Текст на кириллице
 * @returns Транслитерированный текст на латинице
 */
export function transliterate(text: string): string {
  const charMap: Record<string, string> = {
    // Татарские буквы
    'ә': 'a', 'Ә': 'a',
    'ө': 'o', 'Ө': 'o',
    'ү': 'u', 'Ү': 'u',
    'җ': 'j', 'Җ': 'j',
    'ң': 'n', 'Ң': 'n',
    'һ': 'h', 'Һ': 'h',
    'ғ': 'g', 'Ғ': 'g',
    
    // Русские буквы
    'а': 'a', 'А': 'a',
    'б': 'b', 'Б': 'b',
    'в': 'v', 'В': 'v',
    'г': 'g', 'Г': 'g',
    'д': 'd', 'Д': 'd',
    'е': 'e', 'Е': 'e',
    'ё': 'e', 'Ё': 'e',
    'ж': 'zh', 'Ж': 'zh',
    'з': 'z', 'З': 'z',
    'и': 'i', 'И': 'i',
    'й': 'y', 'Й': 'y',
    'к': 'k', 'К': 'k',
    'л': 'l', 'Л': 'l',
    'м': 'm', 'М': 'm',
    'н': 'n', 'Н': 'n',
    'о': 'o', 'О': 'o',
    'п': 'p', 'П': 'p',
    'р': 'r', 'Р': 'r',
    'с': 's', 'С': 's',
    'т': 't', 'Т': 't',
    'у': 'u', 'У': 'u',
    'ф': 'f', 'Ф': 'f',
    'х': 'kh', 'Х': 'kh',
    'ц': 'ts', 'Ц': 'ts',
    'ч': 'ch', 'Ч': 'ch',
    'ш': 'sh', 'Ш': 'sh',
    'щ': 'sch', 'Щ': 'sch',
    'ъ': '', 'Ъ': '',
    'ы': 'y', 'Ы': 'y',
    'ь': '', 'Ь': '',
    'э': 'e', 'Э': 'e',
    'ю': 'yu', 'Ю': 'yu',
    'я': 'ya', 'Я': 'ya',
  };

  // Заменяем символы согласно карте
  let result = '';
  for (let i = 0; i < text.length; i++) {
    const char = text[i];
    result += charMap[char] || char;
  }

  // Заменяем пробелы и другие символы на дефисы
  result = result
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')  // Заменяем все не-латинские символы и не-цифры на дефис
    .replace(/^-+|-+$/g, '')      // Удаляем дефисы в начале и конце строки
    .replace(/-{2,}/g, '-');      // Заменяем множественные дефисы на один

  return result;
}

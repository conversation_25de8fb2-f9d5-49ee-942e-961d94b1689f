"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export function AuthCheck({ children }: { children: React.ReactNode }) {
  const { status } = useSession();
  const router = useRouter();
  const [isChecking, setIsChecking] = useState(true);
  const [isMounted, setIsMounted] = useState(false);

  // Устанавливаем isMounted в true после первого рендера
  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    // Пропускаем проверку до монтирования компонента
    if (!isMounted) return;

    const checkAuth = () => {
      // Временное решение: проверяем cookie
      const cookies = document.cookie.split('; ');
      const adminAuthCookie = cookies.find(cookie => cookie.startsWith('admin-auth='));

      if (status === "unauthenticated" && !adminAuthCookie) {
        router.push("/login");
      }

      setIsChecking(false);
    };

    if (status !== "loading") {
      checkAuth();
    }
  }, [status, router, isMounted]);

  // Если компонент не смонтирован, возвращаем детей без проверки
  if (!isMounted) {
    return <>{children}</>;
  }

  if (status === "loading" || isChecking) {
    return <div className="flex items-center justify-center min-h-screen">Йөкләнә...</div>;
  }

  if (status === "unauthenticated") {
    // Временное решение: проверяем cookie
    const cookies = document.cookie.split('; ');
    const adminAuthCookie = cookies.find(cookie => cookie.startsWith('admin-auth='));

    if (!adminAuthCookie) {
      return null;
    }
  }

  return <>{children}</>;
}

import { PoemCard } from "./poem-card"

// Mock data for poems by category
const poemsByCategory = {
  love: [
    {
      id: "1",
      title: "Алтын көлгә җиде йөзем",
      author: "Газиз Кутуй",
      authorId: "101",
      indicators: { heart: true },
      text: "<p>Кайчагында шундый вакыт була,<br/>Үлем каршысында ятасың,<br/>Тамырларга бетмәс дәртләр тула,<br/>Килеп чыкса шунда якташың.</p>",
    },
    {
      id: "2",
      title: "Алма бирдем, ашадың",
      author: "Мансур Сәттаров",
      authorId: "102",
      indicators: { heart: true, star: true },
      count: 23,
      text: "<p>Алма бирдем, ашадың,<br/>Кабыгын кая ташладың?<br/>Нәчәлник булган көнеңнән<br/>Исәнләшми башладың.</p>",
    },
    {
      id: "3",
      title: "«Яшьлегем чәчәгең...»",
      author: "Зыя Мансур",
      authorId: "103",
      indicators: { star: true, moon: true },
      text: "",
    },
    {
      id: "4",
      title: "«Парасый вакыты шиңлеген...»",
      author: "Рәсим Гарай",
      authorId: "104",
      indicators: { sun: true, music: true },
      text: "",
    },
    {
      id: "5",
      title: "Син — горурлыгым, Казан",
      author: "Фәтхи Әминев",
      authorId: "105",
      indicators: { moon: true },
      text: "",
    },
  ],
  nature: [
    {
      id: "6",
      title: "Поезд чыкса бара",
      author: "Исхакъҗан Шафа",
      authorId: "106",
      indicators: { fire: true },
      text: "",
    },
    {
      id: "7",
      title: "Чабаталары кемгә тукмыйсың?",
      author: "Кави Нәҗми",
      authorId: "107",
      indicators: { leaf: true },
      text: "",
    },
    {
      id: "8",
      title: "Шагыйрь үлгәч",
      author: "Нур Баян",
      authorId: "108",
      indicators: { star: true },
      text: "",
    },
    {
      id: "9",
      title: "Кыркылыч таш",
      author: "Мансур Крымов",
      authorId: "109",
      indicators: { leaf: true },
      count: 45,
      text: "",
    },
    {
      id: "10",
      title: "Тәрәзенең ач та",
      author: "Кәрим Әминов",
      authorId: "110",
      indicators: { star: true },
      text: "",
    },
  ],
  civic: [
    {
      id: "11",
      title: "Гыйбрәт җыры",
      author: "Фәнил Исәнов",
      authorId: "111",
      indicators: { fire: true },
      text: "",
    },
    {
      id: "12",
      title: "Тыныч йокла",
      author: "Салман Хәйруллин",
      authorId: "112",
      indicators: { fire: true },
      text: "",
    },
    {
      id: "13",
      title: "Көрәш җырлары",
      author: "Әхтәм Бәрилов",
      authorId: "113",
      indicators: { fire: true },
       text: "",
   },
    {
      id: "14",
      title: "Нигә шатлык чирмадым",
      author: "Хәбра Рәхман",
      authorId: "114",
      indicators: { star: true },
       text: "",
   },
    {
      id: "15",
      title: "Азат",
      author: "Шәмси Маннур",
      authorId: "115",
      indicators: { sun: true },
      count: 51,
       text: "",
   },
  ],
}

interface PoemSectionProps {
  category: "love" | "nature" | "civic"
}

export function PoemSection({ category }: PoemSectionProps) {
  const poems = poemsByCategory[category] || []

  return (
    <div className="grid lg:grid-cols-[auto_auto_auto] md:grid-cols-[auto_auto] sm:grid-cols-[auto] gap-4 mb-10">
      {poems.map((poem) => (
        <PoemCard key={poem.id} {...poem} />
      ))}
    </div>
  )
}


﻿input, textarea {
	margin:0 0 0.25em 0;
	background:#fff;
	border:1px solid #aaa;
}

.search_form .search_box {
	width: 70%;
	border: solid 1px #dcdddd;
	outline: none;
}

fieldset, form, label, legend {
    background: none repeat scroll 0 0 transparent;
    border: 0 none;
    margin: 0;
    outline: 0 none;
    padding: 0;
    vertical-align: baseline;
}

fieldset {
    clear: both;
}

legend {
    font-size: 1.167em;
    font-weight: 700;
    padding: 0 0 1.286em;
}

fieldset fieldset legend {
    font-size: 1em;
    padding: 0 0 1.5em;
}
* html legend {
    margin-left: -7px;
}
* + html legend {
    margin-left: -7px;
}
form .field, form .buttons {
    clear: both;
    margin: 0 0 1.5em;
}
form .field label {
    display: block;
}
form ul.fields {
    margin: 0 0 1.5em;
    padding: 0;
}
form ul.fields li {
    list-style-type: none;
    margin: 0;
}
form ul.inline li, form ul.inline label {
    display: inline;
}
form ul.inline li {
    padding: 0 0.75em 0 0;
}
/*
input.radio, input.checkbox {
    vertical-align: top;
}
*/
label, button, input.submit, input.image {
    cursor: pointer;
}
* html input.radio, * html input.checkbox {
    vertical-align: middle;
}
* + html input.radio, * + html input.checkbox {
    vertical-align: middle;
}
textarea {
    overflow: auto;
}
input.text, input.password, textarea, select {
    font-size: 1em;
    margin: 0;
    vertical-align: baseline;
}
input.text, input.password, textarea {
    border: 1px solid #444444;
    padding: 2px;
}
form.horizontal .field {
    padding-left: 150px;
}
form.horizontal .field label {
    display: inline;
    float: left;
    margin-left: -150px;
    width: 140px;
}

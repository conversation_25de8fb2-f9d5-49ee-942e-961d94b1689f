{"$schema": "https://ui.shadcn.com/schema.json", "style": "new-york", "rsc": true, "tsx": true, "tailwind": {"config": "", "css": "src/app/globals.css", "baseColor": "neutral", "cssVariables": true, "prefix": ""}, "aliases": {"components": "@/components", "utils": "@/lib/utils", "ui": "@/components/ui", "lib": "@/lib", "hooks": "@/hooks"}, "iconLibrary": "lucide", "registries": {"plate": {"aliases": {"ui": "@/components/plate-ui"}, "url": "https://platejs.org/r"}}}
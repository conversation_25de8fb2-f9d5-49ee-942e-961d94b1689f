﻿using System;
using JetBrains.Annotations;
using Newtonsoft.Json;

namespace Shigriyat.Data
{
    /// <summary>
    /// Поэт, автор произведений
    /// </summary>
    public sealed class Author
    {
        public const string IdPrefix = "authors/";
        private string urlPart;

        /// <summary>
        /// Идентификатор: IdPrefix + UrlPart
        /// </summary>
        [NotNull]
        public string Id { get; private set; }
        
        [JsonIgnore]
        [NotNull]
        public string UrlPart
        {
            get
            {
                if (urlPart != null)
                    return urlPart;

                if (string.IsNullOrEmpty(Id))
                    return Id;
                if (Id.StartsWith(IdPrefix))
                    return Id.Substring(IdPrefix.Length);
                throw new InvalidOperationException("Id not start with prefix");
            }
            set
            {
                urlPart = value;
                Id = IdPrefix + value;
            }
        }

        [NotNull]
        public string Name { get; set; }
        [CanBeNull]
        public string SurName { get; set; }
        [CanBeNull]
        public string LastName { get; set; }
        [NotNull]
        public string DisplayName { get; set; }

        [CanBeNull]
        public string Biography { get; set; }

        public DateTime? BirthDate { get; set; }
        public DateTime? DeathDate { get; set; }
        
        public DateTime AddedDate { get; set; }
        public DateTime ModifiedDate { get; set; }

        /// <summary>
        /// Старый id (int). Удалить позже, если не пригодится.
        /// </summary>
        [Obsolete]
        public int LegacyId { get; set; }
    }
}

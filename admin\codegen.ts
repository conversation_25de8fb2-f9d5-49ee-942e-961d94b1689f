import { CodegenConfig } from '@graphql-codegen/cli';
const dotenv = require('dotenv');

// Загружаем переменные окружения из .env.local
dotenv.config({ path: '.env.local' });

const config: CodegenConfig = {
  schema: process.env.NEXT_PUBLIC_API_URL + '/graphql',
  documents: ['src/**/*.tsx', 'src/**/*.ts'],
  ignoreNoDocuments: true, // for better experience with the watcher
  generates: {
    './src/lib/graphql/': {
      preset: 'client',
      plugins: [
        'typescript',
        'typescript-operations'
      ],
      config: {
        skipTypename: false,
        withHooks: true,
        withHOC: false,
        withComponent: false,
        enumsAsTypes: true,
      },
    },
    './src/lib/graphql/schema.graphql': {
      plugins: ['schema-ast'],
    },
  },
};

export default config;

﻿@{
    ViewBag.Title = "Index";
}

@section bodyClass {
    @("poem-page")
}
<div class="row-fluid">
    <div class="span12">
        ← <a href="#">Равил Файзуллин</a>
        <h2>Новое произведение</h2>
    </div>
</div>

<div class="row-fluid">
    <div class="span8">
        <form class="form-horizontal">
            <div class="control-group">
                <label class="control-label" for="title">Название</label>
                <div class="controls">
                    <input type="text" id="title" class="span12">
                </div>
            </div>
            <div class="control-group">
                <label class="control-label" for="title">Текст</label>
                <div class="controls">
                    <textarea id="poemText" class="span12" rows="15">
                    </textarea>
                </div>
            </div>
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">Опубликовать</button>
                <button type="submit" class="btn">Отмена</button>
            </div>
        </form>
    </div>
    <div class="offset1 span3">
        <div class="author-themes">
            <div><span class="sh-icon-nature"></span>о природе</div>
            <div><span class="sh-icon-village"></span>деревне</div>
            <div><span class="sh-icon-love"></span>любви</div>
            <div><span class="sh-icon-family"></span>семье</div>
        </div>
        <a href="#" class="edit-link">Редактировать</a>

        <div class="audio-block">
            <div><strong>Озвучка</strong></div>
            <a href="http://audio.ilyabirman.ru/Ilya%20Birman%20-%20News.mp3" class="jouele">укый Салават Фатхетдинов</a>
            <ul class="inline">
                <li><a href="#" class="edit-link">Редактировать</a></li>
                <li><a href="#" class="edit-link">Удалить</a></li>
            </ul>
        </div>

        <div class="counter">
            <span class="sh-icon-pageview"></span>
            <span class="counter-value">2385</span>
            <span class="sh-icon-bookmark"></span>
            <span class="counter-value">33</span>
            <span class="sh-icon-comment"></span>
            <span class="counter-value">12</span>
        </div>
    </div>
</div>

@section styles
{
    <link rel="stylesheet" href="http://ilyabirman.ru/js/jouele/jouele.css" />
    <!-- x-editable (bootstrap) -->
    <link href="http://vitalets.github.io/x-editable/assets/x-editable/bootstrap-editable/css/bootstrap-editable.css" rel="stylesheet">
    <!-- wysihtml5 -->
    <link href="http://vitalets.github.io/x-editable/assets/x-editable/inputs-ext/wysihtml5/bootstrap-wysihtml5-0.0.2/bootstrap-wysihtml5-0.0.2.css" rel="stylesheet">
}

@section scripts
{
    <script src="http://ilyabirman.ru/js/jouele/jouele.js"></script>
    <script src="http://ilyabirman.ru/js/jouele/jquery.jplayer.min.js"></script>
    <!-- x-editable (bootstrap) -->
    <script src="http://vitalets.github.io/x-editable/assets/x-editable/bootstrap-editable/js/bootstrap-editable.js"></script>
    <!-- wysihtml5 -->
    <script src="http://vitalets.github.io/x-editable/assets/x-editable/inputs-ext/wysihtml5/bootstrap-wysihtml5-0.0.2/wysihtml5-0.3.0.min.js"></script>
    <script src="http://vitalets.github.io/x-editable/assets/x-editable/inputs-ext/wysihtml5/bootstrap-wysihtml5-0.0.2/bootstrap-wysihtml5-0.0.2.min.js"></script>
    <script src="http://vitalets.github.io/x-editable/assets/x-editable/inputs-ext/wysihtml5/wysihtml5.js"></script>
    <script>
        $('#poemText').wysihtml5({
            "font-styles": false, //Font styling, e.g. h1, h2, etc. Default true
            "emphasis": true, //Italics, bold, etc. Default true
            "lists": false, //(Un)ordered lists, e.g. Bullets, Numbers. Default true
            "html": true, //Button which allows you to edit the generated HTML. Default false
            "link": false, //Button to insert a link. Default true
            "image": false, //Button to insert an image. Default true,
            "color": false //Button to change color of font  
        });
    </script>
}

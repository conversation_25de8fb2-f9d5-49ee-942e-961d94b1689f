import "../styles/global.css";
import "tailwindcss/tailwind.css";
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Шигърият.ру",
  description: "Tatar poetry website",
};

export default function RootLayout({
  // Layouts must accept a children prop.
  // This will be populated with nested layouts or pages
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="tt" className="text-gray-900 leading-tight">
      <body className="min-h-screen">
        <header></header>
        <section>{children}</section>
        <footer>© 2006—{new Date().getFullYear()}, Shigriyat.ru</footer>
      </body>
    </html>
  );
}

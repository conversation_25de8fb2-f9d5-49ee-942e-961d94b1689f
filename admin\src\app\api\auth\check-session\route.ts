import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth/auth-options";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    console.log("Check session:", session ? "exists" : "null");
    
    if (!session) {
      return NextResponse.json({ authenticated: false }, { status: 401 });
    }
    
    return NextResponse.json({ 
      authenticated: true,
      user: session.user
    });
  } catch (error) {
    console.error("Check session error:", error);
    return NextResponse.json({ authenticated: false, error: "Session check failed" }, { status: 500 });
  }
}

import Head from "next/head";
import Image from "next/image";
import styles from "./layout.module.css";
import utilStyles from "../styles/utils.module.css";
import Link from "next/link";
import { <PERSON><PERSON> } from 'next/font/google'
import { <PERSON><PERSON><PERSON> } from 'next/font/google'
import { Merriweather } from 'next/font/google'
import React from 'react';
import { Footer } from "./footer";

const podkova = Podkova({
  subsets: ['cyrillic-ext', 'cyrillic']  
})

const roboto = Roboto({
  subsets: ['cyrillic-ext', 'cyrillic'],
  weight: ['300', '700'],
  variable: '--font-roboto'
})

const merriweather = Merriweather({
  subsets: ['cyrillic-ext', 'cyrillic'],
  weight: ['300', '700'],
  variable: '--font-merriweather'
})


const name = "Шигърият.ру";
export const siteTitle = "Шигърият.ру - Татар шигърияте, шигырьләр, шагыйрьләр. Татарская поэзия, татарские стихи, татарские поэты";

interface LayoutProps {
  home?: boolean;
  children: React.ReactNode;
}

export default function Layout({ children, home }: LayoutProps) {
  return (
    <div className={`${merriweather.className} ${styles.container}`}>
      <Head>
        <link rel="icon" href="/favicon.ico" />
        <title>{siteTitle}</title>
        <meta
          name="description"
          content="Шигърият.ру - Татар шигърияте, шигырьләр, шагыйрьләр. Татарская поэзия, татарские стихи, татарские поэты"
        />
        <meta
          property="og:image"
          content={`https://og-image.vercel.app/${encodeURI(
            siteTitle
          )}.png?theme=light&md=0&fontSize=75px&images=https%3A%2F%2Fassets.vercel.com%2Fimage%2Fupload%2Ffront%2Fassets%2Fdesign%2Fnextjs-black-logo.svg`}
        />
        <meta name="og:title" content={siteTitle} />
        <meta name="twitter:card" content="summary_large_image" />
      </Head>
      <header className={styles.header}>
        {home ? (
          <>
            <Link href="/">
              <Image
                priority
                src="/images/logo.gif"              
                height={60}
                width={230}
                alt={name}
              />
            </Link>
            {/* <h1 className={`${podkova.className} ${utilStyles.heading2Xl}`}>{name}</h1> */}
          </>
        ) : (
          <>
            <Link href="/">
              <Image
                priority
                src="/images/logo.gif"              
                height={60}
                width={230}
                alt={name}
              />
            </Link>
            <h2 className={`${podkova.className} ${utilStyles.headingLg}`}>
              <Link href="/" className={utilStyles.colorInherit}>
                {name}
              </Link>
            </h2>
          </>
        )}
      </header>

      <main>{children}</main>

      <Footer />
    </div>
  );
}

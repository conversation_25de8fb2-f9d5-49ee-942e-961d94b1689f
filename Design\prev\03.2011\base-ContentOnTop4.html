<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<!-- http://donothing.ru/seo-verstka/ -->
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
<title>Untitled Document</title>
<style type="text/css">
body {
	margin: 0;
	padding: 0;
	font-size: 10px;
	font-family:Verdana, Arial, Helvetica, sans-serif;
	line-height: 1.8em;
	background: #fff;
}

/*--Global Property--------------------------------------------*/

*{margin: 0; padding: 0;}
h1, h2, h3 { margin: 10px 0; padding: 10px 0; font-family: Georgia, "Times New Roman", Times, serif; font-weight: normal; border-bottom: 1px dashed #ddd;}
h1 {font-size: 3em;}
h2 {font-size: 2.5em;}
h3 {font-size: 2em;}
p {margin: 5px 0; padding: 5px 0;}

/*--Layout Property--------------------------------------------*/

#main {
	height: 100%;
	min-height: 724px;
	background: url(stretch.gif) repeat-y;
	position: relative;
	overflow: hidden;
}
*html #main { height: 724px; overflow: visible;}
.container {
	margin: 0 auto;
	width: 970px;
	font-size: 1.2em;
}

/*--Header Property---------------*/
#header {
	width: 968px;
	float: left;
	height: 120px;
	background: #ddd;
	position: absolute;
	top: 0;
	border: 1px solid #ccc;
	left: 0;
}
#header h2 {
	padding: 20px 20px;
	font-size: 3.5em;
}
#header ul {
	float: left;
	margin-left: 10px;
	list-style: none;
	display: inline;
}
#header ul li {
	float: left;
	margin: 0 5px;
}
#header ul li a{
	display: block;
	padding: 5px;
	background: #fff;
	border: 1px solid #ccc;
	text-decoration: none;
	color: #222;
}
#header ul li a:hover{
	background: #ddd;
	border: 1px solid #bbb;
}
/*--Side Col Property---------------*/
#sidecol {
	float: left;
	width: 235px;
	position: absolute;
	top: 150px;
	left: 0;
}
#sidecol h2 {
	padding: 10px;
	margin: 0;
	overflow: hidden;
	background:  #ccc;
	display: block;
	clear: both;
	font-weight: normal;
	font-size: 1.6em;
	border-top: 1px solid #bbb;
	border-bottom: 1px solid #bbb;
}
#sidecol ul  {
	list-style: none;
	margin: 0;
	padding: 0;
}
#sidecol ul  li {
	display: block;
	margin: 0;
	padding: 0;
}
#sidecol ul li a{
	display: block;
	width: 225px;
	padding: 7px 0 7px 10px;
	text-decoration: none;
	color: #222; 
	background: #ddd url(sidecol_a.gif) no-repeat right top;
	border-bottom: 1px solid #ccc;
	border-top: 1px solid #eee;
}
#sidecol ul  li :hover {
	border-top: 1px solid #eee;
	border-bottom: 1px solid #ccc;
	background: #ccc;
}
/*--Content Property---------------*/
#content {
	float: right;
	display: inline;
	width: 685px;
	margin: 120px 0 0 0;
	padding: 20px;
}
/*--Footer Property---------------*/
#footer {
	background: url(footer.gif) no-repeat;
	float: left;
	height: 56px;
	line-height: 56px;
	width: 100%;
	text-align: center;
}
</style>
</head>

<body>

<div id="main" class="container">
	<div id="content">
		<h1>Unique Content Comes First</h1>
		<p>Dignissim vero iriure illum, mos decet dolore, saepius ulciscor. Quis, pneum, vicis macto vereor, hendrerit quae reprobo jus eu. Si multo sit facilisi nibh multo, appellatio, at incassum nutus brevitas. </p>
		
		<p>Tation adipiscing tation comis roto dolor, dolus exerci, damnum praemitto antehabeo praesent in euismod. Exerci cogo occuro te saepius minim, eu luctus. Qui cogo eligo decet, gemino ingenium at, saepius autem ex. Nulla magna commodo nisl, proprius quis autem.</p> 
		
		
		<h2>Heading</h2>
		
		<p>Dignissim vero iriure illum, mos decet dolore, saepius ulciscor. Quis, pneum, vicis macto vereor, hendrerit quae reprobo jus eu. Si multo sit facilisi nibh multo, appellatio, at incassum nutus brevitas. </p>
		
		<h3>Heading</h3>
		<p>Tation adipiscing tation comis roto dolor, dolus exerci, damnum praemitto antehabeo praesent in euismod. Exerci cogo occuro te saepius minim, eu luctus. Qui cogo eligo decet, gemino ingenium at, saepius autem ex. Nulla magna commodo nisl, proprius quis autem.</p> 
		
		<p>Distineo paulatim diam te luptatum qui regula nulla iusto. Illum te eum ratis os vel duis. </p>

		<p>Dignissim vero iriure illum, mos decet dolore, saepius ulciscor. Quis, pneum, vicis macto vereor, hendrerit quae reprobo jus eu. Si multo sit facilisi nibh multo, appellatio, at incassum nutus brevitas. </p>
	</div>
	<div id="header">
		<h2>Header Comes Second</h2>
		<ul>
			<li><a href="#">Top Nav Link</a></li>
			<li><a href="#">Top Nav Link</a></li>
			<li><a href="#">Top Nav Link</a></li>
			<li><a href="#">Top Nav Link</a></li>
			<li><a href="#">Top Nav Link</a></li>
			<li><a href="#">Top Nav Link</a></li>
		</ul>
	</div>
	<div id="sidecol">
		<h2>Side-Col Comes Third</h2>
		<ul class="best">
			<li><a href="#">Sidecol Link</a></li>
			<li><a href="#">Sidecol Link</a></li>
			<li><a href="#">Sidecol Link</a></li>
			<li><a href="#">Sidecol Link</a></li>
		</ul>
		<h2>Link Heading</h2>
		<ul class="favorite">
			<li><a href="#">Sidecol Link</a></li>
			<li><a href="#">Sidecol Link</a></li>
			<li><a href="#">Sidecol Link</a></li>
			<li><a href="#">Sidecol Link</a></li>
		</ul>
		<h2>Link Heading</h2>
		<ul class="comments">
			<li><a href="#">Sidecol Link</a></li>
			<li><a href="#">Sidecol Link</a></li>
			<li><a href="#">Sidecol Link</a></li>
			<li><a href="#">Sidecol Link</a></li>
		</ul>
	</div>

</div>
<div class="container">
	<div id="footer">Footer Comes Last</div>
</div>

</body>
</html>

using System.Linq;
using System.Threading.Tasks;
using HotChocolate;
using HotChocolate.Data;
using HotChocolate.Types;
using HotChocolate.Types.Pagination;
using JetBrains.Annotations;
using Raven.Client.Documents.Session;
using Shigriyat.Data;

namespace Shigriyat.Api.GraphQL
{
    [UsedImplicitly]
    public class SchemaRoot
    {
        [UseOffsetPaging(IncludeTotalCount = true)]
        [UseProjection]
        [UseSorting]
        [UseFiltering]
        public IQueryable<Author> GetAuthors([Service] IAsyncDocumentSession session)
        {
            return session.Query<Author>();
        }
        
        public async Task<Author> GetAuthor(string id, [Service] IAsyncDocumentSession session)
        {
            return await session.LoadAsync<Author>(Author.IdPrefix + id);
        }
        
        [UseOffsetPaging(IncludeTotalCount = true)]
        [UseProjection]
        [UseSorting]
        [UseFiltering]
        public IQueryable<Work> GetWorks([Service] IAsyncDocumentSession session)
        {
            return session.Query<Work>();
        }

        public async Task<Work> GetWork(string id, [Service] IAsyncDocumentSession session)
        {
            return await session.LoadAsync<Work>(Work.IdPrefix + id);
        }

        [UsePaging]
        [UseProjection]
        [UseSorting]
        [UseFiltering]
        public IQueryable<Genre> GetGenres([Service] IAsyncDocumentSession session)
        {
            return session.Query<Genre>();
        }

        [UsePaging]
        [UseProjection]
        [UseSorting]
        [UseFiltering]
        public IQueryable<GuestbookMessage> GetGuestbookMessages([Service] IAsyncDocumentSession session)
        {
            return session.Query<GuestbookMessage>();
        }
    }
}
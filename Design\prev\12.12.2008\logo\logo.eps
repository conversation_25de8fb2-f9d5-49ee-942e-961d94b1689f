%!PS-Adobe-3.0 EPSF-3.0
%%BoundingBox: 217.24413 244.76939 624.64564 351.07200 
%%LanguageLevel: 1
%%Creator: CorelDRAW
%%Title: logo.eps
%%CreationDate: Fri Dec 12 15:31:19 2008
%%DocumentProcessColors: Black 
%%DocumentSuppliedResources: (atend)
%%EndComments
%%BeginProlog
/AutoFlatness false def
/AutoSteps 0 def
/CMYKMarks true def
/UseLevel 1 def
%Build: CorelDRAW Version 14.0.0.653
%Color profile: Disabled
/CorelIsEPS true def
%%BeginResource: procset wCorel14Dict 14.0 0
/wCorel14Dict 300 dict def wCorel14Dict begin
% Copyright (c)1992-2007 Corel Corporation
% All rights reserved.     v14 r0.0
/bd{bind def}bind def/ld{load def}bd/xd{exch def}bd/_ null def/rp{{pop}repeat}
bd/@cp/closepath ld/@gs/gsave ld/@gr/grestore ld/@np/newpath ld/Tl/translate ld
/$sv 0 def/@sv{/$sv save def}bd/@rs{$sv restore}bd/spg/showpage ld/showpage{}
bd currentscreen/@dsp xd/$dsp/@dsp def/$dsa xd/$dsf xd/$sdf false def/$SDF
false def/$Scra 0 def/SetScr/setscreen ld/@ss{2 index 0 eq{$dsf 3 1 roll 4 -1
roll pop}if exch $Scra add exch load SetScr}bd/SepMode_5 where{pop}{/SepMode_5
0 def}ifelse/CorelIsSeps where{pop}{/CorelIsSeps false def}ifelse
/CorelIsInRIPSeps where{pop}{/CorelIsInRIPSeps false def}ifelse/CorelIsEPS
where{pop}{/CorelIsEPS false def}ifelse/CurrentInkName_5 where{pop}
{/CurrentInkName_5(Composite)def}ifelse/$ink_5 where{pop}{/$ink_5 -1 def}
ifelse/fill_color 6 array def/num_fill_inks 1 def/$o 0 def/$fil 0 def
/outl_color 6 array def/num_outl_inks 1 def/$O 0 def/$PF false def/$bkg false
def/$op false def matrix currentmatrix/$ctm xd/$ptm matrix def/$ttm matrix def
/$stm matrix def/$ffpnt true def/CorelDrawReencodeVect[16#0/grave 16#5/breve
16#6/dotaccent 16#8/ring 16#A/hungarumlaut 16#B/ogonek 16#C/caron 16#D/dotlessi
16#27/quotesingle 16#60/grave 16#7C/bar 16#80/Euro
16#82/quotesinglbase/florin/quotedblbase/ellipsis/dagger/daggerdbl
16#88/circumflex/perthousand/Scaron/guilsinglleft/OE
16#91/quoteleft/quoteright/quotedblleft/quotedblright/bullet/endash/emdash
16#98/tilde/trademark/scaron/guilsinglright/oe 16#9F/Ydieresis
16#A1/exclamdown/cent/sterling/currency/yen/brokenbar/section
16#a8/dieresis/copyright/ordfeminine/guillemotleft/logicalnot/minus/registered/macron
16#b0/degree/plusminus/twosuperior/threesuperior/acute/mu/paragraph/periodcentered
16#b8/cedilla/onesuperior/ordmasculine/guillemotright/onequarter/onehalf/threequarters/questiondown
16#c0/Agrave/Aacute/Acircumflex/Atilde/Adieresis/Aring/AE/Ccedilla
16#c8/Egrave/Eacute/Ecircumflex/Edieresis/Igrave/Iacute/Icircumflex/Idieresis
16#d0/Eth/Ntilde/Ograve/Oacute/Ocircumflex/Otilde/Odieresis/multiply
16#d8/Oslash/Ugrave/Uacute/Ucircumflex/Udieresis/Yacute/Thorn/germandbls
16#e0/agrave/aacute/acircumflex/atilde/adieresis/aring/ae/ccedilla
16#e8/egrave/eacute/ecircumflex/edieresis/igrave/iacute/icircumflex/idieresis
16#f0/eth/ntilde/ograve/oacute/ocircumflex/otilde/odieresis/divide
16#f8/oslash/ugrave/uacute/ucircumflex/udieresis/yacute/thorn/ydieresis]def
/get_ps_level/languagelevel where{pop systemdict/languagelevel get exec}{1
}ifelse def/level2 get_ps_level 2 ge def/level3 get_ps_level 3 ge def
/is_distilling{/product where{pop systemdict/setdistillerparams known product
(Adobe PostScript Parser)ne and}{false}ifelse}bd/is_rip_separation{
is_distilling{false}{level2{currentpagedevice/Separations 2 copy known{get}{
pop pop false}ifelse}{false}ifelse}ifelse}bd/is_current_sep_color{
is_separation{gsave false setoverprint 1 1 1 1 5 -1 roll findcmykcustomcolor 1
setcustomcolor currentgray 0 eq grestore}{pop false}ifelse}bd
/get_sep_color_index{dup length 1 sub 0 1 3 -1 roll{dup 3 -1 roll dup 3 -1 roll
get is_current_sep_color{exit}{exch pop}ifelse}for pop -1}bd/is_separation{
/LumSepsDict where{pop false}{/AldusSepsDict where{pop false}{
is_rip_separation{true}{1 0 0 0 gsave setcmykcolor currentcmykcolor grestore
add add add 0 ne 0 1 0 0 gsave setcmykcolor currentcmykcolor grestore add add
add 0 ne 0 0 1 0 gsave setcmykcolor currentcmykcolor grestore add add add 0 ne
0 0 0 1 gsave setcmykcolor currentcmykcolor grestore add add add 0 ne and and
and not}ifelse}ifelse}ifelse}bind def/is_composite{is_separation not
is_distilling or}bd/is_sim_devicen{level2 level3 not and{is_distilling
is_rip_separation or}{false}ifelse}bd/@PL{/LV where{pop LV 2 ge level2 not and
{@np/Courier findfont 12 scalefont setfont 72 144 m
(The PostScript level set in the Corel application is higher than)show 72 132 m
(the PostScript level of this device. Change the PS Level in the Corel)show 72
120 m(application to Level 1 by selecting the PostScript tab in the print)show
72 108 m(dialog, and selecting Level 1 from the Compatibility drop down list.)
show flush spg quit}if}if}bd/@BeginSysCorelDict{systemdict/Corel30Dict known
{systemdict/Corel30Dict get exec}if systemdict/CorelLexDict known{1 systemdict
/CorelLexDict get exec}if}bd/@EndSysCorelDict{systemdict/Corel30Dict known
{end}if/EndCorelLexDict where{pop EndCorelLexDict}if}bd AutoFlatness{/@ifl{dup
currentflat exch sub 10 gt{
([Error: PathTooComplex; OffendingCommand: AnyPaintingOperator]\n)print flush
@np exit}{currentflat 2 add setflat}ifelse}bd/@fill/fill ld/fill{currentflat{
{@fill}stopped{@ifl}{exit}ifelse}bind loop setflat}bd/@eofill/eofill ld/eofill
{currentflat{{@eofill}stopped{@ifl}{exit}ifelse}bind loop setflat}bd/@clip
/clip ld/clip{currentflat{{@clip}stopped{@ifl}{exit}ifelse}bind loop setflat}
bd/@eoclip/eoclip ld/eoclip{currentflat{{@eoclip}stopped{@ifl}{exit}ifelse}
bind loop setflat}bd/@stroke/stroke ld/stroke{currentflat{{@stroke}stopped
{@ifl}{exit}ifelse}bind loop setflat}bd}if level2{/@ssa{true setstrokeadjust}
bd}{/@ssa{}bd}ifelse/d/setdash ld/j/setlinejoin ld/J/setlinecap ld/M
/setmiterlimit ld/w/setlinewidth ld/O{/$o xd}bd/R{/$O xd}bd/W/eoclip ld/c
/curveto ld/C/c ld/l/lineto ld/L/l ld/rl/rlineto ld/m/moveto ld/n/newpath ld/N
/newpath ld/P{11 rp}bd/u{}bd/U{}bd/A{pop}bd/q/@gs ld/Q/@gr ld/&{}bd/@j{@sv @np}
bd/@J{@rs}bd/g{1 exch sub 0 0 0 1 null 1 set_fill_color/$fil 0 def}bd/G{1 sub
neg 0 0 0 1 null 1 set_outline_color}bd/set_fill_color{/fill_color exch def
/num_fill_inks fill_color length 6 idiv def/bFillDeviceN num_fill_inks 1 gt def
/$fil 0 def}bd/set_outline_color{/outl_color exch def/num_outl_inks outl_color
length 6 idiv def/bOutlDeviceN num_outl_inks 1 gt def}bd
/get_devicen_color_names{dup length 6 idiv dup 5 mul exch getinterval}bd
/create_colorarray_from_devicen_params{/ink_names_temp exch def
/tint_params_temp exch def/ink_values_temp exch def[ink_values_temp aload pop
tint_params_temp aload pop ink_names_temp aload pop]}bd
/get_devicen_color_specs{dup length 6 idiv dup 4 mul getinterval}bd
/get_devicen_color{dup length 6 idiv 0 exch getinterval}bd/mult_devicen_color{
/colorarray exch def/mult_vals exch def 0 1 mult_vals length 1 sub{colorarray
exch dup mult_vals exch get exch dup colorarray exch get 3 -1 roll mul put}for
colorarray}bd/combine_devicen_colors{/colorarray2 exch def/colorarray1 exch def
/num_inks1 colorarray1 length 6 idiv def/num_inks2 colorarray2 length 6 idiv
def/num3 0 def/colorarray3[num_inks1 num_inks2 add 6 mul{0}repeat]def 0 1
num_inks1 1 sub{colorarray1 exch get colorarray3 num3 3 -1 roll put/num3 num3 1
add def}for 0 1 num_inks2 1 sub{colorarray2 exch get colorarray3 num3 3 -1 roll
put/num3 num3 1 add def}for colorarray1 num_inks1 dup 4 mul getinterval
colorarray3 num3 3 -1 roll putinterval/num3 num3 num_inks1 4 mul add def
colorarray2 num_inks2 dup 4 mul getinterval colorarray3 num3 3 -1 roll
putinterval/num3 num3 num_inks2 4 mul add def colorarray1 num_inks1 dup 5 mul
exch getinterval colorarray3 num3 3 -1 roll putinterval/num3 num3 num_inks1 add
def colorarray2 num_inks2 dup 5 mul exch getinterval colorarray3 num3 3 -1 roll
putinterval/num3 num3 num_inks2 add def colorarray3}bd/get_devicen_color_spec{
/colorant_index exch def/colorarray exch def/ncolorants colorarray length 6
idiv def[colorarray colorant_index get colorarray ncolorants colorant_index 4
mul add 4 getinterval aload pop colorarray ncolorants 5 mul colorant_index add
get]}bd/set_devicen_color{level3{/colorarray exch def/numcolorants colorarray
length 6 idiv def colorarray get_devicen_color_specs/tint_params exch def[
/DeviceN colorarray get_devicen_color_names/DeviceCMYK{tint_params
CorelTintTransformFunction}]setcolorspace colorarray get_devicen_color aload
pop setcolor}{/DeviceCMYK setcolorspace devicen_to_cmyk aload pop pop @tc_5
setprocesscolor_5}ifelse}bd/sf{/bmp_fill_fg_color xd}bd/i{dup 0 ne{setflat}
{pop}ifelse}bd/v{4 -2 roll 2 copy 6 -2 roll c}bd/V/v ld/y{2 copy c}bd/Y/y ld
/@w{matrix rotate/$ptm xd matrix scale $ptm dup concatmatrix/$ptm xd 1 eq{$ptm
exch dup concatmatrix/$ptm xd}if 1 w}bd/@g{1 eq dup/$sdf xd{/$scp xd/$sca xd
/$scf xd}if}bd/@G{1 eq dup/$SDF xd{/$SCP xd/$SCA xd/$SCF xd}if}bd/@D{2 index 0
eq{$dsf 3 1 roll 4 -1 roll pop}if 3 copy exch $Scra add exch load SetScr/$dsp
xd/$dsa xd/$dsf xd}bd/$ngx{$SDF{$SCF SepMode_5 0 eq{$SCA}{$dsa}ifelse $SCP @ss
}if}bd/@MN{2 copy le{pop}{exch pop}ifelse}bd/@MX{2 copy ge{pop}{exch pop}
ifelse}bd/InRange{3 -1 roll @MN @MX}bd/@sqr{dup 0 rl dup 0 exch rl neg 0 rl @cp
}bd/currentscale{1 0 dtransform matrix defaultmatrix idtransform dup mul exch
dup mul add sqrt 0 1 dtransform matrix defaultmatrix idtransform dup mul exch
dup mul add sqrt}bd/@unscale{}bd/wDstChck{2 1 roll dup 3 -1 roll eq{1 add}if}
bd/@dot{dup mul exch dup mul add 1 exch sub}bd/@lin{exch pop abs 1 exch sub}bd
/cmyk2rgb{3{dup 5 -1 roll add 1 exch sub dup 0 lt{pop 0}if exch}repeat pop}bd
/rgb2cmyk{3{1 exch sub 3 1 roll}repeat 3 copy @MN @MN 3{dup 5 -1 roll sub neg
exch}repeat}bd/rgb2g{2 index .299 mul 2 index .587 mul add 1 index .114 mul add
4 1 roll pop pop pop}bd/devicen_to_cmyk{/convertcolor exch def convertcolor
get_devicen_color aload pop convertcolor get_devicen_color_specs
CorelTintTransformFunction}bd/WaldoColor_5 where{pop}{/SetRgb/setrgbcolor ld
/GetRgb/currentrgbcolor ld/SetGry/setgray ld/GetGry/currentgray ld/SetRgb2
systemdict/setrgbcolor get def/GetRgb2 systemdict/currentrgbcolor get def
/SetHsb systemdict/sethsbcolor get def/GetHsb systemdict/currenthsbcolor get
def/rgb2hsb{SetRgb2 GetHsb}bd/hsb2rgb{3 -1 roll dup floor sub 3 1 roll SetHsb
GetRgb2}bd/setcmykcolor where{pop/LumSepsDict where{pop/SetCmyk_5{LumSepsDict
/setcmykcolor get exec}def}{/AldusSepsDict where{pop/SetCmyk_5{AldusSepsDict
/setcmykcolor get exec}def}{/SetCmyk_5/setcmykcolor ld}ifelse}ifelse}{
/SetCmyk_5{cmyk2rgb SetRgb}bd}ifelse/currentcmykcolor where{pop/GetCmyk
/currentcmykcolor ld}{/GetCmyk{GetRgb rgb2cmyk}bd}ifelse/setoverprint where
{pop}{/setoverprint{/$op xd}bd}ifelse/currentoverprint where{pop}{
/currentoverprint{$op}bd}ifelse/@tc_5{5 -1 roll dup 1 ge{pop}{4{dup 6 -1 roll
mul exch}repeat pop}ifelse}bd/@trp{exch pop 5 1 roll @tc_5}bd
/setprocesscolor_5{SepMode_5 0 eq{SetCmyk_5}{SepsColor not{4 1 roll pop pop pop
1 exch sub SetGry}{SetCmyk_5}ifelse}ifelse}bd/findcmykcustomcolor where{pop}{
/findcmykcustomcolor{5 array astore}bd}ifelse/Corelsetcustomcolor_exists false
def/setcustomcolor where{pop/Corelsetcustomcolor_exists true def}if CorelIsSeps
true eq CorelIsInRIPSeps false eq and{/Corelsetcustomcolor_exists false def}if
Corelsetcustomcolor_exists false eq{/setcustomcolor{exch aload pop SepMode_5 0
eq{pop @tc_5 setprocesscolor_5}{CurrentInkName_5 eq{4 index}{0}ifelse 6 1 roll
5 rp 1 sub neg SetGry}ifelse}bd}if/@scc_5{dup type/booleantype eq{dup
currentoverprint ne{setoverprint}{pop}ifelse}{1 eq setoverprint}ifelse dup _ eq
{pop setprocesscolor_5 pop}{dup(CorelRegistrationColor)eq{5 rp 1 exch sub
setregcolor}{findcmykcustomcolor exch setcustomcolor}ifelse}ifelse SepMode_5 0
eq{true}{GetGry 1 eq currentoverprint and not}ifelse}bd/colorimage where{pop
/ColorImage{colorimage}def}{/ColorImage{/ncolors xd/$multi xd $multi true eq{
ncolors 3 eq{/daqB xd/daqG xd/daqR xd pop pop exch pop abs{daqR pop daqG pop
daqB pop}repeat}{/daqK xd/daqY xd/daqM xd/daqC xd pop pop exch pop abs{daqC pop
daqM pop daqY pop daqK pop}repeat}ifelse}{/dataaq xd{dataaq ncolors dup 3 eq{
/$dat xd 0 1 $dat length 3 div 1 sub{dup 3 mul $dat 1 index get 255 div $dat 2
index 1 add get 255 div $dat 3 index 2 add get 255 div rgb2g 255 mul cvi exch
pop $dat 3 1 roll put}for $dat 0 $dat length 3 idiv getinterval pop}{4 eq{
/$dat xd 0 1 $dat length 4 div 1 sub{dup 4 mul $dat 1 index get 255 div $dat 2
index 1 add get 255 div $dat 3 index 2 add get 255 div $dat 4 index 3 add get
255 div cmyk2rgb rgb2g 255 mul cvi exch pop $dat 3 1 roll put}for $dat 0 $dat
length ncolors idiv getinterval}if}ifelse}image}ifelse}bd}ifelse/setcmykcolor{
1 5 1 roll null 6 array astore currentoverprint set_current_color/$ffpnt xd}bd
/currentcmykcolor{GetCmyk}bd/setrgbcolor{rgb2cmyk setcmykcolor}bd
/currentrgbcolor{currentcmykcolor cmyk2rgb}bd/sethsbcolor{hsb2rgb setrgbcolor}
bd/currenthsbcolor{currentrgbcolor rgb2hsb}bd/setgray{dup dup setrgbcolor}bd
/currentgray{currentrgbcolor rgb2g}bd/InsideDCS false def/IMAGE/image ld/image
{InsideDCS{IMAGE}{/EPSDict where{pop SepMode_5 0 eq{IMAGE}{dup type/dicttype eq
{dup/ImageType get 1 ne{IMAGE}{dup dup/BitsPerComponent get 8 eq exch
/BitsPerComponent get 1 eq or currentcolorspace 0 get/DeviceGray eq and{
CurrentInkName_5(Black)eq{IMAGE}{dup/DataSource get/TCC xd/Height get abs{TCC
pop}repeat}ifelse}{IMAGE}ifelse}ifelse}{2 index 1 ne{CurrentInkName_5(Black)eq
{IMAGE}{/TCC xd pop pop exch pop abs{TCC pop}repeat}ifelse}{IMAGE}ifelse}
ifelse}ifelse}{IMAGE}ifelse}ifelse}bd}ifelse/WaldoColor_5 true def
/WaldoColor_13 where{pop}{/separate_color{SepMode_5 0 ne{[exch/colorarray_sep
exch def/ink_num -1 def colorarray_sep length 6 idiv 1 gt{colorarray_sep
get_devicen_color_names dup length 1 sub 0 1 3 -1 roll{exch dup 3 -1 roll dup 3
1 roll get CurrentInkName_5 eq{/ink_num exch def}{pop}ifelse}for pop ink_num -1
ne{colorarray_sep ink_num get_devicen_color_spec aload pop pop SepsColor not{
pop pop pop pop 1 0 0 0 5 -1 roll}if null}{0 0 0 0 0 null}ifelse}{
colorarray_sep 5 get $ink_5 4 eq{CurrentInkName_5 eq{colorarray_sep aload pop
pop SepsColor not{pop pop pop pop 0 0 0 1}if null}{0 0 0 0 0 null}ifelse}{
colorarray_sep 0 get colorarray_sep $ink_5 1 add get 3 -1 roll null eq{0 0 0 4
-1 roll SepsColor{4 $ink_5 1 add roll}if null}{pop pop 0 0 0 0 0 null}ifelse
}ifelse}ifelse]}if}bd/separate_cmyk_color{$ink_5 -1 ne{[exch aload pop 3 $ink5
sub index/colorarray_sep exch def/ink_num -1 def colorarray_sep
get_devicen_color_names dup length 1 sub 0 1 3 -1 roll{exch dup 3 -1 roll dup 3
1 roll get CurrentInkName_5 eq{/ink_num exch def}{pop}ifelse}for pop ink_num -1
ne{[colorarray_sep ink_num get_devicen_color_spec aload pop]}{[0 0 0 0 0 null]
}ifelse}if}bd/set_current_color{dup type/booleantype eq{dup currentoverprint ne
{setoverprint}{pop}ifelse}{1 eq setoverprint}ifelse/cur_color exch def
/nNumColors cur_color length 6 idiv def nNumColors 1 eq{cur_color 5 get
(CorelRegistrationColor)eq{cur_color aload pop 5 rp 1 exch sub setregcolor}{
SepMode_5 0 eq{cur_color aload pop dup null eq{pop @tc_5 setprocesscolor_5}{
findcmykcustomcolor exch setcustomcolor}ifelse}{cur_color separate_color aload
pop pop @tc_5 setprocesscolor_5}ifelse}ifelse}{SepMode_5 0 eq{is_distilling
is_rip_separation or{cur_color set_devicen_color}{cur_color devicen_to_cmyk
setprocesscolor_5}ifelse}{cur_color separate_color aload pop pop @tc_5
setprocesscolor_5}ifelse}ifelse SepMode_5 0 eq{true}{GetGry 1 eq
currentoverprint and not}ifelse}bd}ifelse/WaldoColor_13 true def/$fm 0 def
/wfill{1 $fm eq{fill}{eofill}ifelse}bd/@Pf{@sv SepMode_5 0 eq $Psc 0 ne or
$ink_5 3 eq or{0 J 0 j[]0 d fill_color $o set_current_color pop $ctm setmatrix
72 1000 div dup matrix scale dup concat dup Bburx exch Bbury exch itransform
ceiling cvi/Bbury xd ceiling cvi/Bburx xd Bbllx exch Bblly exch itransform
floor cvi/Bblly xd floor cvi/Bbllx xd $Prm aload pop $Psn load exec}{1 SetGry
wfill}ifelse @rs @np}bd/F{matrix currentmatrix $sdf{$scf $sca $scp @ss}if $fil
1 eq{CorelPtrnDoFill}{$fil 2 eq{@ff}{$fil 3 eq{@Pf}{level3{fill_color $o
set_current_color{wfill}{@np}ifelse}{/overprint_flag $o def is_distilling
is_rip_separation or{0 1 num_fill_inks 1 sub{dup 0 gt{/overprint_flag true def
}if fill_color exch get_devicen_color_spec overprint_flag set_current_color{
@gs wfill @gr}{@np exit}ifelse}for}{fill_color overprint_flag set_current_color
{@gs wfill @gr}{@np}ifelse}ifelse}ifelse}ifelse}ifelse}ifelse $sdf{$dsf $dsa
$dsp @ss}if setmatrix}bd/f{@cp F}bd/S{matrix currentmatrix $ctm setmatrix $SDF
{$SCF $SCA $SCP @ss}if level3{outl_color $O set_current_color{matrix
currentmatrix $ptm concat stroke setmatrix}{@np}ifelse}{/overprint_flag $O def
is_distilling is_rip_separation or{0 1 num_outl_inks 1 sub{dup 0 gt{
/overprint_flag true def}if outl_color exch get_devicen_color_spec
overprint_flag set_current_color{matrix currentmatrix $ptm concat @gs stroke
@gr setmatrix}{@np exit}ifelse}for}{outl_color overprint_flag set_current_color
{matrix currentmatrix $ptm concat @gs stroke @gr setmatrix}{@np}ifelse}ifelse
}ifelse $SDF{$dsf $dsa $dsp @ss}if setmatrix}bd/s{@cp S}bd/B{@gs F @gr S}bd/b{
@cp B}bd/_E{5 array astore exch cvlit xd}bd/@cc{currentfile $dat readhexstring
pop}bd/@sm{/$ctm $ctm currentmatrix def}bd/@E{/Bbury xd/Bburx xd/Bblly xd
/Bbllx xd}bd/@c{@cp}bd/@P{/$fil 3 def/$Psn xd/$Psc xd array astore/$Prm xd}bd
/tcc{@cc}def/@B{@gs S @gr F}bd/@b{@cp @B}bd/@sep{CurrentInkName_5(Composite)eq
{/$ink_5 -1 def}{CurrentInkName_5(Cyan)eq{/$ink_5 0 def}{CurrentInkName_5
(Magenta)eq{/$ink_5 1 def}{CurrentInkName_5(Yellow)eq{/$ink_5 2 def}{
CurrentInkName_5(Black)eq{/$ink_5 3 def}{/$ink_5 4 def}ifelse}ifelse}ifelse}
ifelse}ifelse}bd/@whi{@gs -72000 dup m -72000 72000 l 72000 dup l 72000 -72000
l @cp 1 SetGry fill @gr}bd/@neg{[{1 exch sub}/exec cvx currenttransfer/exec
cvx]cvx settransfer @whi}bd/deflevel 0 def/@sax{/deflevel deflevel 1 add def}
bd/@eax{/deflevel deflevel dup 0 gt{1 sub}if def deflevel 0 gt{/eax load}{eax}
ifelse}bd/eax{{exec}forall}bd/@rax{deflevel 0 eq{@rs @sv}if}bd systemdict
/pdfmark known not{/pdfmark/cleartomark ld}if/wclip{1 $fm eq{clip}{eoclip}
ifelse}bd level2{/setregcolor{/neg_flag exch def[/Separation/All/DeviceCMYK{
dup dup dup}]setcolorspace 1.0 neg_flag sub setcolor}bd}{/setregcolor{1 exch
sub dup dup dup setcmykcolor}bd}ifelse/CorelTintTransformFunction{
/colorantSpecArray exch def/nColorants colorantSpecArray length 4 idiv def
/inColor nColorants 1 add 1 roll nColorants array astore def/outColor 4 array
def 0 1 3{/nOutInk exch def 1 0 1 nColorants 1 sub{dup inColor exch get exch 4
mul nOutInk add colorantSpecArray exch get mul 1 exch sub mul}for 1 exch sub
outColor nOutInk 3 -1 roll put}for outColor aload pop}bind def
% Copyright (c)1992-2007 Corel Corporation
% All rights reserved.     v14 r0.0
/@ii{concat 3 index 3 index m 3 index 1 index l 2 copy l 1 index 3 index l 3
index 3 index l clip pop pop pop pop}bd/@i{@sm @gs @ii 6 index 1 ne{/$frg true
def pop pop}{1 eq{bmp_fill_fg_color $O set_current_color/$frg xd}{/$frg false
def}ifelse 1 eq{@gs $ctm setmatrix F @gr}if}ifelse @np/$ury xd/$urx xd/$lly xd
/$llx xd/$bts xd/$hei xd/$wid xd/$dat $wid $bts mul 8 div ceiling cvi string
def $bkg $frg or{$SDF{$SCF $SCA $SCP @ss}if $llx $lly Tl $urx $llx sub $ury
$lly sub scale $bkg{fill_color set_current_color pop}if $wid $hei abs $bts 1 eq
{$bkg}{$bts}ifelse[$wid 0 0 $hei neg 0 $hei 0 gt{$hei}{0}ifelse]/tcc load $bts
1 eq{imagemask}{image}ifelse $SDF{$dsf $dsa $dsp @ss}if}{$hei abs{tcc pop}
repeat}ifelse @gr $ctm setmatrix}bd/@I{@sm @gs @ii @np/$ury xd/$urx xd/$lly xd
/$llx xd/$ncl xd/$bts xd/$hei xd/$wid xd $ngx $llx $lly Tl $urx $llx sub $ury
$lly sub scale $wid $hei abs $bts[$wid 0 0 $hei neg 0 $hei 0 gt{$hei}{0}ifelse
]/$dat $wid $bts mul $ncl mul 8 div ceiling cvi string def $msimage false eq
$ncl 1 eq or{/@cc load false $ncl ColorImage}{$wid $bts mul 8 div ceiling cvi
$ncl 3 eq{dup dup/$dat1 exch string def/$dat2 exch string def/$dat3 exch string
def/@cc1 load/@cc2 load/@cc3 load}{dup dup dup/$dat1 exch string def/$dat2 exch
string def/$dat3 exch string def/$dat4 exch string def/@cc1 load/@cc2 load
/@cc3 load/@cc4 load}ifelse true $ncl ColorImage}ifelse $SDF{$dsf $dsa $dsp
@ss}if @gr $ctm setmatrix}bd/@cc1{currentfile $dat1 readhexstring pop}bd/@cc2{
currentfile $dat2 readhexstring pop}bd/@cc3{currentfile $dat3 readhexstring pop
}bd/@cc4{currentfile $dat4 readhexstring pop}bd/$msimage false def/COMP 0 def
/MaskedImage false def/bImgDeviceN false def/nNumInksDeviceN 0 def
/sNamesDeviceN[]def/tint_params[]def level2{/@I_2{@sm @gs @ii @np/$ury xd/$urx
xd/$lly xd/$llx xd/$ncl xd/$bts xd/$hei xd/$wid xd/$dat $wid $bts mul $ncl mul
8 div ceiling cvi string def $ngx $ncl 1 eq{/DeviceGray}{$ncl 3 eq{/DeviceRGB}
{/DeviceCMYK}ifelse}ifelse setcolorspace $llx $lly Tl $urx $llx sub $ury $lly
sub scale 8 dict begin/ImageType 1 def/Width $wid def/Height $hei abs def
/BitsPerComponent $bts def/Decode $ncl 1 eq{[0 1]}{$ncl 3 eq{[0 1 0 1 0 1]}{[0
1 0 1 0 1 0 1]}ifelse}ifelse def/ImageMatrix[$wid 0 0 $hei neg 0 $hei 0 gt
{$hei}{0}ifelse]def/DataSource currentfile/ASCII85Decode filter COMP 1 eq
{/DCTDecode filter}{COMP 2 eq{/RunLengthDecode filter}{COMP 3 eq{/LZWDecode
filter}if}ifelse}ifelse def currentdict end image $SDF{$dsf $dsa $dsp @ss}if
@gr $ctm setmatrix}bd}{/@I_2{}bd}ifelse level2{/@I_2D{@sm @gs @ii @np/$ury xd
/$urx xd/$lly xd/$llx xd/$ncl xd/$bts xd/$hei xd/$wid xd $ngx/scanline $wid
$bts mul $ncl mul 8 div ceiling cvi string def/readscanline{currentfile
scanline readhexstring pop}bind def level3{[/DeviceN sNamesDeviceN/DeviceCMYK{
tint_params CorelTintTransformFunction}]setcolorspace $llx $lly Tl $urx $llx
sub $ury $lly sub scale 8 dict begin/ImageType 1 def/Width $wid def/Height $hei
abs def/BitsPerComponent $bts def/Decode[nNumInksDeviceN{0 1}repeat]def
/ImageMatrix[$wid 0 0 $hei neg 0 $hei 0 gt{$hei}{0}ifelse]def/DataSource{
readscanline}def currentdict end image}{/scanline_height $lly $ury sub 1 sub
$hei div def/plate_scanline $wid string def/cmyk_scanline $wid 4 mul string def
is_distilling is_rip_separation or{/bSimDeviceN true def}{/bSimDeviceN false
def}ifelse/scanline_img_dict 8 dict begin/ImageType 1 def/Width $wid def
/Height 1 def/BitsPerComponent $bts def/Decode bSimDeviceN{[0 1]}{[0 1 0 1 0 1
0 1]}ifelse def/ImageMatrix[$wid 0 0 1 neg 0 1]def/DataSource bSimDeviceN{
plate_scanline}{cmyk_scanline}ifelse def currentdict end def 0 1 $hei 1 sub{
@gs/nScanIndex exch def readscanline pop/$t_lly $ury $lly scanline_height
nScanIndex mul sub sub ceiling cvi def/$t_ury $t_lly scanline_height sub
ceiling cvi def bSimDeviceN{0 1 $ncl 1 sub{@gs/nInkIndex exch def 0 1
plate_scanline length 1 sub{dup $ncl mul nInkIndex add scanline exch get
plate_scanline 3 1 roll put}for[0 1 $ncl 1 sub{nInkIndex eq{1.0}{0.0}ifelse
}for]/sepTintTransformParams exch def[/Separation sNamesDeviceN nInkIndex get
/DeviceCMYK{sepTintTransformParams aload pop tint_params
CorelTintTransformFunction @tc_5}]setcolorspace $llx $t_lly Tl $urx $llx sub
$t_ury $t_lly sub scale nInkIndex 0 eq currentoverprint not and{false
setoverprint}{true setoverprint}ifelse scanline_img_dict image @gr}for}{0 1
$wid 1 sub{dup $ncl mul scanline exch $ncl getinterval 0 1 $ncl 1 sub{2 copy
get 255 div 3 1 roll pop}for pop tint_params CorelTintTransformFunction 5 -1
roll cmyk_scanline exch 0 1 3{3 1 roll 2 copy 5 -1 roll dup 8 exch sub index
255 mul cvi 3 1 roll exch 4 mul add exch put}for 6 rp}for/DeviceCMYK
setcolorspace $llx $t_lly Tl $urx $llx sub $t_ury $t_lly sub scale
scanline_img_dict image}ifelse @gr}for}ifelse $SDF{$dsf $dsa $dsp @ss}if @gr
$ctm setmatrix}bd}{/@I_2D{}bd}ifelse/@I_3{@sm @gs @ii @np/$ury xd/$urx xd/$lly
xd/$llx xd/$ncl xd/$bts xd/$hei xd/$wid xd/$dat $wid $bts mul $ncl mul 8 div
ceiling cvi string def $ngx bImgDeviceN{[/DeviceN sNamesDeviceN/DeviceCMYK{
tint_params CorelTintTransformFunction}]}{$ncl 1 eq{/DeviceGray}{$ncl 3 eq
{/DeviceRGB}{/DeviceCMYK}ifelse}ifelse}ifelse setcolorspace $llx $lly Tl $urx
$llx sub $ury $lly sub scale/ImageDataDict 8 dict def ImageDataDict begin
/ImageType 1 def/Width $wid def/Height $hei abs def/BitsPerComponent $bts def
/Decode[$ncl{0 1}repeat]def/ImageMatrix[$wid 0 0 $hei neg 0 $hei 0 gt{$hei}{0}
ifelse]def/DataSource currentfile/ASCII85Decode filter COMP 1 eq{/DCTDecode
filter}{COMP 2 eq{/RunLengthDecode filter}{COMP 3 eq{/LZWDecode filter}if}
ifelse}ifelse def end/MaskedImageDict 7 dict def MaskedImageDict begin
/ImageType 3 def/InterleaveType 3 def/MaskDict ImageMaskDict def/DataDict
ImageDataDict def end MaskedImageDict image $SDF{$dsf $dsa $dsp @ss}if @gr $ctm
setmatrix}bd/@SetMask{/$mbts xd/$mhei xd/$mwid xd/ImageMaskDict 8 dict def
ImageMaskDict begin/ImageType 1 def/Width $mwid def/Height $mhei abs def
/BitsPerComponent $mbts def/DataSource maskstream def/ImageMatrix[$mwid 0 0
$mhei neg 0 $mhei 0 gt{$mhei}{0}ifelse]def/Decode[1 0]def end}bd/@daq{dup type
/arraytype eq{{}forall}if}bd/@BMP{/@cc xd UseLevel 3 eq MaskedImage true eq and
{7 -2 roll pop pop @I_3}{12 index 1 gt UseLevel 2 eq UseLevel 3 eq or and{7 -2
roll pop pop bImgDeviceN{@I_2D}{@I_2}ifelse}{11 index 1 eq{12 -1 roll pop @i}{
7 -2 roll pop pop @I}ifelse}ifelse}ifelse}bd/disable_raster_output{/@BMP load
/old_raster_func exch bind def/@BMP{8 rp/$ury xd/$urx xd/$lly xd/$llx xd/$ncl
xd/$bts xd/$hei xd/$wid xd/scanline $wid $bts mul $ncl mul 8 div ceiling cvi
string def 0 1 $hei 1 sub{currentfile scanline readhexstring pop pop pop}for
}def}bd/enable_raster_output{/old_raster_func where{pop/old_raster_func load
/@BMP exch bd}if}bd
end
%%EndResource
%%EndProlog
%%BeginSetup
wCorel14Dict begin
@BeginSysCorelDict
1.00 setflat
/$fst 128 def
%%EndSetup

%%Page: 1 1
%%ViewingOrientation: 0 1 1 0
%LogicalPage: 1
%%BeginPageSetup
@sv
@sm
@sv
%%EndPageSetup
@rax %Note: Object
217.24413 254.14866 317.87263 302.60778 @E
/$fm 1 def
 0 O 0 @g
[ 1.00 0.00 0.00 0.00 1.00 null ] set_fill_color
308.66372 286.53250 m
298.43631 264.94328 289.40230 254.14866 281.56082 254.14866 c
278.12750 254.14866 276.41083 256.63635 276.41083 261.61172 c
276.41083 263.73572 277.15266 268.53647 278.62214 276.01427 C
281.18268 288.91814 L
276.20702 278.47276 271.02813 270.07852 265.61622 263.72126 C
260.21906 257.37817 255.56372 254.20677 251.63575 254.20677 c
247.31490 254.20677 245.16170 256.73811 245.16170 261.78633 c
245.16170 263.60476 245.80205 267.76545 247.08217 274.26841 C
248.40595 280.91679 L
249.20617 284.91761 249.59906 287.88548 249.59906 289.82013 c
249.59906 293.22454 248.39150 294.92646 245.97638 294.92646 c
240.52110 294.92646 232.14132 284.78665 220.82315 264.50674 C
217.24413 266.49978 L
229.58079 290.54778 240.34649 302.56441 249.55540 302.56441 c
255.44721 302.56441 258.38589 298.72346 258.38589 291.05688 c
258.38589 288.49635 257.81839 284.39376 256.66923 278.74913 C
255.47641 272.87178 L
254.67619 268.88570 254.28331 266.41247 254.28331 265.45238 c
254.28331 264.23036 254.90891 263.61950 256.16013 263.61950 c
259.22976 263.61950 264.72869 269.71483 272.65748 281.89162 C
279.84416 293.00627 283.43764 299.34907 283.43764 300.94951 c
283.43764 301.10939 283.37924 301.41496 283.26302 301.83676 C
283.67036 301.92406 284.35408 302.11342 285.35783 302.38980 C
285.77962 302.53521 286.41997 302.60778 287.27830 302.60778 c
289.91140 302.60778 291.23518 301.34211 291.23518 298.81077 c
291.23518 298.05449 290.81339 295.72668 289.95506 291.87156 C
289.64948 290.47493 289.32945 288.97654 289.02387 287.39083 C
287.23465 278.35654 L
285.92532 271.85357 285.27052 267.94006 285.27052 266.63074 c
285.27052 264.72501 285.96898 263.77937 287.36532 263.77937 c
290.42050 263.77937 294.94488 269.27858 300.90954 280.27672 C
306.84529 291.18784 309.81288 297.69080 309.81288 299.78561 c
309.81288 300.38202 309.58016 300.97843 309.12917 301.54592 C
310.70041 302.22964 312.12595 302.56441 313.43528 302.56441 c
316.38869 302.56441 317.87263 300.97843 317.87263 297.79257 c
317.87263 297.39969 317.45055 293.25345 316.59222 285.38306 C
315.61767 276.30510 L
315.28290 273.13370 315.10857 270.18057 315.10857 267.48907 c
315.10857 264.08494 315.80674 261.58252 317.18891 259.98236 C
313.90101 258.78954 311.57320 258.19285 310.20576 258.19285 c
307.70362 258.19285 306.45241 259.67679 306.45241 262.63020 c
306.45241 263.67761 306.78690 267.57638 307.47061 274.34126 C
308.66372 286.53250 L
@c
F

@rax %Note: Object
472.70948 256.38321 558.56098 304.75559 @E
/$fm 1 def
 0 O 0 @g
[ 1.00 0.00 0.00 0.00 1.00 null ] set_fill_color
479.33830 256.93569 m
482.35266 270.05783 L
486.37162 291.12321 L
486.99950 294.38220 487.30620 296.52123 487.30620 297.50995 c
487.30620 299.27083 486.49720 300.15751 484.89222 300.15751 c
482.22709 300.15751 478.16532 297.04507 472.70948 290.81820 C
473.44932 294.73143 L
479.39386 301.40901 484.73887 304.75559 489.51156 304.75559 c
493.43272 304.75559 495.40054 302.61628 495.40054 298.32435 c
495.40054 296.86989 495.09298 294.46980 494.46510 291.10876 C
492.45562 280.59109 L
493.02822 280.59109 L
496.51625 287.96655 500.32658 293.84391 504.45723 298.20784 c
508.58816 302.57291 512.39735 304.75559 515.88652 304.75559 c
517.71402 304.75559 519.24983 303.99874 520.49083 302.49978 C
521.73298 300.98750 522.34696 299.11011 522.34696 296.86989 c
522.34696 295.22636 522.10970 293.24806 521.64992 290.94888 C
519.69572 280.59109 L
520.26831 280.59109 L
523.28268 287.69017 526.85461 293.49439 530.97165 298.00488 C
535.08841 302.49978 538.85622 304.75559 542.28898 304.75559 c
547.11780 304.75559 549.53178 302.07827 549.53178 296.73893 c
549.53178 295.15323 549.30841 293.18939 548.87613 290.86129 C
544.89883 270.05783 L
544.27068 266.78438 543.96312 264.66066 543.96312 263.67194 c
543.96312 261.88214 544.77326 260.98016 546.37824 260.98016 c
549.07115 260.98016 553.13150 264.10819 558.56098 270.36397 C
557.82113 266.39178 L
551.87575 259.71420 546.53159 256.38321 541.75890 256.38321 c
537.80910 256.38321 535.82825 258.52139 535.82825 262.81332 c
535.82825 264.21024 536.14857 266.62479 536.80422 270.05783 C
540.78180 290.86129 L
541.15852 292.73868 541.35439 294.09137 541.35439 294.95027 c
541.35439 297.30699 540.50287 298.48535 538.81370 298.48535 c
534.08381 298.48535 527.20384 289.94485 518.21631 272.86611 C
515.14668 257.44507 L
507.13625 256.93569 L
513.55559 290.77483 L
513.90454 292.65109 514.08567 294.00378 514.08567 294.86239 c
514.08567 297.27694 513.26277 298.48535 511.63002 298.48535 c
509.59276 298.48535 506.56365 295.98208 502.57332 290.97780 C
498.58214 285.98797 494.68847 279.83424 490.90677 272.53106 C
487.92019 257.44507 L
479.33830 256.93569 L
@c
F

@rax %Note: Object
321.29717 244.79150 476.12494 284.26564 @E
/$fm 1 def
 0 O 0 @g
[ 1.00 0.00 0.00 0.00 1.00 null ] set_fill_color
324.70753 275.58709 m
322.49622 264.98183 L
322.18384 263.22406 322.10844 261.83424 322.25896 260.83219 C
322.41061 259.83014 322.72299 259.12176 323.18589 258.71443 C
323.65814 258.30822 324.22507 258.11036 324.87761 258.11972 C
325.85074 258.11972 326.85307 258.35584 327.89310 258.81874 C
328.94277 259.28249 329.97260 259.96309 331.00243 260.84126 C
331.96649 261.66416 332.81802 262.45786 333.57373 263.24249 c
334.32009 264.01776 334.97263 264.78369 335.53984 265.54876 C
335.57783 265.57767 335.62545 265.59609 335.68214 265.59609 c
335.74791 265.59609 335.79553 265.57767 335.82331 265.54876 C
335.74791 265.10428 335.68214 264.71679 335.63452 264.39619 C
335.57783 264.07446 335.53049 263.78164 335.49222 263.50753 C
335.44488 263.23313 335.40775 262.94967 335.36976 262.65685 C
335.22831 261.83424 335.18098 261.07852 335.21811 260.39707 C
335.26545 259.71676 335.42646 259.16910 335.72948 258.75269 C
336.02230 258.34649 336.49455 258.12907 337.12753 258.11972 C
337.63890 258.11972 338.25317 258.24246 338.98110 258.48765 C
339.70791 258.73427 340.49254 259.09285 341.31515 259.55575 C
342.14712 260.02913 342.96973 260.58671 343.77307 261.25795 C
344.57613 261.91984 345.31313 262.66620 345.97502 263.50753 C
346.05128 263.64869 346.12668 263.86611 346.20180 264.14050 C
346.27805 264.42397 346.34409 264.70743 346.39172 264.99090 C
346.42857 265.27436 346.44841 265.50142 346.42857 265.66214 C
346.40079 265.74775 346.36280 265.83222 346.31518 265.89827 C
346.26784 265.96517 346.23071 266.00230 346.20180 266.00230 C
345.60709 265.42602 345.03987 264.94356 344.51943 264.55691 C
343.99984 264.17849 343.53694 263.97950 343.13981 263.96107 C
342.91304 263.97043 342.75231 264.11272 342.66671 264.37663 C
342.57288 264.64139 342.54397 264.97247 342.57288 265.37868 C
342.61002 265.88891 342.68627 266.55080 342.80901 267.36435 C
342.92239 268.16740 343.07291 269.07449 343.24413 270.06746 C
343.41420 271.06016 343.60271 272.08091 343.81106 273.12945 C
344.00891 274.17883 344.21726 275.19959 344.42561 276.19228 C
344.63282 277.17591 344.83181 278.07392 345.01124 278.87698 C
345.18132 279.58507 345.35140 280.20983 345.52148 280.72942 c
345.69156 281.24901 345.86164 281.65521 346.03172 281.93896 C
346.02236 282.09033 345.98438 282.23263 345.89991 282.36472 C
345.81430 282.49654 345.72983 282.58214 345.63487 282.62041 C
345.36983 282.59150 345.00189 282.50702 344.51008 282.36472 c
344.01827 282.22243 343.48025 282.04299 342.88441 281.83578 C
342.29877 281.62743 341.72135 281.41002 341.16350 281.18324 C
340.59657 280.94712 340.11524 280.73877 339.71811 280.54091 C
339.31106 280.34192 339.05622 280.18120 338.94283 280.06781 C
338.82945 279.88838 338.72542 279.65225 338.62139 279.35830 C
338.51707 279.05641 338.40369 278.65928 338.28094 278.15839 C
338.14913 277.65723 337.99748 277.01490 337.82740 276.23055 C
337.64797 275.44592 337.43991 274.47165 337.18422 273.31909 C
337.10882 273.02627 337.01414 272.68498 336.90047 272.27877 C
336.77802 271.88192 336.65528 271.47572 336.52318 271.06016 C
336.38088 270.64375 336.24907 270.26532 336.11613 269.92517 C
335.98403 269.59408 335.87065 269.33953 335.76661 269.17880 C
335.19033 268.16740 334.56643 267.26003 333.89546 266.44677 C
333.22422 265.63436 332.57169 264.99090 331.92935 264.51893 C
331.28617 264.04554 330.70961 263.80035 330.20872 263.79099 C
329.87764 263.80035 329.65087 263.97043 329.53748 264.29187 c
329.42409 264.61361 329.37676 265.02917 329.41474 265.54876 C
329.59417 266.92923 329.89748 268.56425 330.31276 270.44589 C
330.72831 272.33546 331.19235 274.51899 331.68302 277.00469 C
331.89109 278.15839 332.01383 279.10375 332.05209 279.84076 C
332.09008 280.57805 332.06145 281.15433 331.95713 281.56139 C
331.86331 281.95852 331.71194 282.24198 331.51294 282.39364 C
331.31480 282.55323 331.08803 282.62948 330.83235 282.62041 C
330.39836 282.62041 329.83030 282.49654 329.13128 282.25134 C
328.42205 282.00586 327.64677 281.66570 326.79609 281.21102 C
325.94570 280.75748 325.08595 280.20983 324.20636 279.58507 C
323.32706 278.95209 322.49622 278.24400 321.70139 277.45937 C
321.64441 277.33578 321.57865 277.13764 321.51260 276.88195 C
321.44655 276.61805 321.38986 276.34394 321.34252 276.06954 C
321.30425 275.79543 321.28583 275.57773 321.30425 275.41701 C
321.31474 275.37987 321.34252 275.34189 321.39921 275.29427 C
321.45591 275.23757 321.49389 275.22850 321.53102 275.24693 C
321.91880 275.54995 322.29723 275.81386 322.68472 276.03156 C
323.07250 276.24898 323.42202 276.41906 323.74346 276.54180 C
324.05499 276.66454 324.30132 276.72123 324.48076 276.72123 c
324.65083 276.72123 324.75487 276.62740 324.78265 276.42841 C
324.82091 276.23962 324.79313 275.95616 324.70753 275.58709 C
@c
351.42066 275.81386 m
352.40315 276.46668 353.35786 276.95820 354.29386 277.29865 C
355.23921 277.62945 356.22170 277.79953 357.26202 277.79953 c
358.02794 277.79953 358.68869 277.68614 359.25591 277.45937 C
359.82397 277.24195 360.26731 276.92957 360.57969 276.53244 C
360.90113 276.13559 361.06214 275.66334 361.06214 275.13354 c
361.06214 274.70863 360.93827 274.32113 360.71150 273.98098 C
360.47537 273.63033 360.16413 273.31909 359.75679 273.03562 C
359.31260 272.73231 358.78365 272.41172 358.18781 272.05200 C
357.58375 271.70249 356.95899 271.33342 356.29795 270.96548 C
355.63606 270.59641 354.99288 270.22819 354.36898 269.87783 C
353.73600 269.51896 353.16907 269.19723 352.66791 268.89534 C
351.58139 268.27143 350.76784 267.67559 350.21027 267.11773 C
349.65241 266.55080 349.27427 266.01279 349.08548 265.49206 C
348.88762 264.98183 348.80202 264.49002 348.81137 264.01776 C
348.81137 263.32809 348.94431 262.64750 349.21729 261.96605 C
349.49169 261.28573 349.91745 260.66211 350.48438 260.10425 C
351.06066 259.54639 351.78860 259.09285 352.68661 258.75269 c
353.58463 258.41254 354.65272 258.24246 355.90110 258.23310 C
356.96920 258.23310 357.95169 258.34649 358.84035 258.57326 C
359.72901 258.79096 360.57969 259.12176 361.39209 259.55575 C
362.20535 260.00022 363.01776 260.53937 363.84038 261.18170 C
364.09606 261.38069 364.40759 261.68258 364.77666 262.08992 C
365.13524 262.49613 365.48476 262.92076 365.81584 263.37543 C
366.15600 263.82898 366.40233 264.23518 366.56334 264.58469 C
366.56334 264.72699 366.53443 264.90643 366.47773 265.13320 c
366.42104 265.35997 366.35499 265.56831 366.26939 265.75710 C
366.18491 265.94561 366.08995 266.06948 365.99613 266.11569 C
365.23020 265.39824 364.50227 264.82082 363.83131 264.37663 C
363.15071 263.93216 362.47039 263.61156 361.77024 263.41257 C
361.08057 263.20422 360.33420 263.11039 359.53002 263.11039 c
358.76523 263.11039 358.07443 263.24249 357.45987 263.51660 C
356.84532 263.78164 356.36400 264.16942 356.00428 264.67030 C
355.63606 265.18082 355.45663 265.79509 355.44756 266.51282 C
355.44756 267.07039 355.59808 267.53443 355.88154 267.89301 C
356.17436 268.26208 356.57235 268.59317 357.09194 268.89534 C
357.45052 269.14054 357.91342 269.42400 358.47128 269.75509 c
359.02913 270.08589 359.62498 270.42605 360.25795 270.79540 C
360.90113 271.16334 361.51569 271.52306 362.11975 271.88192 C
362.73430 272.23143 363.26409 272.56224 363.72699 272.86554 C
364.56831 273.43247 365.20129 273.95206 365.63641 274.44387 C
366.07153 274.92520 366.37342 275.39858 366.52507 275.85213 c
366.67672 276.30567 366.74249 276.76857 366.73342 277.23260 C
366.72406 278.13969 366.54378 278.93367 366.18491 279.60463 C
365.82605 280.28523 365.34359 280.84309 364.73839 281.28728 C
364.14369 281.73175 363.47244 282.07191 362.73430 282.28932 C
362.00636 282.50702 361.26000 282.62041 360.49408 282.62041 C
359.47332 282.60992 358.50019 282.47811 357.57326 282.22243 C
356.64746 281.95852 355.76816 281.59852 354.94639 281.14498 C
354.13313 280.70079 353.36693 280.19027 352.66791 279.61398 C
352.50718 279.41499 352.32775 279.13153 352.11969 278.77266 C
351.91134 278.42343 351.71348 278.05408 351.53405 277.67594 C
351.35461 277.29865 351.22167 276.97691 351.13720 276.72123 C
351.11735 276.64583 351.12784 276.54180 351.15562 276.40998 C
351.19389 276.28724 351.23074 276.16337 351.28772 276.04998 C
351.33506 275.93660 351.38239 275.86148 351.42066 275.81386 C
@c
386.35540 269.40557 m
386.35540 268.61159 386.25222 267.79833 386.04416 266.96721 C
385.84517 266.12617 385.57077 265.32198 385.22126 264.53735 C
384.87175 263.76208 384.47490 263.06306 384.03071 262.43943 c
383.58624 261.81553 383.11398 261.31465 382.62246 260.94557 C
382.12129 260.58671 381.62948 260.39707 381.13767 260.38772 C
380.50469 260.39707 380.01288 260.62384 379.64494 261.05896 C
379.28523 261.50315 379.10580 262.10835 379.09644 262.88362 C
379.09644 263.57329 379.24809 264.36728 379.55934 265.25594 C
379.86236 266.15395 380.31619 267.09931 380.91090 268.10135 C
381.29868 268.71591 381.72444 269.26328 382.19641 269.74573 C
382.66980 270.22819 383.16047 270.61597 383.65228 270.89943 c
384.14409 271.18290 384.60699 271.32406 385.05118 271.33342 C
385.40069 271.34362 385.70372 271.19225 385.95855 270.87987 C
386.21424 270.57770 386.34605 270.08589 386.35540 269.40557 C
@c
372.23433 263.50753 m
372.24369 262.60951 372.35707 261.89093 372.56542 261.34243 C
372.78198 260.79506 373.04674 260.35058 373.36847 259.99087 C
373.78403 259.46192 374.44592 259.02680 375.34365 258.67729 C
376.24167 258.31757 377.43250 258.13814 378.92636 258.11972 C
380.13676 258.12907 381.28932 258.26088 382.37698 258.51657 C
383.46350 258.77112 384.47490 259.12176 385.40069 259.55575 C
386.33698 259.99087 387.18737 260.47332 387.94422 261.01162 C
388.85131 261.63638 389.69263 262.41052 390.48633 263.32809 C
391.28031 264.24454 391.92350 265.26529 392.43402 266.40964 c
392.94425 267.55285 393.22772 268.78167 393.27506 270.08589 C
393.30312 271.16334 393.14211 272.06135 392.80195 272.78901 C
392.47115 273.50759 392.00825 274.05609 391.42261 274.41496 C
390.83584 274.78403 390.18444 274.96346 389.47521 274.96346 C
388.65231 274.95411 387.84926 274.83137 387.06463 274.59524 C
386.27093 274.34891 385.53364 274.04702 384.82441 273.65924 C
384.11631 273.28082 383.47285 272.86554 382.88721 272.43043 C
382.30072 271.98595 381.79020 271.57068 381.36444 271.16334 C
381.27061 271.07887 381.18529 271.04995 381.10989 271.06951 C
381.03477 271.09729 381.00586 271.16334 381.02428 271.27672 C
381.13767 271.74983 381.33666 272.35502 381.62013 273.08296 C
381.89452 273.81090 382.22532 274.59524 382.60375 275.45528 C
382.99039 276.30567 383.39773 277.16542 383.83172 278.02630 C
384.26683 278.88633 384.70167 279.69959 385.13679 280.44595 C
385.57077 281.19231 385.97811 281.82557 386.35540 282.33666 C
386.30891 282.45940 386.19553 282.58214 386.01524 282.72331 C
385.82646 282.85654 385.62746 282.96992 385.42025 283.06375 C
385.20283 283.15843 385.02340 283.19669 384.88110 283.18734 C
384.59764 282.96992 384.34309 282.76157 384.12539 282.55323 C
383.89861 282.34602 383.69962 282.17594 383.52954 282.04299 C
383.35011 281.90183 383.19874 281.83578 383.06665 281.82557 C
382.43367 281.83578 381.79956 281.90183 381.14816 282.03364 C
380.50469 282.15638 379.79660 282.28932 379.01197 282.41206 C
378.22734 282.54416 377.30976 282.60992 376.26038 282.62041 C
375.52309 282.60085 374.83342 282.36472 374.19109 281.92025 C
373.54791 281.47606 372.94271 280.88107 372.38485 280.12450 C
371.81792 279.36879 371.28898 278.51698 370.79717 277.56255 C
370.30564 276.60784 369.83339 275.60665 369.39827 274.56661 C
369.40762 274.42431 369.51165 274.27380 369.69109 274.10372 C
369.87137 273.93335 370.07858 273.79134 370.29628 273.68702 C
370.52306 273.58299 370.69313 273.55521 370.81587 273.60255 C
371.45906 274.55726 372.04469 275.27584 372.57477 275.76652 C
373.10343 276.26769 373.66129 276.59849 374.25713 276.76857 c
374.85213 276.93865 375.57978 277.01490 376.43131 277.00469 C
376.70457 277.00469 377.00787 277.00469 377.36646 276.99534 c
377.71597 276.98627 378.08504 276.98627 378.46346 276.99534 C
378.84076 277.00469 379.20047 277.02425 379.54998 277.06139 C
377.65928 274.80246 376.17562 272.81792 375.10781 271.11685 c
374.03972 269.41493 373.29335 267.94063 372.85824 266.69310 C
372.42312 265.45493 372.21477 264.38683 372.23433 263.50753 C
@c
386.35540 269.40557 m
386.35540 268.61159 386.25222 267.79833 386.04416 266.96721 C
385.84517 266.12617 385.57077 265.32198 385.22126 264.53735 C
384.87175 263.76208 384.47490 263.06306 384.03071 262.43943 c
383.58624 261.81553 383.11398 261.31465 382.62246 260.94557 C
382.12129 260.58671 381.62948 260.39707 381.13767 260.38772 C
380.50469 260.39707 380.01288 260.62384 379.64494 261.05896 C
379.28523 261.50315 379.10580 262.10835 379.09644 262.88362 C
379.09644 263.57329 379.24809 264.36728 379.55934 265.25594 C
379.86236 266.15395 380.31619 267.09931 380.91090 268.10135 C
381.29868 268.71591 381.72444 269.26328 382.19641 269.74573 C
382.66980 270.22819 383.16047 270.61597 383.65228 270.89943 c
384.14409 271.18290 384.60699 271.32406 385.05118 271.33342 C
385.40069 271.34362 385.70372 271.19225 385.95855 270.87987 C
386.21424 270.57770 386.34605 270.08589 386.35540 269.40557 C
@c
372.23433 263.50753 m
372.24369 262.60951 372.35707 261.89093 372.56542 261.34243 C
372.78198 260.79506 373.04674 260.35058 373.36847 259.99087 C
373.78403 259.46192 374.44592 259.02680 375.34365 258.67729 C
376.24167 258.31757 377.43250 258.13814 378.92636 258.11972 C
380.13676 258.12907 381.28932 258.26088 382.37698 258.51657 C
383.46350 258.77112 384.47490 259.12176 385.40069 259.55575 C
386.33698 259.99087 387.18737 260.47332 387.94422 261.01162 C
388.85131 261.63638 389.69263 262.41052 390.48633 263.32809 C
391.28031 264.24454 391.92350 265.26529 392.43402 266.40964 c
392.94425 267.55285 393.22772 268.78167 393.27506 270.08589 C
393.30312 271.16334 393.14211 272.06135 392.80195 272.78901 C
392.47115 273.50759 392.00825 274.05609 391.42261 274.41496 C
390.83584 274.78403 390.18444 274.96346 389.47521 274.96346 C
388.65231 274.95411 387.84926 274.83137 387.06463 274.59524 C
386.27093 274.34891 385.53364 274.04702 384.82441 273.65924 C
384.11631 273.28082 383.47285 272.86554 382.88721 272.43043 C
382.30072 271.98595 381.79020 271.57068 381.36444 271.16334 C
381.27061 271.07887 381.18529 271.04995 381.10989 271.06951 C
381.03477 271.09729 381.00586 271.16334 381.02428 271.27672 C
381.13767 271.74983 381.33666 272.35502 381.62013 273.08296 C
381.89452 273.81090 382.22532 274.59524 382.60375 275.45528 C
382.99039 276.30567 383.39773 277.16542 383.83172 278.02630 C
384.26683 278.88633 384.70167 279.69959 385.13679 280.44595 C
385.57077 281.19231 385.97811 281.82557 386.35540 282.33666 C
386.30891 282.45940 386.19553 282.58214 386.01524 282.72331 C
385.82646 282.85654 385.62746 282.96992 385.42025 283.06375 C
385.20283 283.15843 385.02340 283.19669 384.88110 283.18734 C
384.59764 282.96992 384.34309 282.76157 384.12539 282.55323 C
383.89861 282.34602 383.69962 282.17594 383.52954 282.04299 C
383.35011 281.90183 383.19874 281.83578 383.06665 281.82557 C
382.43367 281.83578 381.79956 281.90183 381.14816 282.03364 C
380.50469 282.15638 379.79660 282.28932 379.01197 282.41206 C
378.22734 282.54416 377.30976 282.60992 376.26038 282.62041 C
375.52309 282.60085 374.83342 282.36472 374.19109 281.92025 C
373.54791 281.47606 372.94271 280.88107 372.38485 280.12450 C
371.81792 279.36879 371.28898 278.51698 370.79717 277.56255 C
370.30564 276.60784 369.83339 275.60665 369.39827 274.56661 C
369.40762 274.42431 369.51165 274.27380 369.69109 274.10372 C
369.87137 273.93335 370.07858 273.79134 370.29628 273.68702 C
370.52306 273.58299 370.69313 273.55521 370.81587 273.60255 C
371.45906 274.55726 372.04469 275.27584 372.57477 275.76652 C
373.10343 276.26769 373.66129 276.59849 374.25713 276.76857 c
374.85213 276.93865 375.57978 277.01490 376.43131 277.00469 C
376.70457 277.00469 377.00787 277.00469 377.36646 276.99534 c
377.71597 276.98627 378.08504 276.98627 378.46346 276.99534 C
378.84076 277.00469 379.20047 277.02425 379.54998 277.06139 C
377.65928 274.80246 376.17562 272.81792 375.10781 271.11685 c
374.03972 269.41493 373.29335 267.94063 372.85824 266.69310 C
372.42312 265.45493 372.21477 264.38683 372.23433 263.50753 C
@c
420.95112 275.47370 m
420.96047 277.06139 420.78104 278.38517 420.40290 279.44391 C
420.03468 280.50265 419.53380 281.29663 418.90989 281.82557 C
418.27578 282.35537 417.57704 282.62041 416.81083 282.62041 C
415.89439 282.60992 414.86457 282.32646 413.72022 281.76888 C
412.58608 281.22123 411.40460 280.45531 410.18485 279.48217 C
408.96652 278.49855 407.76520 277.37376 406.60243 276.09732 C
406.57465 276.06954 406.52702 276.06954 406.47033 276.08825 C
406.41364 276.11717 406.38586 276.15430 406.37565 276.21099 C
406.60243 277.44917 406.79206 278.48013 406.93323 279.30161 C
407.07553 280.12450 407.18891 280.81417 407.25496 281.36268 C
407.33036 281.91090 407.37770 282.38315 407.39726 282.79049 C
407.41597 283.02661 407.37770 283.25339 407.29323 283.48951 C
407.19827 283.71628 407.08488 283.90479 406.94258 284.05644 C
406.79206 284.20809 406.64976 284.27386 406.48904 284.26479 C
405.98787 284.22652 405.32712 283.99975 404.49515 283.58419 C
403.67254 283.16778 402.76517 282.62948 401.79203 281.98630 C
400.81776 281.33461 399.85370 280.63474 398.91770 279.89745 C
397.98255 279.15109 397.15994 278.43250 396.45071 277.74283 C
396.37531 277.61924 396.30926 277.43981 396.25257 277.18526 C
396.19587 276.93865 396.14854 276.68409 396.12076 276.40998 C
396.10120 276.13559 396.09184 275.89946 396.11027 275.70047 C
396.11027 275.64378 396.13918 275.58709 396.18680 275.53975 C
396.22394 275.48306 396.28063 275.44592 396.33732 275.41701 C
396.81978 275.79543 397.27332 276.12624 397.70844 276.39128 C
398.13307 276.66454 398.51150 276.87288 398.83323 277.01490 C
399.14447 277.15635 399.38173 277.23260 399.51354 277.23260 C
399.68362 277.22211 399.79701 277.12828 399.86306 276.93865 C
399.92910 276.75921 399.93931 276.53244 399.91039 276.26769 C
399.80636 275.51197 399.67427 274.60460 399.50419 273.55521 c
399.33411 272.50554 399.14447 271.37169 398.94661 270.15194 C
398.73827 268.94268 398.52085 267.70450 398.30315 266.44677 C
398.08687 265.18989 397.87852 263.97043 397.67953 262.79802 C
397.47231 261.62589 397.29175 260.55780 397.13216 259.59402 C
396.86712 258.05367 396.63099 256.68255 396.42265 255.48236 C
396.20523 254.28132 396.00624 253.20416 395.81773 252.24945 C
395.63830 251.29474 395.44951 250.41543 395.25987 249.61238 C
395.12778 249.01739 394.91036 248.53493 394.62690 248.14715 C
394.33408 247.76901 393.91767 247.48526 393.38872 247.28740 C
393.05764 247.18337 392.71748 247.07906 392.35776 246.99373 C
391.99890 246.90898 391.56378 246.79559 391.06290 246.66378 C
391.00620 246.58753 390.94951 246.42652 390.90302 246.16261 C
390.86504 245.90693 390.83613 245.64217 390.82677 245.36891 c
390.81742 245.09452 390.83613 244.90488 390.89282 244.79150 C
393.54009 245.19770 396.17745 245.52850 398.79496 245.79354 C
401.41361 246.04923 403.95600 246.17197 406.43235 246.15326 C
406.50860 246.24709 406.58400 246.41745 406.64976 246.68220 c
406.71581 246.94724 406.76315 247.21115 406.78271 247.46683 C
406.81077 247.73187 406.81077 247.89260 406.77250 247.96800 C
406.22513 248.02469 405.68598 248.08139 405.14683 248.13808 C
404.61789 248.19477 404.18277 248.25146 403.82391 248.30816 C
403.41770 248.42154 403.10532 248.57291 402.90746 248.77106 c
402.70847 248.97005 402.57666 249.22460 402.52904 249.54633 C
402.48170 249.85871 402.48170 250.23600 402.51997 250.68954 C
402.54775 251.21027 402.60444 251.80498 402.67956 252.48557 C
402.75581 253.16702 402.84057 253.87512 402.94460 254.61241 C
403.04863 255.35991 403.15294 256.06800 403.25698 256.76816 C
403.36101 257.45783 403.45483 258.08145 403.54044 258.62995 C
403.77657 258.55483 404.06003 258.48765 404.40019 258.43096 C
404.73128 258.36491 405.07143 258.31757 405.40252 258.28980 C
405.74268 258.25153 406.02614 258.23310 406.26227 258.23310 c
406.98085 258.23310 407.75613 258.35584 408.59745 258.59197 C
409.44784 258.82781 410.30759 259.14954 411.18718 259.57531 C
412.06649 259.99087 412.91688 260.48268 413.74913 261.03118 C
414.57061 261.58876 415.32746 262.18375 416.01713 262.82693 C
417.53877 264.31058 418.73046 266.10661 419.59049 268.23345 C
420.45959 270.36028 420.91313 272.77058 420.95112 275.47370 C
@c
408.19124 261.06831 m
407.44375 261.06831 406.77250 261.21969 406.15795 261.50315 C
405.54454 261.79597 405.05301 262.19310 404.67458 262.71354 C
404.59918 262.80737 404.53228 262.94031 404.48580 263.10132 C
404.43846 263.26091 404.40019 263.41257 404.37241 263.57329 C
404.34350 263.73430 404.33443 263.85704 404.33443 263.96107 C
404.36220 264.20655 404.40954 264.55691 404.47559 265.00025 C
404.55184 265.44472 404.62724 265.94561 404.72192 266.50346 C
404.82595 267.06104 404.93027 267.62825 405.04365 268.21474 C
405.16639 268.79102 405.28885 269.33953 405.41159 269.85912 C
405.50655 270.31266 405.62929 270.77556 405.78066 271.24894 C
405.93118 271.71184 406.10126 272.15603 406.29118 272.57159 C
406.47969 272.98800 406.67868 273.34687 406.88589 273.65924 C
407.48173 274.50992 408.08693 275.22850 408.71083 275.80479 C
409.34381 276.39128 409.93965 276.82526 410.50658 277.12828 C
411.07380 277.42110 411.56532 277.57276 411.99024 277.57276 C
412.56765 277.58211 413.02120 277.26973 413.35200 276.62740 C
413.68309 275.99329 413.85317 274.98189 413.86252 273.60255 C
413.86252 272.26942 413.75820 270.94592 413.55014 269.63235 C
413.34293 268.32813 413.04898 267.11773 412.69011 266.00230 C
412.32104 264.87751 411.90576 263.93216 411.42302 263.16737 C
411.13049 262.71354 410.82831 262.32605 410.52529 262.01367 c
410.22312 261.70214 409.88296 261.46517 409.50454 261.31465 C
409.13660 261.15364 408.70148 261.06831 408.19124 261.06831 c
@c
427.29364 275.58709 m
425.08233 264.98183 L
424.76995 263.22406 424.69455 261.83424 424.84507 260.83219 C
424.99672 259.83014 425.30910 259.12176 425.77200 258.71443 C
426.24425 258.30822 426.81118 258.11036 427.46372 258.11972 C
428.43713 258.11972 429.43918 258.35584 430.47921 258.81874 C
431.52888 259.28249 432.55871 259.96309 433.58882 260.84126 C
434.55260 261.66416 435.40413 262.45786 436.15984 263.24249 c
436.90620 264.01776 437.55902 264.78369 438.12595 265.54876 C
438.16394 265.57767 438.21156 265.59609 438.26825 265.59609 c
438.33430 265.59609 438.38164 265.57767 438.40942 265.54876 C
438.33430 265.10428 438.26825 264.71679 438.22063 264.39619 C
438.16394 264.07446 438.11660 263.78164 438.07861 263.50753 C
438.03099 263.23313 437.99386 262.94967 437.95587 262.65685 C
437.81471 261.83424 437.76709 261.07852 437.80422 260.39707 C
437.85156 259.71676 438.01257 259.16910 438.31559 258.75269 C
438.60841 258.34649 439.08066 258.12907 439.71364 258.11972 C
440.22501 258.11972 440.83928 258.24246 441.56721 258.48765 C
442.29402 258.73427 443.07865 259.09285 443.90126 259.55575 C
444.73323 260.02913 445.55613 260.58671 446.35918 261.25795 C
447.16224 261.91984 447.89924 262.66620 448.56113 263.50753 C
448.63767 263.64869 448.71279 263.86611 448.78819 264.14050 C
448.86444 264.42397 448.93020 264.70743 448.97783 264.99090 C
449.01496 265.27436 449.03452 265.50142 449.01496 265.66214 C
448.98690 265.74775 448.94891 265.83222 448.90157 265.89827 c
448.85395 265.96517 448.81682 266.00230 448.78819 266.00230 C
448.19320 265.42602 447.62627 264.94356 447.10554 264.55691 C
446.58595 264.17849 446.12306 263.97950 445.72620 263.96107 C
445.49943 263.97043 445.33843 264.11272 445.25282 264.37663 C
445.15899 264.64139 445.13036 264.97247 445.15899 265.37868 C
445.19613 265.88891 445.27238 266.55080 445.39512 267.36435 C
445.50850 268.16740 445.65902 269.07449 445.83024 270.06746 C
446.00031 271.06016 446.18910 272.08091 446.39717 273.12945 C
446.59531 274.17883 446.80337 275.19959 447.01172 276.19228 C
447.21893 277.17591 447.41792 278.07392 447.59735 278.87698 C
447.76743 279.58507 447.93751 280.20983 448.10759 280.72942 c
448.27767 281.24901 448.44775 281.65521 448.61783 281.93896 C
448.60876 282.09033 448.57049 282.23263 448.48602 282.36472 C
448.40041 282.49654 448.31594 282.58214 448.22098 282.62041 C
447.95594 282.59150 447.58800 282.50702 447.09619 282.36472 c
446.60438 282.22243 446.06636 282.04299 445.47052 281.83578 C
444.88488 281.62743 444.30746 281.41002 443.74989 281.18324 C
443.18268 280.94712 442.70135 280.73877 442.30450 280.54091 C
441.89717 280.34192 441.64261 280.18120 441.52923 280.06781 C
441.41556 279.88838 441.31153 279.65225 441.20750 279.35830 C
441.10318 279.05641 440.98980 278.65928 440.86734 278.15839 C
440.73524 277.65723 440.58359 277.01490 440.41351 276.23055 C
440.23436 275.44592 440.02602 274.47165 439.77033 273.31909 C
439.69493 273.02627 439.60025 272.68498 439.48687 272.27877 C
439.36413 271.88192 439.24139 271.47572 439.10957 271.06016 C
438.96728 270.64375 438.83518 270.26532 438.70224 269.92517 C
438.57014 269.59408 438.45676 269.33953 438.35272 269.17880 C
437.77644 268.16740 437.15282 267.26003 436.48157 266.44677 C
435.81033 265.63436 435.15780 264.99090 434.51546 264.51893 C
433.87228 264.04554 433.29600 263.80035 432.79483 263.79099 C
432.46403 263.80035 432.23698 263.97043 432.12359 264.29187 c
432.01020 264.61361 431.96287 265.02917 432.00113 265.54876 C
432.18028 266.92923 432.48359 268.56425 432.89887 270.44589 C
433.31443 272.33546 433.77846 274.51899 434.26913 277.00469 C
434.47748 278.15839 434.59994 279.10375 434.63820 279.84076 c
434.67647 280.57805 434.64756 281.15433 434.54324 281.56139 C
434.44942 281.95852 434.29805 282.24198 434.09906 282.39364 C
433.90120 282.55323 433.67414 282.62948 433.41846 282.62041 C
432.98447 282.62041 432.41641 282.49654 431.71739 282.25134 C
431.00816 282.00586 430.23288 281.66570 429.38249 281.21102 C
428.53181 280.75748 427.67206 280.20983 426.79276 279.58507 C
425.91317 278.95209 425.08233 278.24400 424.28750 277.45937 C
424.23080 277.33578 424.16476 277.13764 424.09871 276.88195 C
424.03266 276.61805 423.97597 276.34394 423.92863 276.06954 C
423.89036 275.79543 423.87194 275.57773 423.89036 275.41701 C
423.90085 275.37987 423.92863 275.34189 423.98532 275.29427 C
424.04202 275.23757 424.08028 275.22850 424.11742 275.24693 C
424.50491 275.54995 424.88334 275.81386 425.27083 276.03156 C
425.65861 276.24898 426.00813 276.41906 426.32986 276.54180 C
426.64110 276.66454 426.88743 276.72123 427.06687 276.72123 c
427.23694 276.72123 427.34098 276.62740 427.36904 276.42841 C
427.40702 276.23962 427.37924 275.95616 427.29364 275.58709 C
@c
475.61443 263.28076 m
475.67112 263.36523 475.74652 263.55487 475.82277 263.82898 C
475.89817 264.10224 475.97329 264.39619 476.02998 264.69808 C
476.08668 265.01046 476.12494 265.25594 476.12494 265.43537 C
476.11559 265.53005 476.07732 265.62501 476.00220 265.72819 C
475.93616 265.82287 475.87946 265.87956 475.84120 265.88891 C
475.18866 265.28485 474.59395 264.80239 474.06387 264.45288 C
473.52586 264.09317 473.06183 263.91373 472.66498 263.90438 C
472.43820 263.90438 472.28769 264.00841 472.20208 264.21676 C
472.11761 264.42397 472.09805 264.75477 472.15474 265.20860 C
472.24035 265.97452 472.37216 266.90031 472.54224 267.96841 C
472.71231 269.04671 472.91131 270.19928 473.13808 271.40967 C
473.35578 272.62828 473.59191 273.82932 473.82775 275.01080 C
474.07323 276.19228 474.31049 277.27880 474.53726 278.25307 C
474.65065 278.79137 474.78246 279.31209 474.93411 279.82233 C
475.08463 280.33257 475.24535 280.78611 475.40636 281.18324 C
475.55773 281.58945 475.70939 281.89247 475.84120 282.10904 C
475.49197 282.18529 475.07528 282.25134 474.57439 282.30803 C
474.08343 282.37380 473.56299 282.42142 473.01562 282.46876 c
472.46712 282.51609 471.92769 282.55323 471.41745 282.58214 C
470.89786 282.60992 470.44431 282.62041 470.05654 282.62041 c
468.70498 282.62041 467.34321 282.50702 465.97294 282.28932 c
464.60296 282.07191 463.28854 281.73175 462.03165 281.27792 C
460.78413 280.82438 459.66869 280.24696 458.68535 279.55729 C
457.70258 278.86762 456.92731 278.04501 456.35102 277.09965 C
455.78296 276.16337 455.49043 275.08620 455.48107 273.88602 C
455.49043 272.78901 455.73676 271.84365 456.22743 271.06016 C
456.72860 270.27553 457.41912 269.65077 458.30693 269.18787 C
459.19559 268.73433 460.23591 268.44151 461.43581 268.32813 C
460.61405 267.40148 459.89546 266.65483 459.29027 266.07855 C
458.67600 265.50142 458.12750 265.07650 457.62633 264.81146 C
457.13452 264.54671 456.64384 264.41461 456.16139 264.41461 C
455.73676 264.42397 455.24494 264.51893 454.69644 264.69808 C
454.13858 264.88687 453.60992 265.12384 453.09940 265.41666 C
452.57896 265.70948 452.16340 266.01279 451.85102 266.34246 C
451.78498 266.35294 451.68094 266.32403 451.52022 266.25798 C
451.36970 266.18287 451.21805 266.09726 451.06639 265.99294 C
450.91587 265.88891 450.82120 265.79509 450.77357 265.71883 C
450.67890 265.44472 450.59414 265.13320 450.52838 264.77348 C
450.46233 264.40554 450.41499 264.02712 450.37672 263.63934 c
450.33874 263.25184 450.32003 262.89298 450.32003 262.54346 C
450.33874 261.77754 450.48076 261.05896 450.75515 260.38772 c
451.02926 259.71676 451.43546 259.17846 451.96441 258.76205 C
452.49420 258.34649 453.15609 258.12907 453.95008 258.11972 C
454.84809 258.15685 455.68006 258.41254 456.43578 258.87543 C
457.19235 259.34854 457.92964 259.98151 458.65729 260.78457 C
459.66869 261.91984 460.60384 263.06306 461.45537 264.20655 C
462.30605 265.35090 463.10910 266.48476 463.87502 267.60954 C
464.64009 268.72498 465.39666 269.82113 466.14302 270.87987 C
466.17194 270.97455 466.18129 271.09729 466.18129 271.23959 C
466.17194 271.39011 466.14302 271.49414 466.08633 271.56019 C
465.39666 271.69313 464.76255 271.95817 464.17720 272.33546 C
463.60091 272.71389 463.12753 273.20570 462.77802 273.79134 C
462.41943 274.37783 462.23887 275.05814 462.22951 275.81386 C
462.23887 276.71187 462.41008 277.47780 462.75024 278.12013 C
463.09039 278.76359 463.56265 279.25540 464.14913 279.60463 C
464.73477 279.94507 465.39666 280.12450 466.14302 280.12450 c
466.37915 280.12450 466.63483 280.11515 466.92765 280.09644 C
467.21112 280.07688 467.50394 280.02954 467.77833 279.96350 C
468.06180 279.89745 468.31748 279.80277 468.52554 279.67068 C
468.08135 277.56255 467.69357 275.70983 467.36277 274.14085 C
467.02261 272.56224 466.72980 271.20132 466.48346 270.05811 C
466.23798 268.92312 466.02964 267.94970 465.85956 267.14665 C
465.68013 266.34246 465.53811 265.66214 465.41537 265.09521 C
465.30198 264.53735 465.19767 264.04554 465.12255 263.62091 C
464.89578 262.49613 464.78239 261.53206 464.78239 260.70945 c
464.78239 259.88683 464.91420 259.25386 465.17924 258.80003 C
465.44428 258.35584 465.85956 258.12907 466.42677 258.11972 C
467.05975 258.12907 467.80696 258.28044 468.64828 258.57326 C
469.48961 258.87543 470.34935 259.27228 471.23802 259.76409 C
472.11761 260.26498 472.94844 260.81348 473.71436 261.41868 C
474.48964 262.03323 475.12261 262.64750 475.61443 263.28076 C
@c
F

@rax %Note: Object
561.49682 244.76939 624.64564 284.11824 @E
/$fm 1 def
 0 O 0 @g
[ 1.00 0.00 0.00 0.00 0.50 null ] set_fill_color
566.30268 266.70501 m
565.76580 266.70501 565.21928 266.62025 564.68211 266.43175 C
564.13559 266.24324 563.62677 265.94192 563.13694 265.51786 C
562.55272 264.99969 562.12866 264.42482 561.87439 263.78419 C
561.61984 263.13392 561.48803 262.47430 561.49739 261.78661 C
561.49739 261.33420 561.56343 260.89143 561.70460 260.45802 C
561.83669 260.03395 562.03455 259.64787 562.28882 259.29921 C
562.60913 258.95991 562.99550 258.68665 563.44791 258.47943 C
563.90939 258.27194 564.40885 258.16847 564.94602 258.16847 c
565.54894 258.16847 566.14252 258.25323 566.71739 258.43238 C
567.30161 258.62060 567.81978 258.90350 568.28154 259.29921 C
568.79036 259.78904 569.16709 260.34491 569.42164 260.96683 c
569.67591 261.58876 569.80800 262.20132 569.80800 262.80425 c
569.80800 263.27537 569.74195 263.76520 569.61014 264.28365 C
569.46869 264.81118 569.23313 265.27294 568.90346 265.68737 C
568.58287 265.97962 568.20614 266.21518 567.75373 266.40340 C
567.30161 266.60154 566.82113 266.69565 566.30268 266.70501 C
@c
586.76825 260.99518 m
586.02387 260.99518 585.35490 261.14598 584.74233 261.42860 C
584.12976 261.72057 583.63994 262.11628 583.26293 262.63474 C
583.18753 262.72885 583.12176 262.86066 583.07471 263.02082 c
583.02737 263.18098 582.98967 263.33178 582.96161 263.49194 c
582.93326 263.65209 582.92391 263.77483 582.92391 263.87830 C
582.95197 264.12321 582.99931 264.47187 583.06507 264.91493 C
583.14047 265.35770 583.21587 265.85717 583.31027 266.41304 C
583.41373 266.96891 583.51748 267.53414 583.63058 268.11836 C
583.75304 268.69323 583.87550 269.23975 583.99795 269.75792 C
584.09206 270.21005 584.21480 270.67181 584.36532 271.14293 C
584.51613 271.60469 584.68592 272.04746 584.87414 272.46217 C
585.06265 272.87660 585.26050 273.23461 585.46772 273.54557 C
586.06157 274.39370 586.66450 275.10973 587.28643 275.68460 C
587.91770 276.26882 588.51128 276.70224 589.07650 277.00356 C
589.64202 277.29581 590.13184 277.44661 590.55591 277.44661 C
591.13077 277.45597 591.58290 277.14501 591.91285 276.50438 C
592.24252 275.87310 592.41203 274.86482 592.42139 273.48917 C
592.42139 272.16057 592.31792 270.84132 592.11071 269.53172 C
591.90321 268.23146 591.61124 267.02532 591.25323 265.91357 C
590.88557 264.79219 590.47115 263.84995 589.99068 263.08687 C
589.69843 262.63474 589.39682 262.24838 589.09550 261.93742 c
588.79389 261.62646 588.45458 261.39090 588.07786 261.24009 C
587.71049 261.07994 587.27707 260.99518 586.76825 260.99518 c
@c
599.48816 275.35465 m
599.49780 276.93780 599.31865 278.25676 598.94164 279.31209 C
598.57427 280.36743 598.07480 281.15887 597.45317 281.68639 C
596.82161 282.21420 596.12457 282.47811 595.36120 282.47811 C
594.44731 282.46847 593.42031 282.18586 592.28022 281.62998 C
591.14948 281.08346 589.97169 280.32038 588.75619 279.34980 C
587.54069 278.36986 586.34419 277.24876 585.18510 275.97657 C
585.15704 275.94822 585.10970 275.94822 585.05329 275.96721 C
584.99660 275.99556 584.96854 276.03326 584.95918 276.08967 C
585.18510 277.32387 585.37361 278.35115 585.51506 279.17065 C
585.65622 279.99043 585.76932 280.67839 585.83537 281.22491 C
585.91077 281.77143 585.95783 282.24255 585.97654 282.64762 C
585.99553 282.88318 585.95783 283.10939 585.87307 283.34494 C
585.77868 283.57087 585.66557 283.75937 585.52441 283.91017 C
585.37361 284.06098 585.23216 284.12702 585.07200 284.11739 C
584.57282 284.07969 583.91320 283.85376 583.08406 283.43906 C
582.26428 283.02463 581.35975 282.48746 580.38917 281.84683 C
579.41858 281.19657 578.45764 280.49924 577.52476 279.76450 C
576.59216 279.02013 575.77238 278.30381 575.06570 277.61613 C
574.99030 277.49367 574.92425 277.31452 574.86784 277.06025 C
574.81115 276.81506 574.76409 276.56079 574.73575 276.28753 C
574.71704 276.01427 574.70769 275.77871 574.72639 275.58085 C
574.72639 275.52444 574.75474 275.46775 574.80180 275.42069 C
574.83950 275.36428 574.89591 275.32658 574.95260 275.29824 C
575.43307 275.67496 575.88548 276.00491 576.31890 276.26882 C
576.74268 276.54208 577.11969 276.74929 577.44000 276.89046 C
577.75096 277.03191 577.98652 277.10731 578.11833 277.10731 C
578.28813 277.09795 578.40123 277.00356 578.46699 276.81506 C
578.53304 276.63619 578.54239 276.40998 578.51405 276.14608 C
578.41058 275.39235 578.27849 274.48781 578.10898 273.44211 c
577.93946 272.39613 577.75096 271.26539 577.55310 270.04989 C
577.34589 268.84403 577.12904 267.60954 576.91247 266.35635 c
576.69562 265.10315 576.48841 263.88765 576.29055 262.71950 C
576.08334 261.55106 575.90419 260.48636 575.74403 259.52513 C
575.48013 257.98932 575.24457 256.62331 575.03735 255.42652 C
574.82050 254.23002 574.62265 253.15569 574.43443 252.20409 C
574.25528 251.25250 574.06677 250.37631 573.87827 249.57524 C
573.74646 248.98167 573.52989 248.50120 573.24699 248.11483 C
572.95502 247.73783 572.54031 247.45521 572.01279 247.25735 C
571.68312 247.15361 571.34381 247.05014 570.98580 246.96539 c
570.62778 246.88063 570.19436 246.76753 569.69490 246.63543 C
569.63849 246.56003 569.58180 246.39987 569.53474 246.13625 C
569.49704 245.88170 569.46869 245.61780 569.45934 245.34454 c
569.44998 245.07128 569.46869 244.88306 569.52539 244.76995 C
572.16359 245.17502 574.79244 245.50498 577.40230 245.76860 C
580.01244 246.02315 582.54690 246.14561 585.01559 246.12661 C
585.09099 246.22101 585.16639 246.39052 585.23216 246.65443 C
585.29820 246.91805 585.34526 247.18195 585.36425 247.43650 C
585.39260 247.70013 585.39260 247.86028 585.35490 247.93569 C
584.80838 247.99238 584.27121 248.04879 583.73405 248.10548 C
583.20652 248.16189 582.77310 248.21858 582.41509 248.27499 C
582.00973 248.38809 581.69906 248.53890 581.50091 248.73676 c
581.30306 248.93461 581.17124 249.18888 581.12419 249.50920 C
581.07713 249.82016 581.07713 250.19717 581.11483 250.64929 C
581.14290 251.16775 581.19959 251.76132 581.27499 252.43965 c
581.35039 253.11798 581.43515 253.82466 581.53861 254.55969 C
581.64236 255.30406 581.74611 256.01074 581.84957 256.70806 C
581.95332 257.39575 582.04743 258.01767 582.13247 258.56419 C
582.36803 258.48879 582.65065 258.42274 582.98967 258.36633 C
583.31962 258.30028 583.65865 258.25323 583.98860 258.22488 C
584.32762 258.18718 584.61052 258.16847 584.84608 258.16847 c
585.56211 258.16847 586.33483 258.29093 587.17332 258.52649 C
588.02117 258.76205 588.87865 259.08236 589.75512 259.50643 C
590.63131 259.92085 591.47915 260.41096 592.30857 260.95748 C
593.12806 261.51335 593.88208 262.10693 594.56976 262.74756 C
596.08687 264.22696 597.27402 266.01732 598.13150 268.13735 C
598.99833 270.25739 599.45046 272.66003 599.48816 275.35465 C
@c
610.79499 282.47811 m
610.10702 282.46847 609.38164 282.27061 608.60891 281.88454 C
607.84583 281.48854 607.07310 280.98935 606.31002 280.36743 c
605.54665 279.74551 604.83061 279.06718 604.16164 278.35115 c
603.49266 277.63483 602.91780 276.93780 602.42797 276.25918 C
602.35257 276.11802 602.28652 275.92016 602.23011 275.66561 C
602.17342 275.41134 602.12636 275.14743 602.09802 274.87417 C
602.07931 274.60091 602.06995 274.36535 602.08866 274.16750 C
602.12636 274.02605 602.19241 273.91323 602.28652 273.84718 c
602.38091 273.78113 602.44668 273.77178 602.48438 273.82819 C
603.16271 274.57257 603.79427 275.14743 604.35950 275.57150 C
604.93436 275.98592 605.41483 276.19342 605.81991 276.20277 C
606.13087 276.20277 606.41348 275.94822 606.67739 275.45839 C
606.93165 274.96828 607.17685 274.25225 607.40277 273.31965 c
607.61027 272.46217 607.82683 271.47260 608.05304 270.35150 C
608.27924 269.23975 608.49581 268.06195 608.70331 266.82746 C
608.91987 265.59326 609.10838 264.36841 609.26854 263.14328 C
609.43805 261.92806 609.56050 260.78797 609.64526 259.73263 C
609.72066 258.66794 609.74901 257.75376 609.72066 256.98132 C
609.72066 256.89628 609.71131 256.74576 609.67361 256.52891 C
609.64526 256.30271 609.59820 256.06715 609.53244 255.81288 C
609.46639 255.56797 609.38164 255.35112 609.26854 255.17225 C
608.48646 253.96611 607.78913 253.09928 607.18620 252.56211 C
606.59263 252.03458 606.03676 251.77068 605.53729 251.78003 C
605.19798 251.78003 604.79291 251.83672 604.31244 251.95918 C
603.84132 252.07228 603.30416 252.26050 602.71058 252.51506 C
602.55978 252.53376 602.40898 252.50570 602.24882 252.43030 C
602.07931 252.35490 601.95685 252.27014 601.86246 252.17575 c
601.68359 251.99688 601.45739 251.68592 601.17477 251.25250 C
600.89216 250.80973 600.58120 250.31027 600.26088 249.74476 C
599.94028 249.17953 599.63896 248.61430 599.34671 248.04879 C
599.06409 247.48356 598.82854 246.98409 598.64003 246.54132 C
598.46117 246.10791 598.36677 245.79694 598.35742 245.61780 C
598.35742 245.33518 598.47052 245.12797 598.68737 244.98652 C
598.91329 244.84535 599.23361 244.76995 599.65767 244.76995 C
600.17584 244.76060 600.78841 244.87342 601.48573 245.12797 C
602.17342 245.37288 602.98384 245.82529 603.91672 246.48463 C
604.84932 247.14425 605.94236 248.07713 607.17685 249.29263 C
608.22255 250.31027 609.39099 251.61052 610.68189 253.19339 C
611.98214 254.77654 613.32945 256.57597 614.72409 258.58290 c
616.11846 260.58983 617.49411 262.73820 618.85106 265.02775 C
620.19836 267.30794 621.45156 269.66353 622.61065 272.07581 C
623.33603 273.66803 623.85420 275.11909 624.15581 276.44769 C
624.46677 277.76693 624.62693 278.90702 624.64564 279.87732 C
624.64564 280.23562 624.59858 280.62170 624.51383 281.04576 c
624.42907 281.46983 624.31597 281.84683 624.15581 282.15751 c
623.99565 282.46847 623.79780 282.62863 623.57159 282.64762 C
623.41143 282.64762 623.13817 282.56287 622.77080 282.42142 C
622.40315 282.27061 621.98872 282.07276 621.52696 281.83720 C
621.05584 281.59228 620.58472 281.32838 620.12296 281.04576 C
619.65184 280.76315 619.23742 280.48989 618.87940 280.21663 C
618.65320 280.00942 618.48340 279.81156 618.37994 279.62306 C
618.26683 279.43455 618.22913 279.29339 618.25748 279.19899 C
618.65320 278.36050 618.95452 277.54072 619.15238 276.73030 C
619.35052 275.92951 619.44463 275.09102 619.44463 274.22419 C
619.43528 273.61162 619.33153 272.83890 619.13367 271.91565 C
618.92646 270.99213 618.55880 269.88973 618.02192 268.59883 C
617.49411 267.31757 616.72167 265.80983 615.71339 264.10450 C
615.65698 264.01975 615.60964 263.99140 615.58157 264.00076 C
615.54387 264.01039 615.51553 264.04809 615.48718 264.10450 C
615.44013 264.86759 615.37408 265.76277 615.27997 266.79940 C
615.18586 267.84510 615.06312 268.94750 614.92195 270.13465 C
614.78050 271.31244 614.62035 272.49987 614.44148 273.68702 C
614.25298 274.87417 614.06447 275.99556 613.84762 277.05061 C
613.64041 278.09660 613.39550 279.02948 613.10353 279.83962 C
612.82063 280.65005 612.48161 281.29068 612.10460 281.76180 C
611.71824 282.23291 611.28482 282.46847 610.79499 282.47811 C
@c
F

@rax %Note: Object
338.78239 298.97093 453.22498 351.07200 @E
/$fm 1 def
 0 O 0 @g
[ 1.00 0.00 0.00 0.00 0.50 null ] set_fill_color
386.73241 310.12611 m
391.58986 305.92261 398.65861 302.72088 407.48031 301.72904 C
413.74091 301.02520 420.45591 301.98019 425.51348 304.68359 C
429.81392 306.98220 432.90794 310.55329 433.45814 315.44787 C
433.88731 319.26586 432.50485 322.66063 430.21049 325.27446 C
427.57030 328.28287 423.73162 330.26117 420.04602 330.67531 C
415.82409 331.14983 412.23203 330.62088 409.28117 329.47455 C
405.97597 328.19074 403.47043 326.13080 401.78381 323.84438 C
402.24983 323.14025 402.66312 322.31565 402.95367 321.52365 C
403.28957 320.60806 403.46986 319.71061 403.39531 319.04674 C
403.28164 318.03562 402.82129 317.09339 402.22488 316.41449 C
401.56016 315.65735 400.70438 315.20721 399.91578 315.29565 C
399.26353 315.36879 398.51631 316.07178 398.05200 317.08800 C
397.69172 317.87745 397.47515 318.87723 397.59137 319.91357 C
397.68265 320.72683 397.92472 321.51912 398.25099 322.23798 C
398.55005 322.89704 398.92167 323.49827 399.31398 324.00113 C
397.49131 326.57386 395.07562 328.89997 392.61175 330.85446 C
389.16822 333.58592 385.64447 335.58406 383.50148 336.52545 C
382.44954 335.18721 381.55748 333.62731 380.86498 331.89619 C
380.13024 330.05906 379.61802 328.02718 379.37452 325.86094 C
378.77131 320.49524 381.42794 314.71682 386.73241 310.12611 C
@c
375.72236 344.84031 m
378.39912 344.10586 381.14532 343.09899 383.87537 341.72277 C
386.30721 344.02195 389.54438 346.67150 394.14076 348.56476 C
398.91430 350.53115 405.14088 351.67408 413.42428 350.74290 C
421.64957 349.81824 428.72230 346.56888 434.58293 342.41301 C
442.80510 336.58243 448.64164 328.96148 451.92501 323.48381 C
452.48627 322.72214 452.83068 322.12913 453.01946 321.68494 C
453.36132 320.88217 453.24113 320.36513 452.92110 320.10945 C
452.73798 319.96318 L
452.50469 319.98983 L
452.29153 320.01392 452.01798 320.11824 451.54488 320.62904 C
451.54403 320.62819 L
451.18233 321.01824 450.62220 321.71981 449.71512 322.98123 C
445.63521 328.63805 440.73213 333.63694 434.77483 337.45323 C
428.82491 341.26441 421.81739 343.89780 413.51953 344.83096 C
407.69546 345.48576 402.44428 345.46139 397.85528 344.57471 C
393.57524 343.74728 389.87150 342.16866 386.81688 339.68835 C
389.52482 337.87559 392.20724 335.71672 394.67820 333.19531 C
396.85181 330.97748 398.86384 328.47647 400.58731 325.68094 C
402.98825 328.76816 405.94167 331.09172 409.24857 332.57225 C
412.80746 334.16561 416.77030 334.78016 420.88592 334.31754 C
425.04718 333.84983 429.77906 331.69805 432.94791 327.93137 C
435.49909 324.89858 437.04142 320.82831 436.47194 315.76082 C
435.80778 309.85200 432.30841 305.40926 427.21087 302.57972 C
421.78054 299.56535 414.52980 298.39096 406.98624 299.23880 C
398.81310 300.15751 390.20428 303.58630 384.32069 309.08041 C
379.31386 313.75587 376.26888 319.91953 377.09943 327.30491 C
377.31345 329.21150 377.81291 331.21984 378.50202 333.09496 C
379.10409 334.73225 379.85471 336.27572 380.69008 337.56888 C
379.03521 338.17323 377.11474 338.73761 374.98564 339.22488 C
372.72472 339.74249 370.22939 340.17335 367.56879 340.47241 C
361.97320 341.10142 354.68164 340.53279 349.20227 337.41865 C
345.16828 335.12570 342.11679 331.43386 341.48268 325.79178 C
340.91150 320.71068 342.92069 316.19367 346.40589 312.72350 C
350.24372 308.90183 355.86227 306.34214 361.81361 305.67317 C
364.86000 305.33074 367.78649 305.40387 370.32463 305.70548 C
373.46485 306.07909 375.99137 306.79710 377.40302 307.50661 C
379.07490 308.43468 379.75294 308.41540 380.04265 308.38592 C
380.65635 308.38592 L
380.58605 307.75691 L
380.56450 307.56274 380.53162 307.26992 380.01883 306.82460 C
379.64466 306.50003 378.93458 306.03345 377.56743 305.34576 C
377.54107 305.33244 L
375.44202 304.40494 373.15134 303.74787 370.37480 303.43691 C
367.63087 303.12992 364.41298 303.16224 360.43427 303.60728 C
360.43370 303.60728 L
360.39317 303.61181 L
355.25622 304.18951 349.54809 306.79313 345.29839 310.76532 C
341.01468 314.76954 338.19619 320.16643 338.88557 326.29805 C
339.63704 332.98384 343.19055 338.26252 348.22233 341.75594 C
353.57159 345.46961 360.59556 347.15792 367.68728 346.36054 C
370.22117 346.07565 372.93052 345.60624 375.72236 344.84031 C
@c
F

%%PageTrailer
@rs
@rs
%%Trailer
@EndSysCorelDict
end
%%DocumentSuppliedResources: procset wCorel14Dict 14.0 0
%%EOF

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const dotenv = require('dotenv');

// Загружаем переменные окружения из .env.local
dotenv.config({ path: '.env.local' });

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3556';
const SCHEMA_URL = `${API_URL}/graphql`;
const OUTPUT_DIR = path.join(__dirname, '../src/lib/graphql');
const SCHEMA_PATH = path.join(OUTPUT_DIR, 'schema.graphql');

// Создаем директорию, если она не существует
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

console.log(`Downloading GraphQL schema from ${SCHEMA_URL}...`);

try {
  // Скачиваем схему
  execSync(`npx graphql-codegen --config codegen.ts`, { stdio: 'inherit' });
  
  console.log(`Schema downloaded and saved to ${SCHEMA_PATH}`);
  console.log('TypeScript types generated successfully!');
} catch (error) {
  console.error('Error downloading schema:', error.message);
  process.exit(1);
}

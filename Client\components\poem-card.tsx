import Link from "next/link"

interface PoemCardProps {
  id: string
  title: string
  author: string
  authorId: string
  text: string
}

export function PoemCard({ id, title, author, authorId, text }: PoemCardProps) {
  return (
    <div className="border border-gray-150 rounded p-3 relative">
      <Link href={`/poem/${id}`} className="block text-blue-500 hover:underline font-medium mb-1">
        {title}
      </Link>

      <Link href={`/poet/${authorId}`} className="text-sm text-gray-600 hover:underline">
        {author}
      </Link>

      {text && text.length > 0 ? (
        <div className="mt-2 text-sm text-gray-700 prose prose-sm">
          <div className="leading-relaxed" dangerouslySetInnerHTML={{ __html: text }} />
        </div>
      ) : (
        <div className="mt-2 text-sm italic text-gray-500">[Шигырьнен тексты]</div>
      )}
    </div>
  )
}


body, html {
	margin:0;
	padding:0;
	font-family:Times New Roman, Times, serif;
	background:#fff;
	}

td	{
	color:#000;
	font-size:16px;
	text-align:left;
	}

p {
	font-size:1.125em;
	padding:0;
	margin:0 0 1.5em 0;
	line-height:1.5em;
	}
	
p.autor {
	color:#777;
	margin:0 0 0.25em 0;
	font-style:italic;
	}	
	
p.search {
	font-size:1em;
	margin:0 0 2em 0;
	}			
	
img {
	border:0;
	}	

a:img {
	border:0;
	}	
	
a	{
	color:000;
	text-decoration:underline;
	}

a:hover	{
	text-decoration:none;
	}
	
h1 {
	font-variant:small-caps;
	font-size:1.5em;
	font-weight:normal;
	padding:0;
	border-bottom:1px solid #c8c8be;
	margin:0;
	}
	
h2 {
	font-size:2em;
	font-weight:bold;
	margin:0 0 0.75em 0;
	line-height:1.1em;
	}

h3 {
	font-size:1.125em;
	font-weight:bold;
	margin:0 0 0.25em 0;
	line-height:1.1em;
	}
	
.top {
	border-top:5px solid #c8c8be;
	height:30px;
	}

.box {
	width:860px;
	border-collapse:collapse;
	}
	
.box td {
	padding:0;
/*	border:1px solid #00CC00;*/
	}
	
.box td.left {
	width:32%;
	padding:2em 2em 3em 30px;
	}
	
.box td.right {
	width:68%;
	padding:2em 0 2em 6em;
	}
	
.box td.left_search {
	padding:4em 0 0 30px;
	}
	
.box td.right_header {
	padding:2em 0 0 0;
	}	
	
.mainmenu {
	font-variant:small-caps;
	font-size:16px;
	padding:0;
	margin:0;
	}

.mainmenu a, .mainmenu_current  {
	padding:0.25em 0.5em 0.25em 0.5em;
	margin:0 1.5em 0 0;
	line-height:1.5em;
	}

.mainmenu_current {
	background:#ebebe1
	;
	}
	
.mainmenu_hole {
	margin:0 10em 0 0;
	}
	
.logo {
	padding:30px 90px 0 50px;
	float:left;
	}
	
.logo_left_text {
	float:left;
	font-size:22px;
	font-style:italic;
	padding:40px 0 0 0;
	line-height:1.1em;
	color:#444;
	}
	
.logo_left_text_padding {
	padding:0 0 0 5em;
	}		
	
.header {
	background:#ebebe1
	url("back_feather.jpg")
	top right
	no-repeat;
	height:200px;
	}	
	
.q_left {
	float:left;
	display:inline;
	font-size:20px;
	margin:28px 0 0 30px;
	line-height:1.5em;
	background:
	url("q-l.gif")
	bottom left
	no-repeat;
	padding:0 0 0 38px;
	}
	
.q_right {
	float:left;
	background:
	url("q-r.gif")
	top left
	no-repeat;
	margin:28px 0 0 15px;
	padding:0 0 0 30px;
	}
	
.q_autor {
	text-align:right;
	font-style:italic;	
	}
	
.catalog {
	margin:1.25em;
	text-align:center;
	}

.catalog_fon {
	background:#ebebe1;
	}	

.catalog a, .catalog_current  {
	padding:0 0.25em 0 0.25em;
	margin:0;
	}

.catalog_current {
	background:#c8c8be;
	}
	
.catalog_hole {
	margin:0 2em 0 0;
	}	

.left_spacer {
	background:url("left.gif")
	1.5em center
	no-repeat;
	height:3em;
	}

.left_header {
	font-variant:small-caps;
	font-weight:bold;
	padding:0 0 0.5em 0;
	color:#000;
	}		
	
.left_text {
	padding:0;
	margin:0 0 0.5em 0;
	line-height:1.2em;
	}

.left_text a {
	padding:0;
	margin:0 0 0.5em 0;
	line-height:1.2em;
	display:block;
	}	
	
.h_left {
	background:
	url("h-l.gif")
	left 0.3em
	no-repeat;
	padding:0;
	margin-left:2em;
	}
	
.h_right {
	background:
	url("h-r.gif")
	right 0.3em
	no-repeat;
	padding:0 2em 0 2em;
	line-height:1.75em;
	}
	
.h_line {
	border-bottom:3px solid #c8c8be;
	margin:0 0 0.1em 0;
	}	

.autor_menu {
	margin:0 0 1.5em 0;
	}

.autor_menu a, .autor_menu_current {
	padding:0 0.25em 0.25em 0.25em;
	margin:0 1em 0 0;
	}

.autor_menu_current {
	background:#c8c8be;
	}

.list {
	font-size:1.125em;
	padding:0 0 1.5em 0;
	}
	
.list a {
	padding:0;
	margin:0 0 0.5em 0;
	display:block;
	}
	
.page {
	margin:0 0 1.5em 0;
	}

.page a, .page_current  {
	padding:0 0.25em 0 0.25em;
	margin:0;
	}

.page_current {
	background:#c8c8be;
	}						

.spacer_line {
	clear:both;
	border-bottom:1px solid #c8c8be;
	font-size:16px;
	margin:0 0 0.2em 0;
	}			

import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  /** The `DateTime` scalar represents an ISO-8601 compliant date time type. */
  DateTime: { input: any; output: any; }
};

/** Defines when a policy shall be executed. */
export type ApplyPolicy =
  /** After the resolver was executed. */
  | 'AFTER_RESOLVER'
  /** Before the resolver was executed. */
  | 'BEFORE_RESOLVER'
  /** The policy is applied in the validation step before the execution. */
  | 'VALIDATION';

export type Author = {
  __typename?: 'Author';
  addedDate: Scalars['DateTime']['output'];
  biography?: Maybe<Scalars['String']['output']>;
  birthDate?: Maybe<Scalars['DateTime']['output']>;
  deathDate?: Maybe<Scalars['DateTime']['output']>;
  displayName?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  /** @deprecated No longer supported. */
  legacyId: Scalars['Int']['output'];
  modifiedDate: Scalars['DateTime']['output'];
  name?: Maybe<Scalars['String']['output']>;
  surName?: Maybe<Scalars['String']['output']>;
  urlPart?: Maybe<Scalars['String']['output']>;
};

export type AuthorFilterInput = {
  addedDate?: InputMaybe<DateTimeOperationFilterInput>;
  and?: InputMaybe<Array<AuthorFilterInput>>;
  biography?: InputMaybe<StringOperationFilterInput>;
  birthDate?: InputMaybe<DateTimeOperationFilterInput>;
  deathDate?: InputMaybe<DateTimeOperationFilterInput>;
  displayName?: InputMaybe<StringOperationFilterInput>;
  id?: InputMaybe<StringOperationFilterInput>;
  lastName?: InputMaybe<StringOperationFilterInput>;
  legacyId?: InputMaybe<IntOperationFilterInput>;
  modifiedDate?: InputMaybe<DateTimeOperationFilterInput>;
  name?: InputMaybe<StringOperationFilterInput>;
  or?: InputMaybe<Array<AuthorFilterInput>>;
  surName?: InputMaybe<StringOperationFilterInput>;
  urlPart?: InputMaybe<StringOperationFilterInput>;
};

export type AuthorSortInput = {
  addedDate?: InputMaybe<SortEnumType>;
  biography?: InputMaybe<SortEnumType>;
  birthDate?: InputMaybe<SortEnumType>;
  deathDate?: InputMaybe<SortEnumType>;
  displayName?: InputMaybe<SortEnumType>;
  id?: InputMaybe<SortEnumType>;
  lastName?: InputMaybe<SortEnumType>;
  legacyId?: InputMaybe<SortEnumType>;
  modifiedDate?: InputMaybe<SortEnumType>;
  name?: InputMaybe<SortEnumType>;
  surName?: InputMaybe<SortEnumType>;
  urlPart?: InputMaybe<SortEnumType>;
};

/** A segment of a collection. */
export type AuthorsCollectionSegment = {
  __typename?: 'AuthorsCollectionSegment';
  /** A flattened list of the items. */
  items?: Maybe<Array<Maybe<Author>>>;
  /** Information to aid in pagination. */
  pageInfo: CollectionSegmentInfo;
  totalCount: Scalars['Int']['output'];
};

/** Information about the offset pagination. */
export type CollectionSegmentInfo = {
  __typename?: 'CollectionSegmentInfo';
  /** Indicates whether more items exist following the set defined by the clients arguments. */
  hasNextPage: Scalars['Boolean']['output'];
  /** Indicates whether more items exist prior the set defined by the clients arguments. */
  hasPreviousPage: Scalars['Boolean']['output'];
};

export type ContentFormat =
  | 'BLOCKS'
  | 'HTML';

export type ContentFormatOperationFilterInput = {
  eq?: InputMaybe<ContentFormat>;
  in?: InputMaybe<Array<ContentFormat>>;
  neq?: InputMaybe<ContentFormat>;
  nin?: InputMaybe<Array<ContentFormat>>;
};

export type CreateAuthorInput = {
  biography?: InputMaybe<Scalars['String']['input']>;
  birthDate?: InputMaybe<Scalars['DateTime']['input']>;
  deathDate?: InputMaybe<Scalars['DateTime']['input']>;
  displayName?: InputMaybe<Scalars['String']['input']>;
  lastName?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  surName?: InputMaybe<Scalars['String']['input']>;
  urlPart?: InputMaybe<Scalars['String']['input']>;
};

export type CreateAuthorPayload = {
  __typename?: 'CreateAuthorPayload';
  author?: Maybe<Author>;
};

export type CreateWorkInput = {
  authorId?: InputMaybe<Scalars['String']['input']>;
  comments?: InputMaybe<Scalars['String']['input']>;
  content?: InputMaybe<Scalars['String']['input']>;
  genres?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  publishedDate?: InputMaybe<Scalars['DateTime']['input']>;
  source?: InputMaybe<Scalars['String']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
};

export type CreateWorkPayload = {
  __typename?: 'CreateWorkPayload';
  work?: Maybe<Work>;
};

export type DateTimeOperationFilterInput = {
  eq?: InputMaybe<Scalars['DateTime']['input']>;
  gt?: InputMaybe<Scalars['DateTime']['input']>;
  gte?: InputMaybe<Scalars['DateTime']['input']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['DateTime']['input']>>>;
  lt?: InputMaybe<Scalars['DateTime']['input']>;
  lte?: InputMaybe<Scalars['DateTime']['input']>;
  neq?: InputMaybe<Scalars['DateTime']['input']>;
  ngt?: InputMaybe<Scalars['DateTime']['input']>;
  ngte?: InputMaybe<Scalars['DateTime']['input']>;
  nin?: InputMaybe<Array<InputMaybe<Scalars['DateTime']['input']>>>;
  nlt?: InputMaybe<Scalars['DateTime']['input']>;
  nlte?: InputMaybe<Scalars['DateTime']['input']>;
};

export type DeleteAuthorPayload = {
  __typename?: 'DeleteAuthorPayload';
  success: Scalars['Boolean']['output'];
};

export type DeleteWorkPayload = {
  __typename?: 'DeleteWorkPayload';
  success: Scalars['Boolean']['output'];
};

export type Genre = {
  __typename?: 'Genre';
  iconCssClass?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
};

export type GenreFilterInput = {
  and?: InputMaybe<Array<GenreFilterInput>>;
  iconCssClass?: InputMaybe<StringOperationFilterInput>;
  id?: InputMaybe<StringOperationFilterInput>;
  or?: InputMaybe<Array<GenreFilterInput>>;
  title?: InputMaybe<StringOperationFilterInput>;
};

export type GenreSortInput = {
  iconCssClass?: InputMaybe<SortEnumType>;
  id?: InputMaybe<SortEnumType>;
  title?: InputMaybe<SortEnumType>;
};

/** A connection to a list of items. */
export type GenresConnection = {
  __typename?: 'GenresConnection';
  /** A list of edges. */
  edges?: Maybe<Array<GenresEdge>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<Maybe<Genre>>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
};

/** An edge in a connection. */
export type GenresEdge = {
  __typename?: 'GenresEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String']['output'];
  /** The item at the end of the edge. */
  node?: Maybe<Genre>;
};

export type GuestbookMessage = {
  __typename?: 'GuestbookMessage';
  answerAuthorName?: Maybe<Scalars['String']['output']>;
  answerDate?: Maybe<Scalars['DateTime']['output']>;
  answerMessage?: Maybe<Scalars['String']['output']>;
  authorEmail?: Maybe<Scalars['String']['output']>;
  authorName?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  messageDate: Scalars['DateTime']['output'];
};

export type GuestbookMessageFilterInput = {
  and?: InputMaybe<Array<GuestbookMessageFilterInput>>;
  answerAuthorName?: InputMaybe<StringOperationFilterInput>;
  answerDate?: InputMaybe<DateTimeOperationFilterInput>;
  answerMessage?: InputMaybe<StringOperationFilterInput>;
  authorEmail?: InputMaybe<StringOperationFilterInput>;
  authorName?: InputMaybe<StringOperationFilterInput>;
  message?: InputMaybe<StringOperationFilterInput>;
  messageDate?: InputMaybe<DateTimeOperationFilterInput>;
  or?: InputMaybe<Array<GuestbookMessageFilterInput>>;
};

export type GuestbookMessageSortInput = {
  answerAuthorName?: InputMaybe<SortEnumType>;
  answerDate?: InputMaybe<SortEnumType>;
  answerMessage?: InputMaybe<SortEnumType>;
  authorEmail?: InputMaybe<SortEnumType>;
  authorName?: InputMaybe<SortEnumType>;
  message?: InputMaybe<SortEnumType>;
  messageDate?: InputMaybe<SortEnumType>;
};

/** A connection to a list of items. */
export type GuestbookMessagesConnection = {
  __typename?: 'GuestbookMessagesConnection';
  /** A list of edges. */
  edges?: Maybe<Array<GuestbookMessagesEdge>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<Maybe<GuestbookMessage>>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
};

/** An edge in a connection. */
export type GuestbookMessagesEdge = {
  __typename?: 'GuestbookMessagesEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String']['output'];
  /** The item at the end of the edge. */
  node?: Maybe<GuestbookMessage>;
};

export type IntOperationFilterInput = {
  eq?: InputMaybe<Scalars['Int']['input']>;
  gt?: InputMaybe<Scalars['Int']['input']>;
  gte?: InputMaybe<Scalars['Int']['input']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
  lt?: InputMaybe<Scalars['Int']['input']>;
  lte?: InputMaybe<Scalars['Int']['input']>;
  neq?: InputMaybe<Scalars['Int']['input']>;
  ngt?: InputMaybe<Scalars['Int']['input']>;
  ngte?: InputMaybe<Scalars['Int']['input']>;
  nin?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
  nlt?: InputMaybe<Scalars['Int']['input']>;
  nlte?: InputMaybe<Scalars['Int']['input']>;
};

export type ListStringOperationFilterInput = {
  any?: InputMaybe<Scalars['Boolean']['input']>;
  none?: InputMaybe<StringOperationFilterInput>;
  some?: InputMaybe<StringOperationFilterInput>;
};

export type Mutation = {
  __typename?: 'Mutation';
  createAuthor?: Maybe<CreateAuthorPayload>;
  createWork?: Maybe<CreateWorkPayload>;
  deleteAuthor?: Maybe<DeleteAuthorPayload>;
  deleteWork?: Maybe<DeleteWorkPayload>;
  updateAuthor?: Maybe<UpdateAuthorPayload>;
  updateWork?: Maybe<UpdateWorkPayload>;
};


export type MutationCreateAuthorArgs = {
  input?: InputMaybe<CreateAuthorInput>;
};


export type MutationCreateWorkArgs = {
  input?: InputMaybe<CreateWorkInput>;
};


export type MutationDeleteAuthorArgs = {
  id?: InputMaybe<Scalars['String']['input']>;
};


export type MutationDeleteWorkArgs = {
  id?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateAuthorArgs = {
  id?: InputMaybe<Scalars['String']['input']>;
  input?: InputMaybe<UpdateAuthorInput>;
};


export type MutationUpdateWorkArgs = {
  id?: InputMaybe<Scalars['String']['input']>;
  input?: InputMaybe<UpdateWorkInput>;
};

/** Information about pagination in a connection. */
export type PageInfo = {
  __typename?: 'PageInfo';
  /** When paginating forwards, the cursor to continue. */
  endCursor?: Maybe<Scalars['String']['output']>;
  /** Indicates whether more edges exist following the set defined by the clients arguments. */
  hasNextPage: Scalars['Boolean']['output'];
  /** Indicates whether more edges exist prior the set defined by the clients arguments. */
  hasPreviousPage: Scalars['Boolean']['output'];
  /** When paginating backwards, the cursor to continue. */
  startCursor?: Maybe<Scalars['String']['output']>;
};

export type SchemaRoot = {
  __typename?: 'SchemaRoot';
  author?: Maybe<Author>;
  authors?: Maybe<AuthorsCollectionSegment>;
  genres?: Maybe<GenresConnection>;
  guestbookMessages?: Maybe<GuestbookMessagesConnection>;
  work?: Maybe<Work>;
  works?: Maybe<WorksCollectionSegment>;
};


export type SchemaRootAuthorArgs = {
  id?: InputMaybe<Scalars['String']['input']>;
};


export type SchemaRootAuthorsArgs = {
  order?: InputMaybe<Array<AuthorSortInput>>;
  skip?: InputMaybe<Scalars['Int']['input']>;
  take?: InputMaybe<Scalars['Int']['input']>;
  where?: InputMaybe<AuthorFilterInput>;
};


export type SchemaRootGenresArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  order?: InputMaybe<Array<GenreSortInput>>;
  where?: InputMaybe<GenreFilterInput>;
};


export type SchemaRootGuestbookMessagesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  order?: InputMaybe<Array<GuestbookMessageSortInput>>;
  where?: InputMaybe<GuestbookMessageFilterInput>;
};


export type SchemaRootWorkArgs = {
  id?: InputMaybe<Scalars['String']['input']>;
};


export type SchemaRootWorksArgs = {
  order?: InputMaybe<Array<WorkSortInput>>;
  skip?: InputMaybe<Scalars['Int']['input']>;
  take?: InputMaybe<Scalars['Int']['input']>;
  where?: InputMaybe<WorkFilterInput>;
};

export type SortEnumType =
  | 'ASC'
  | 'DESC';

export type StringOperationFilterInput = {
  and?: InputMaybe<Array<StringOperationFilterInput>>;
  contains?: InputMaybe<Scalars['String']['input']>;
  endsWith?: InputMaybe<Scalars['String']['input']>;
  eq?: InputMaybe<Scalars['String']['input']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  ncontains?: InputMaybe<Scalars['String']['input']>;
  nendsWith?: InputMaybe<Scalars['String']['input']>;
  neq?: InputMaybe<Scalars['String']['input']>;
  nin?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  nstartsWith?: InputMaybe<Scalars['String']['input']>;
  or?: InputMaybe<Array<StringOperationFilterInput>>;
  startsWith?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateAuthorInput = {
  biography?: InputMaybe<Scalars['String']['input']>;
  birthDate?: InputMaybe<Scalars['DateTime']['input']>;
  deathDate?: InputMaybe<Scalars['DateTime']['input']>;
  displayName?: InputMaybe<Scalars['String']['input']>;
  lastName?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  surName?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateAuthorPayload = {
  __typename?: 'UpdateAuthorPayload';
  author?: Maybe<Author>;
};

export type UpdateWorkInput = {
  authorId?: InputMaybe<Scalars['String']['input']>;
  comments?: InputMaybe<Scalars['String']['input']>;
  content?: InputMaybe<Scalars['String']['input']>;
  genres?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  publishedDate?: InputMaybe<Scalars['DateTime']['input']>;
  source?: InputMaybe<Scalars['String']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateWorkPayload = {
  __typename?: 'UpdateWorkPayload';
  work?: Maybe<Work>;
};

export type Work = {
  __typename?: 'Work';
  authorId?: Maybe<Scalars['String']['output']>;
  comments?: Maybe<Scalars['String']['output']>;
  content?: Maybe<Scalars['String']['output']>;
  contentFormat: ContentFormat;
  genres?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  id?: Maybe<Scalars['String']['output']>;
  /** @deprecated No longer supported. */
  legacyId: Scalars['Int']['output'];
  modifiedBy?: Maybe<Scalars['String']['output']>;
  modifiedDate: Scalars['DateTime']['output'];
  publishedBy?: Maybe<Scalars['String']['output']>;
  publishedDate: Scalars['DateTime']['output'];
  source?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
  urlPart?: Maybe<Scalars['String']['output']>;
};

export type WorkFilterInput = {
  and?: InputMaybe<Array<WorkFilterInput>>;
  authorId?: InputMaybe<StringOperationFilterInput>;
  comments?: InputMaybe<StringOperationFilterInput>;
  content?: InputMaybe<StringOperationFilterInput>;
  contentFormat?: InputMaybe<ContentFormatOperationFilterInput>;
  genres?: InputMaybe<ListStringOperationFilterInput>;
  id?: InputMaybe<StringOperationFilterInput>;
  legacyId?: InputMaybe<IntOperationFilterInput>;
  modifiedBy?: InputMaybe<StringOperationFilterInput>;
  modifiedDate?: InputMaybe<DateTimeOperationFilterInput>;
  or?: InputMaybe<Array<WorkFilterInput>>;
  publishedBy?: InputMaybe<StringOperationFilterInput>;
  publishedDate?: InputMaybe<DateTimeOperationFilterInput>;
  source?: InputMaybe<StringOperationFilterInput>;
  title?: InputMaybe<StringOperationFilterInput>;
  urlPart?: InputMaybe<StringOperationFilterInput>;
};

export type WorkSortInput = {
  authorId?: InputMaybe<SortEnumType>;
  comments?: InputMaybe<SortEnumType>;
  content?: InputMaybe<SortEnumType>;
  contentFormat?: InputMaybe<SortEnumType>;
  id?: InputMaybe<SortEnumType>;
  legacyId?: InputMaybe<SortEnumType>;
  modifiedBy?: InputMaybe<SortEnumType>;
  modifiedDate?: InputMaybe<SortEnumType>;
  publishedBy?: InputMaybe<SortEnumType>;
  publishedDate?: InputMaybe<SortEnumType>;
  source?: InputMaybe<SortEnumType>;
  title?: InputMaybe<SortEnumType>;
  urlPart?: InputMaybe<SortEnumType>;
};

/** A segment of a collection. */
export type WorksCollectionSegment = {
  __typename?: 'WorksCollectionSegment';
  /** A flattened list of the items. */
  items?: Maybe<Array<Maybe<Work>>>;
  /** Information to aid in pagination. */
  pageInfo: CollectionSegmentInfo;
  totalCount: Scalars['Int']['output'];
};

export type GetAuthorsQueryVariables = Exact<{
  skip?: InputMaybe<Scalars['Int']['input']>;
  take?: InputMaybe<Scalars['Int']['input']>;
  where?: InputMaybe<AuthorFilterInput>;
}>;


export type GetAuthorsQuery = { __typename?: 'SchemaRoot', authors?: { __typename?: 'AuthorsCollectionSegment', totalCount: number, items?: Array<{ __typename?: 'Author', id?: string | null, urlPart?: string | null, displayName?: string | null, birthDate?: any | null, deathDate?: any | null, addedDate: any, modifiedDate: any } | null> | null } | null };

export type GetAuthorQueryVariables = Exact<{
  id: Scalars['String']['input'];
}>;


export type GetAuthorQuery = { __typename?: 'SchemaRoot', author?: { __typename?: 'Author', id?: string | null, urlPart?: string | null, name?: string | null, surName?: string | null, lastName?: string | null, displayName?: string | null, biography?: string | null, birthDate?: any | null, deathDate?: any | null, addedDate: any, modifiedDate: any } | null };

export type CreateAuthorMutationVariables = Exact<{
  input: CreateAuthorInput;
}>;


export type CreateAuthorMutation = { __typename?: 'Mutation', createAuthor?: { __typename?: 'CreateAuthorPayload', author?: { __typename?: 'Author', id?: string | null, urlPart?: string | null, displayName?: string | null } | null } | null };

export type UpdateAuthorMutationVariables = Exact<{
  id: Scalars['String']['input'];
  input: UpdateAuthorInput;
}>;


export type UpdateAuthorMutation = { __typename?: 'Mutation', updateAuthor?: { __typename?: 'UpdateAuthorPayload', author?: { __typename?: 'Author', id?: string | null, urlPart?: string | null, displayName?: string | null } | null } | null };

export type DeleteAuthorMutationVariables = Exact<{
  id: Scalars['String']['input'];
}>;


export type DeleteAuthorMutation = { __typename?: 'Mutation', deleteAuthor?: { __typename?: 'DeleteAuthorPayload', success: boolean } | null };

export type GetWorksQueryVariables = Exact<{
  skip?: InputMaybe<Scalars['Int']['input']>;
  take?: InputMaybe<Scalars['Int']['input']>;
  where?: InputMaybe<WorkFilterInput>;
}>;


export type GetWorksQuery = { __typename?: 'SchemaRoot', works?: { __typename?: 'WorksCollectionSegment', totalCount: number, items?: Array<{ __typename?: 'Work', id?: string | null, urlPart?: string | null, authorId?: string | null, title?: string | null, publishedDate: any, modifiedDate: any } | null> | null } | null };

export type GetWorkQueryVariables = Exact<{
  id: Scalars['String']['input'];
}>;


export type GetWorkQuery = { __typename?: 'SchemaRoot', work?: { __typename?: 'Work', id?: string | null, urlPart?: string | null, authorId?: string | null, title?: string | null, content?: string | null, genres?: Array<string | null> | null, comments?: string | null, source?: string | null, publishedDate: any, publishedBy?: string | null, modifiedDate: any, modifiedBy?: string | null } | null };

export type CreateWorkMutationVariables = Exact<{
  input: CreateWorkInput;
}>;


export type CreateWorkMutation = { __typename?: 'Mutation', createWork?: { __typename?: 'CreateWorkPayload', work?: { __typename?: 'Work', id?: string | null, urlPart?: string | null, title?: string | null } | null } | null };

export type UpdateWorkMutationVariables = Exact<{
  id: Scalars['String']['input'];
  input: UpdateWorkInput;
}>;


export type UpdateWorkMutation = { __typename?: 'Mutation', updateWork?: { __typename?: 'UpdateWorkPayload', work?: { __typename?: 'Work', id?: string | null, urlPart?: string | null, title?: string | null } | null } | null };

export type DeleteWorkMutationVariables = Exact<{
  id: Scalars['String']['input'];
}>;


export type DeleteWorkMutation = { __typename?: 'Mutation', deleteWork?: { __typename?: 'DeleteWorkPayload', success: boolean } | null };


export const GetAuthorsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetAuthors"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"skip"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"take"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"where"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"AuthorFilterInput"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"authors"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"skip"},"value":{"kind":"Variable","name":{"kind":"Name","value":"skip"}}},{"kind":"Argument","name":{"kind":"Name","value":"take"},"value":{"kind":"Variable","name":{"kind":"Name","value":"take"}}},{"kind":"Argument","name":{"kind":"Name","value":"where"},"value":{"kind":"Variable","name":{"kind":"Name","value":"where"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"items"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"urlPart"}},{"kind":"Field","name":{"kind":"Name","value":"displayName"}},{"kind":"Field","name":{"kind":"Name","value":"birthDate"}},{"kind":"Field","name":{"kind":"Name","value":"deathDate"}},{"kind":"Field","name":{"kind":"Name","value":"addedDate"}},{"kind":"Field","name":{"kind":"Name","value":"modifiedDate"}}]}},{"kind":"Field","name":{"kind":"Name","value":"totalCount"}}]}}]}}]} as unknown as DocumentNode<GetAuthorsQuery, GetAuthorsQueryVariables>;
export const GetAuthorDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetAuthor"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"author"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"urlPart"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"surName"}},{"kind":"Field","name":{"kind":"Name","value":"lastName"}},{"kind":"Field","name":{"kind":"Name","value":"displayName"}},{"kind":"Field","name":{"kind":"Name","value":"biography"}},{"kind":"Field","name":{"kind":"Name","value":"birthDate"}},{"kind":"Field","name":{"kind":"Name","value":"deathDate"}},{"kind":"Field","name":{"kind":"Name","value":"addedDate"}},{"kind":"Field","name":{"kind":"Name","value":"modifiedDate"}}]}}]}}]} as unknown as DocumentNode<GetAuthorQuery, GetAuthorQueryVariables>;
export const CreateAuthorDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"CreateAuthor"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"input"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"CreateAuthorInput"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"createAuthor"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"input"},"value":{"kind":"Variable","name":{"kind":"Name","value":"input"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"author"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"urlPart"}},{"kind":"Field","name":{"kind":"Name","value":"displayName"}}]}}]}}]}}]} as unknown as DocumentNode<CreateAuthorMutation, CreateAuthorMutationVariables>;
export const UpdateAuthorDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"UpdateAuthor"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"input"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"UpdateAuthorInput"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"updateAuthor"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}},{"kind":"Argument","name":{"kind":"Name","value":"input"},"value":{"kind":"Variable","name":{"kind":"Name","value":"input"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"author"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"urlPart"}},{"kind":"Field","name":{"kind":"Name","value":"displayName"}}]}}]}}]}}]} as unknown as DocumentNode<UpdateAuthorMutation, UpdateAuthorMutationVariables>;
export const DeleteAuthorDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"DeleteAuthor"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"deleteAuthor"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"success"}}]}}]}}]} as unknown as DocumentNode<DeleteAuthorMutation, DeleteAuthorMutationVariables>;
export const GetWorksDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetWorks"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"skip"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"take"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"where"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"WorkFilterInput"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"works"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"skip"},"value":{"kind":"Variable","name":{"kind":"Name","value":"skip"}}},{"kind":"Argument","name":{"kind":"Name","value":"take"},"value":{"kind":"Variable","name":{"kind":"Name","value":"take"}}},{"kind":"Argument","name":{"kind":"Name","value":"where"},"value":{"kind":"Variable","name":{"kind":"Name","value":"where"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"items"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"urlPart"}},{"kind":"Field","name":{"kind":"Name","value":"authorId"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"publishedDate"}},{"kind":"Field","name":{"kind":"Name","value":"modifiedDate"}}]}},{"kind":"Field","name":{"kind":"Name","value":"totalCount"}}]}}]}}]} as unknown as DocumentNode<GetWorksQuery, GetWorksQueryVariables>;
export const GetWorkDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetWork"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"work"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"urlPart"}},{"kind":"Field","name":{"kind":"Name","value":"authorId"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"content"}},{"kind":"Field","name":{"kind":"Name","value":"genres"}},{"kind":"Field","name":{"kind":"Name","value":"comments"}},{"kind":"Field","name":{"kind":"Name","value":"source"}},{"kind":"Field","name":{"kind":"Name","value":"publishedDate"}},{"kind":"Field","name":{"kind":"Name","value":"publishedBy"}},{"kind":"Field","name":{"kind":"Name","value":"modifiedDate"}},{"kind":"Field","name":{"kind":"Name","value":"modifiedBy"}}]}}]}}]} as unknown as DocumentNode<GetWorkQuery, GetWorkQueryVariables>;
export const CreateWorkDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"CreateWork"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"input"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"CreateWorkInput"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"createWork"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"input"},"value":{"kind":"Variable","name":{"kind":"Name","value":"input"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"work"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"urlPart"}},{"kind":"Field","name":{"kind":"Name","value":"title"}}]}}]}}]}}]} as unknown as DocumentNode<CreateWorkMutation, CreateWorkMutationVariables>;
export const UpdateWorkDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"UpdateWork"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"input"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"UpdateWorkInput"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"updateWork"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}},{"kind":"Argument","name":{"kind":"Name","value":"input"},"value":{"kind":"Variable","name":{"kind":"Name","value":"input"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"work"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"urlPart"}},{"kind":"Field","name":{"kind":"Name","value":"title"}}]}}]}}]}}]} as unknown as DocumentNode<UpdateWorkMutation, UpdateWorkMutationVariables>;
export const DeleteWorkDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"DeleteWork"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"deleteWork"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"success"}}]}}]}}]} as unknown as DocumentNode<DeleteWorkMutation, DeleteWorkMutationVariables>;
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  /** The `DateTime` scalar represents an ISO-8601 compliant date time type. */
  DateTime: { input: any; output: any; }
};

/** Defines when a policy shall be executed. */
export type ApplyPolicy =
  /** After the resolver was executed. */
  | 'AFTER_RESOLVER'
  /** Before the resolver was executed. */
  | 'BEFORE_RESOLVER'
  /** The policy is applied in the validation step before the execution. */
  | 'VALIDATION';

export type Author = {
  __typename?: 'Author';
  addedDate: Scalars['DateTime']['output'];
  biography?: Maybe<Scalars['String']['output']>;
  birthDate?: Maybe<Scalars['DateTime']['output']>;
  deathDate?: Maybe<Scalars['DateTime']['output']>;
  displayName?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  /** @deprecated No longer supported. */
  legacyId: Scalars['Int']['output'];
  modifiedDate: Scalars['DateTime']['output'];
  name?: Maybe<Scalars['String']['output']>;
  surName?: Maybe<Scalars['String']['output']>;
  urlPart?: Maybe<Scalars['String']['output']>;
};

export type AuthorFilterInput = {
  addedDate?: InputMaybe<DateTimeOperationFilterInput>;
  and?: InputMaybe<Array<AuthorFilterInput>>;
  biography?: InputMaybe<StringOperationFilterInput>;
  birthDate?: InputMaybe<DateTimeOperationFilterInput>;
  deathDate?: InputMaybe<DateTimeOperationFilterInput>;
  displayName?: InputMaybe<StringOperationFilterInput>;
  id?: InputMaybe<StringOperationFilterInput>;
  lastName?: InputMaybe<StringOperationFilterInput>;
  legacyId?: InputMaybe<IntOperationFilterInput>;
  modifiedDate?: InputMaybe<DateTimeOperationFilterInput>;
  name?: InputMaybe<StringOperationFilterInput>;
  or?: InputMaybe<Array<AuthorFilterInput>>;
  surName?: InputMaybe<StringOperationFilterInput>;
  urlPart?: InputMaybe<StringOperationFilterInput>;
};

export type AuthorSortInput = {
  addedDate?: InputMaybe<SortEnumType>;
  biography?: InputMaybe<SortEnumType>;
  birthDate?: InputMaybe<SortEnumType>;
  deathDate?: InputMaybe<SortEnumType>;
  displayName?: InputMaybe<SortEnumType>;
  id?: InputMaybe<SortEnumType>;
  lastName?: InputMaybe<SortEnumType>;
  legacyId?: InputMaybe<SortEnumType>;
  modifiedDate?: InputMaybe<SortEnumType>;
  name?: InputMaybe<SortEnumType>;
  surName?: InputMaybe<SortEnumType>;
  urlPart?: InputMaybe<SortEnumType>;
};

/** A segment of a collection. */
export type AuthorsCollectionSegment = {
  __typename?: 'AuthorsCollectionSegment';
  /** A flattened list of the items. */
  items?: Maybe<Array<Maybe<Author>>>;
  /** Information to aid in pagination. */
  pageInfo: CollectionSegmentInfo;
  totalCount: Scalars['Int']['output'];
};

/** Information about the offset pagination. */
export type CollectionSegmentInfo = {
  __typename?: 'CollectionSegmentInfo';
  /** Indicates whether more items exist following the set defined by the clients arguments. */
  hasNextPage: Scalars['Boolean']['output'];
  /** Indicates whether more items exist prior the set defined by the clients arguments. */
  hasPreviousPage: Scalars['Boolean']['output'];
};

export type ContentFormat =
  | 'BLOCKS'
  | 'HTML';

export type ContentFormatOperationFilterInput = {
  eq?: InputMaybe<ContentFormat>;
  in?: InputMaybe<Array<ContentFormat>>;
  neq?: InputMaybe<ContentFormat>;
  nin?: InputMaybe<Array<ContentFormat>>;
};

export type CreateAuthorInput = {
  biography?: InputMaybe<Scalars['String']['input']>;
  birthDate?: InputMaybe<Scalars['DateTime']['input']>;
  deathDate?: InputMaybe<Scalars['DateTime']['input']>;
  displayName?: InputMaybe<Scalars['String']['input']>;
  lastName?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  surName?: InputMaybe<Scalars['String']['input']>;
  urlPart?: InputMaybe<Scalars['String']['input']>;
};

export type CreateAuthorPayload = {
  __typename?: 'CreateAuthorPayload';
  author?: Maybe<Author>;
};

export type CreateWorkInput = {
  authorId?: InputMaybe<Scalars['String']['input']>;
  comments?: InputMaybe<Scalars['String']['input']>;
  content?: InputMaybe<Scalars['String']['input']>;
  genres?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  publishedDate?: InputMaybe<Scalars['DateTime']['input']>;
  source?: InputMaybe<Scalars['String']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
};

export type CreateWorkPayload = {
  __typename?: 'CreateWorkPayload';
  work?: Maybe<Work>;
};

export type DateTimeOperationFilterInput = {
  eq?: InputMaybe<Scalars['DateTime']['input']>;
  gt?: InputMaybe<Scalars['DateTime']['input']>;
  gte?: InputMaybe<Scalars['DateTime']['input']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['DateTime']['input']>>>;
  lt?: InputMaybe<Scalars['DateTime']['input']>;
  lte?: InputMaybe<Scalars['DateTime']['input']>;
  neq?: InputMaybe<Scalars['DateTime']['input']>;
  ngt?: InputMaybe<Scalars['DateTime']['input']>;
  ngte?: InputMaybe<Scalars['DateTime']['input']>;
  nin?: InputMaybe<Array<InputMaybe<Scalars['DateTime']['input']>>>;
  nlt?: InputMaybe<Scalars['DateTime']['input']>;
  nlte?: InputMaybe<Scalars['DateTime']['input']>;
};

export type DeleteAuthorPayload = {
  __typename?: 'DeleteAuthorPayload';
  success: Scalars['Boolean']['output'];
};

export type DeleteWorkPayload = {
  __typename?: 'DeleteWorkPayload';
  success: Scalars['Boolean']['output'];
};

export type Genre = {
  __typename?: 'Genre';
  iconCssClass?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
};

export type GenreFilterInput = {
  and?: InputMaybe<Array<GenreFilterInput>>;
  iconCssClass?: InputMaybe<StringOperationFilterInput>;
  id?: InputMaybe<StringOperationFilterInput>;
  or?: InputMaybe<Array<GenreFilterInput>>;
  title?: InputMaybe<StringOperationFilterInput>;
};

export type GenreSortInput = {
  iconCssClass?: InputMaybe<SortEnumType>;
  id?: InputMaybe<SortEnumType>;
  title?: InputMaybe<SortEnumType>;
};

/** A connection to a list of items. */
export type GenresConnection = {
  __typename?: 'GenresConnection';
  /** A list of edges. */
  edges?: Maybe<Array<GenresEdge>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<Maybe<Genre>>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
};

/** An edge in a connection. */
export type GenresEdge = {
  __typename?: 'GenresEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String']['output'];
  /** The item at the end of the edge. */
  node?: Maybe<Genre>;
};

export type GuestbookMessage = {
  __typename?: 'GuestbookMessage';
  answerAuthorName?: Maybe<Scalars['String']['output']>;
  answerDate?: Maybe<Scalars['DateTime']['output']>;
  answerMessage?: Maybe<Scalars['String']['output']>;
  authorEmail?: Maybe<Scalars['String']['output']>;
  authorName?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  messageDate: Scalars['DateTime']['output'];
};

export type GuestbookMessageFilterInput = {
  and?: InputMaybe<Array<GuestbookMessageFilterInput>>;
  answerAuthorName?: InputMaybe<StringOperationFilterInput>;
  answerDate?: InputMaybe<DateTimeOperationFilterInput>;
  answerMessage?: InputMaybe<StringOperationFilterInput>;
  authorEmail?: InputMaybe<StringOperationFilterInput>;
  authorName?: InputMaybe<StringOperationFilterInput>;
  message?: InputMaybe<StringOperationFilterInput>;
  messageDate?: InputMaybe<DateTimeOperationFilterInput>;
  or?: InputMaybe<Array<GuestbookMessageFilterInput>>;
};

export type GuestbookMessageSortInput = {
  answerAuthorName?: InputMaybe<SortEnumType>;
  answerDate?: InputMaybe<SortEnumType>;
  answerMessage?: InputMaybe<SortEnumType>;
  authorEmail?: InputMaybe<SortEnumType>;
  authorName?: InputMaybe<SortEnumType>;
  message?: InputMaybe<SortEnumType>;
  messageDate?: InputMaybe<SortEnumType>;
};

/** A connection to a list of items. */
export type GuestbookMessagesConnection = {
  __typename?: 'GuestbookMessagesConnection';
  /** A list of edges. */
  edges?: Maybe<Array<GuestbookMessagesEdge>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<Maybe<GuestbookMessage>>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
};

/** An edge in a connection. */
export type GuestbookMessagesEdge = {
  __typename?: 'GuestbookMessagesEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String']['output'];
  /** The item at the end of the edge. */
  node?: Maybe<GuestbookMessage>;
};

export type IntOperationFilterInput = {
  eq?: InputMaybe<Scalars['Int']['input']>;
  gt?: InputMaybe<Scalars['Int']['input']>;
  gte?: InputMaybe<Scalars['Int']['input']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
  lt?: InputMaybe<Scalars['Int']['input']>;
  lte?: InputMaybe<Scalars['Int']['input']>;
  neq?: InputMaybe<Scalars['Int']['input']>;
  ngt?: InputMaybe<Scalars['Int']['input']>;
  ngte?: InputMaybe<Scalars['Int']['input']>;
  nin?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>;
  nlt?: InputMaybe<Scalars['Int']['input']>;
  nlte?: InputMaybe<Scalars['Int']['input']>;
};

export type ListStringOperationFilterInput = {
  any?: InputMaybe<Scalars['Boolean']['input']>;
  none?: InputMaybe<StringOperationFilterInput>;
  some?: InputMaybe<StringOperationFilterInput>;
};

export type Mutation = {
  __typename?: 'Mutation';
  createAuthor?: Maybe<CreateAuthorPayload>;
  createWork?: Maybe<CreateWorkPayload>;
  deleteAuthor?: Maybe<DeleteAuthorPayload>;
  deleteWork?: Maybe<DeleteWorkPayload>;
  updateAuthor?: Maybe<UpdateAuthorPayload>;
  updateWork?: Maybe<UpdateWorkPayload>;
};


export type MutationCreateAuthorArgs = {
  input?: InputMaybe<CreateAuthorInput>;
};


export type MutationCreateWorkArgs = {
  input?: InputMaybe<CreateWorkInput>;
};


export type MutationDeleteAuthorArgs = {
  id?: InputMaybe<Scalars['String']['input']>;
};


export type MutationDeleteWorkArgs = {
  id?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateAuthorArgs = {
  id?: InputMaybe<Scalars['String']['input']>;
  input?: InputMaybe<UpdateAuthorInput>;
};


export type MutationUpdateWorkArgs = {
  id?: InputMaybe<Scalars['String']['input']>;
  input?: InputMaybe<UpdateWorkInput>;
};

/** Information about pagination in a connection. */
export type PageInfo = {
  __typename?: 'PageInfo';
  /** When paginating forwards, the cursor to continue. */
  endCursor?: Maybe<Scalars['String']['output']>;
  /** Indicates whether more edges exist following the set defined by the clients arguments. */
  hasNextPage: Scalars['Boolean']['output'];
  /** Indicates whether more edges exist prior the set defined by the clients arguments. */
  hasPreviousPage: Scalars['Boolean']['output'];
  /** When paginating backwards, the cursor to continue. */
  startCursor?: Maybe<Scalars['String']['output']>;
};

export type SchemaRoot = {
  __typename?: 'SchemaRoot';
  author?: Maybe<Author>;
  authors?: Maybe<AuthorsCollectionSegment>;
  genres?: Maybe<GenresConnection>;
  guestbookMessages?: Maybe<GuestbookMessagesConnection>;
  work?: Maybe<Work>;
  works?: Maybe<WorksCollectionSegment>;
};


export type SchemaRootAuthorArgs = {
  id?: InputMaybe<Scalars['String']['input']>;
};


export type SchemaRootAuthorsArgs = {
  order?: InputMaybe<Array<AuthorSortInput>>;
  skip?: InputMaybe<Scalars['Int']['input']>;
  take?: InputMaybe<Scalars['Int']['input']>;
  where?: InputMaybe<AuthorFilterInput>;
};


export type SchemaRootGenresArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  order?: InputMaybe<Array<GenreSortInput>>;
  where?: InputMaybe<GenreFilterInput>;
};


export type SchemaRootGuestbookMessagesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  order?: InputMaybe<Array<GuestbookMessageSortInput>>;
  where?: InputMaybe<GuestbookMessageFilterInput>;
};


export type SchemaRootWorkArgs = {
  id?: InputMaybe<Scalars['String']['input']>;
};


export type SchemaRootWorksArgs = {
  order?: InputMaybe<Array<WorkSortInput>>;
  skip?: InputMaybe<Scalars['Int']['input']>;
  take?: InputMaybe<Scalars['Int']['input']>;
  where?: InputMaybe<WorkFilterInput>;
};

export type SortEnumType =
  | 'ASC'
  | 'DESC';

export type StringOperationFilterInput = {
  and?: InputMaybe<Array<StringOperationFilterInput>>;
  contains?: InputMaybe<Scalars['String']['input']>;
  endsWith?: InputMaybe<Scalars['String']['input']>;
  eq?: InputMaybe<Scalars['String']['input']>;
  in?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  ncontains?: InputMaybe<Scalars['String']['input']>;
  nendsWith?: InputMaybe<Scalars['String']['input']>;
  neq?: InputMaybe<Scalars['String']['input']>;
  nin?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  nstartsWith?: InputMaybe<Scalars['String']['input']>;
  or?: InputMaybe<Array<StringOperationFilterInput>>;
  startsWith?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateAuthorInput = {
  biography?: InputMaybe<Scalars['String']['input']>;
  birthDate?: InputMaybe<Scalars['DateTime']['input']>;
  deathDate?: InputMaybe<Scalars['DateTime']['input']>;
  displayName?: InputMaybe<Scalars['String']['input']>;
  lastName?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  surName?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateAuthorPayload = {
  __typename?: 'UpdateAuthorPayload';
  author?: Maybe<Author>;
};

export type UpdateWorkInput = {
  authorId?: InputMaybe<Scalars['String']['input']>;
  comments?: InputMaybe<Scalars['String']['input']>;
  content?: InputMaybe<Scalars['String']['input']>;
  genres?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  publishedDate?: InputMaybe<Scalars['DateTime']['input']>;
  source?: InputMaybe<Scalars['String']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateWorkPayload = {
  __typename?: 'UpdateWorkPayload';
  work?: Maybe<Work>;
};

export type Work = {
  __typename?: 'Work';
  authorId?: Maybe<Scalars['String']['output']>;
  comments?: Maybe<Scalars['String']['output']>;
  content?: Maybe<Scalars['String']['output']>;
  contentFormat: ContentFormat;
  genres?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  id?: Maybe<Scalars['String']['output']>;
  /** @deprecated No longer supported. */
  legacyId: Scalars['Int']['output'];
  modifiedBy?: Maybe<Scalars['String']['output']>;
  modifiedDate: Scalars['DateTime']['output'];
  publishedBy?: Maybe<Scalars['String']['output']>;
  publishedDate: Scalars['DateTime']['output'];
  source?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
  urlPart?: Maybe<Scalars['String']['output']>;
};

export type WorkFilterInput = {
  and?: InputMaybe<Array<WorkFilterInput>>;
  authorId?: InputMaybe<StringOperationFilterInput>;
  comments?: InputMaybe<StringOperationFilterInput>;
  content?: InputMaybe<StringOperationFilterInput>;
  contentFormat?: InputMaybe<ContentFormatOperationFilterInput>;
  genres?: InputMaybe<ListStringOperationFilterInput>;
  id?: InputMaybe<StringOperationFilterInput>;
  legacyId?: InputMaybe<IntOperationFilterInput>;
  modifiedBy?: InputMaybe<StringOperationFilterInput>;
  modifiedDate?: InputMaybe<DateTimeOperationFilterInput>;
  or?: InputMaybe<Array<WorkFilterInput>>;
  publishedBy?: InputMaybe<StringOperationFilterInput>;
  publishedDate?: InputMaybe<DateTimeOperationFilterInput>;
  source?: InputMaybe<StringOperationFilterInput>;
  title?: InputMaybe<StringOperationFilterInput>;
  urlPart?: InputMaybe<StringOperationFilterInput>;
};

export type WorkSortInput = {
  authorId?: InputMaybe<SortEnumType>;
  comments?: InputMaybe<SortEnumType>;
  content?: InputMaybe<SortEnumType>;
  contentFormat?: InputMaybe<SortEnumType>;
  id?: InputMaybe<SortEnumType>;
  legacyId?: InputMaybe<SortEnumType>;
  modifiedBy?: InputMaybe<SortEnumType>;
  modifiedDate?: InputMaybe<SortEnumType>;
  publishedBy?: InputMaybe<SortEnumType>;
  publishedDate?: InputMaybe<SortEnumType>;
  source?: InputMaybe<SortEnumType>;
  title?: InputMaybe<SortEnumType>;
  urlPart?: InputMaybe<SortEnumType>;
};

/** A segment of a collection. */
export type WorksCollectionSegment = {
  __typename?: 'WorksCollectionSegment';
  /** A flattened list of the items. */
  items?: Maybe<Array<Maybe<Work>>>;
  /** Information to aid in pagination. */
  pageInfo: CollectionSegmentInfo;
  totalCount: Scalars['Int']['output'];
};

export type GetAuthorsQueryVariables = Exact<{
  skip?: InputMaybe<Scalars['Int']['input']>;
  take?: InputMaybe<Scalars['Int']['input']>;
  where?: InputMaybe<AuthorFilterInput>;
}>;


export type GetAuthorsQuery = { __typename?: 'SchemaRoot', authors?: { __typename?: 'AuthorsCollectionSegment', totalCount: number, items?: Array<{ __typename?: 'Author', id?: string | null, urlPart?: string | null, displayName?: string | null, birthDate?: any | null, deathDate?: any | null, addedDate: any, modifiedDate: any } | null> | null } | null };

export type GetAuthorQueryVariables = Exact<{
  id: Scalars['String']['input'];
}>;


export type GetAuthorQuery = { __typename?: 'SchemaRoot', author?: { __typename?: 'Author', id?: string | null, urlPart?: string | null, name?: string | null, surName?: string | null, lastName?: string | null, displayName?: string | null, biography?: string | null, birthDate?: any | null, deathDate?: any | null, addedDate: any, modifiedDate: any } | null };

export type CreateAuthorMutationVariables = Exact<{
  input: CreateAuthorInput;
}>;


export type CreateAuthorMutation = { __typename?: 'Mutation', createAuthor?: { __typename?: 'CreateAuthorPayload', author?: { __typename?: 'Author', id?: string | null, urlPart?: string | null, displayName?: string | null } | null } | null };

export type UpdateAuthorMutationVariables = Exact<{
  id: Scalars['String']['input'];
  input: UpdateAuthorInput;
}>;


export type UpdateAuthorMutation = { __typename?: 'Mutation', updateAuthor?: { __typename?: 'UpdateAuthorPayload', author?: { __typename?: 'Author', id?: string | null, urlPart?: string | null, displayName?: string | null } | null } | null };

export type DeleteAuthorMutationVariables = Exact<{
  id: Scalars['String']['input'];
}>;


export type DeleteAuthorMutation = { __typename?: 'Mutation', deleteAuthor?: { __typename?: 'DeleteAuthorPayload', success: boolean } | null };

export type GetWorksQueryVariables = Exact<{
  skip?: InputMaybe<Scalars['Int']['input']>;
  take?: InputMaybe<Scalars['Int']['input']>;
  where?: InputMaybe<WorkFilterInput>;
}>;


export type GetWorksQuery = { __typename?: 'SchemaRoot', works?: { __typename?: 'WorksCollectionSegment', totalCount: number, items?: Array<{ __typename?: 'Work', id?: string | null, urlPart?: string | null, authorId?: string | null, title?: string | null, publishedDate: any, modifiedDate: any } | null> | null } | null };

export type GetWorkQueryVariables = Exact<{
  id: Scalars['String']['input'];
}>;


export type GetWorkQuery = { __typename?: 'SchemaRoot', work?: { __typename?: 'Work', id?: string | null, urlPart?: string | null, authorId?: string | null, title?: string | null, content?: string | null, genres?: Array<string | null> | null, comments?: string | null, source?: string | null, publishedDate: any, publishedBy?: string | null, modifiedDate: any, modifiedBy?: string | null } | null };

export type CreateWorkMutationVariables = Exact<{
  input: CreateWorkInput;
}>;


export type CreateWorkMutation = { __typename?: 'Mutation', createWork?: { __typename?: 'CreateWorkPayload', work?: { __typename?: 'Work', id?: string | null, urlPart?: string | null, title?: string | null } | null } | null };

export type UpdateWorkMutationVariables = Exact<{
  id: Scalars['String']['input'];
  input: UpdateWorkInput;
}>;


export type UpdateWorkMutation = { __typename?: 'Mutation', updateWork?: { __typename?: 'UpdateWorkPayload', work?: { __typename?: 'Work', id?: string | null, urlPart?: string | null, title?: string | null } | null } | null };

export type DeleteWorkMutationVariables = Exact<{
  id: Scalars['String']['input'];
}>;


export type DeleteWorkMutation = { __typename?: 'Mutation', deleteWork?: { __typename?: 'DeleteWorkPayload', success: boolean } | null };

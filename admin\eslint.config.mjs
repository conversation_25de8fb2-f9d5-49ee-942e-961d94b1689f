import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    rules: {
      // Отключаем проверку неиспользуемых переменных
      "@typescript-eslint/no-unused-vars": "off",
      // Отключаем проверку использования any
      "@typescript-eslint/no-explicit-any": "off",
      // Отключаем проверку на отсутствие display name
      "react/display-name": "off",
      // Отключаем проверку на неиспользуемые выражения
      "@typescript-eslint/no-unused-expressions": "off",
      // Отключаем проверку на пустые интерфейсы
      "@typescript-eslint/no-empty-object-type": "off"
    }
  }
];

export default eslintConfig;

import Link from "next/link";
import Layout from "@/components/layout";
import { getWork, WorkPageProps } from "@/lib/authors";
import { Metadata } from "next";

interface PageProps {
  params: {
    author: string;
    work: string;
  };
};

export default async function WorkPage({ params }: PageProps) {
  const work = await getWork((await params).work);

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-3xl mx-auto">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            {work.title}
          </h1>
          <h2 className="text-xl text-gray-700 mb-4">
            <Link href={`/authors/${work.authorId}`}>
              {work.authorName}
            </Link>
          </h2>

          <article className="prose prose-lg max-w-none">
            <div
              className="leading-relaxed"
              dangerouslySetInnerHTML={{ __html: work.content }}
            />
          </article>
        </div>
      </div>
    </Layout>
  );
}

// Optional: Add metadata generation
export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const work = await getWork((await params).work);
  
  return {
    title: `${work.title} – ${work.authorName}`,
  };
} 
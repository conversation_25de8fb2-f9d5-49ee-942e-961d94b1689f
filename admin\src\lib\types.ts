export interface Author {
  id: string;
  urlPart: string;
  name: string;
  surName?: string;
  lastName?: string;
  displayName: string;
  biography?: string;
  birthDate?: string;
  deathDate?: string;
  addedDate: string;
  modifiedDate: string;
}

export interface CreateAuthorInput {
  name: string;
  surName?: string;
  lastName?: string;
  displayName: string;
  biography?: string;
  birthDate?: string;
  deathDate?: string;
}

export interface UpdateAuthorInput {
  name?: string;
  surName?: string;
  lastName?: string;
  displayName?: string;
  biography?: string;
  birthDate?: string;
  deathDate?: string;
}

export interface Work {
  id: string;
  urlPart: string;
  authorId: string;
  title: string;
  content: string;
  genres?: string[];
  comments?: string;
  source?: string;
  publishedDate: string;
  publishedBy: string;
  modifiedDate: string;
  modifiedBy: string;
}

export interface CreateWorkInput {
  authorId: string;
  title: string;
  content: string;
  genres?: string[];
  comments?: string;
  source?: string;
  publishedDate?: string;
}

export interface UpdateWorkInput {
  authorId?: string;
  title?: string;
  content?: string;
  genres?: string[];
  comments?: string;
  source?: string;
  publishedDate?: string;
}

export interface Genre {
  id: string;
  name: string;
}

export interface User {
  id: string;
  name: string;
  email: string;
  role: string;
}

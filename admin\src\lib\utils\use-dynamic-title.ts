"use client";

import { useEffect, useRef } from "react";

/**
 * Хук для динамического изменения заголовка страницы в клиентских компонентах
 *
 * @param title Заголовок страницы
 * @param suffix Суффикс заголовка (по умолчанию "Шигърият.ру Админ")
 */
export function useDynamicTitle(title: string | null | undefined, suffix: string = "Шигърият.ру Админ") {
  // Используем ref для хранения предыдущего заголовка
  const originalTitleRef = useRef<string | null>(null);

  // Используем ref для отслеживания, был ли уже установлен заголовок
  const isMountedRef = useRef(false);

  useEffect(() => {
    // Пропускаем выполнение на сервере
    if (typeof document === 'undefined') return;

    // Сохраняем оригинальный заголовок только при первом рендере
    if (!isMountedRef.current) {
      originalTitleRef.current = document.title;
      isMountedRef.current = true;
    }

    // Не меняем заголовок, если title не определен
    if (!title) return;

    // Устанавливаем новый заголовок
    const newTitle = suffix ? `${title} | ${suffix}` : title;

    // Меняем заголовок только если он отличается от текущего
    if (document.title !== newTitle) {
      document.title = newTitle;
    }

    // Восстанавливаем оригинальный заголовок при размонтировании компонента
    return () => {
      if (originalTitleRef.current) {
        document.title = originalTitleRef.current;
      }
    };
  }, [title, suffix]);
}

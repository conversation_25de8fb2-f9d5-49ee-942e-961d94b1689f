
import * as types from './graphql';
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';

/**
 * Map of all GraphQL operations in the project.
 *
 * This map has several performance disadvantages:
 * 1. It is not tree-shakeable, so it will include all operations in the project.
 * 2. It is not minifiable, so the string of a GraphQL query will be multiple times inside the bundle.
 * 3. It does not support dead code elimination, so it will add unused operations.
 *
 * Therefore it is highly recommended to use the babel or swc plugin for production.
 * Learn more about it here: https://the-guild.dev/graphql/codegen/plugins/presets/preset-client#reducing-bundle-size
 */
type Documents = {
    "\n  query GetAuthors($skip: Int, $take: Int, $where: AuthorFilterInput) {\n    authors(skip: $skip, take: $take, where: $where) {\n      items {\n        id\n        urlPart\n        displayName\n        birthDate\n        deathDate\n        addedDate\n        modifiedDate\n      }\n      totalCount\n    }\n  }\n": typeof types.GetAuthorsDocument,
    "\n  query GetAuthor($id: String!) {\n    author(id: $id) {\n      id\n      urlPart\n      name\n      surName\n      lastName\n      displayName\n      biography\n      birthDate\n      deathDate\n      addedDate\n      modifiedDate\n    }\n  }\n": typeof types.GetAuthorDocument,
    "\n  mutation CreateAuthor($input: CreateAuthorInput!) {\n    createAuthor(input: $input) {\n      author {\n        id\n        urlPart\n        displayName\n      }\n    }\n  }\n": typeof types.CreateAuthorDocument,
    "\n  mutation UpdateAuthor($id: String!, $input: UpdateAuthorInput!) {\n    updateAuthor(id: $id, input: $input) {\n      author {\n        id\n        urlPart\n        displayName\n      }\n    }\n  }\n": typeof types.UpdateAuthorDocument,
    "\n  mutation DeleteAuthor($id: String!) {\n    deleteAuthor(id: $id) {\n      success\n    }\n  }\n": typeof types.DeleteAuthorDocument,
    "\n  query GetWorks($skip: Int, $take: Int, $where: WorkFilterInput) {\n    works(skip: $skip, take: $take, where: $where) {\n      items {\n        id\n        urlPart\n        authorId\n        title\n        publishedDate\n        modifiedDate\n      }\n      totalCount\n    }\n  }\n": typeof types.GetWorksDocument,
    "\n  query GetWork($id: String!) {\n    work(id: $id) {\n      id\n      urlPart\n      authorId\n      title\n      content\n      genres\n      comments\n      source\n      publishedDate\n      publishedBy\n      modifiedDate\n      modifiedBy\n    }\n  }\n": typeof types.GetWorkDocument,
    "\n  mutation CreateWork($input: CreateWorkInput!) {\n    createWork(input: $input) {\n      work {\n        id\n        urlPart\n        title\n      }\n    }\n  }\n": typeof types.CreateWorkDocument,
    "\n  mutation UpdateWork($id: String!, $input: UpdateWorkInput!) {\n    updateWork(id: $id, input: $input) {\n      work {\n        id\n        urlPart\n        title\n      }\n    }\n  }\n": typeof types.UpdateWorkDocument,
    "\n  mutation DeleteWork($id: String!) {\n    deleteWork(id: $id) {\n      success\n    }\n  }\n": typeof types.DeleteWorkDocument,
};
const documents: Documents = {
    "\n  query GetAuthors($skip: Int, $take: Int, $where: AuthorFilterInput) {\n    authors(skip: $skip, take: $take, where: $where) {\n      items {\n        id\n        urlPart\n        displayName\n        birthDate\n        deathDate\n        addedDate\n        modifiedDate\n      }\n      totalCount\n    }\n  }\n": types.GetAuthorsDocument,
    "\n  query GetAuthor($id: String!) {\n    author(id: $id) {\n      id\n      urlPart\n      name\n      surName\n      lastName\n      displayName\n      biography\n      birthDate\n      deathDate\n      addedDate\n      modifiedDate\n    }\n  }\n": types.GetAuthorDocument,
    "\n  mutation CreateAuthor($input: CreateAuthorInput!) {\n    createAuthor(input: $input) {\n      author {\n        id\n        urlPart\n        displayName\n      }\n    }\n  }\n": types.CreateAuthorDocument,
    "\n  mutation UpdateAuthor($id: String!, $input: UpdateAuthorInput!) {\n    updateAuthor(id: $id, input: $input) {\n      author {\n        id\n        urlPart\n        displayName\n      }\n    }\n  }\n": types.UpdateAuthorDocument,
    "\n  mutation DeleteAuthor($id: String!) {\n    deleteAuthor(id: $id) {\n      success\n    }\n  }\n": types.DeleteAuthorDocument,
    "\n  query GetWorks($skip: Int, $take: Int, $where: WorkFilterInput) {\n    works(skip: $skip, take: $take, where: $where) {\n      items {\n        id\n        urlPart\n        authorId\n        title\n        publishedDate\n        modifiedDate\n      }\n      totalCount\n    }\n  }\n": types.GetWorksDocument,
    "\n  query GetWork($id: String!) {\n    work(id: $id) {\n      id\n      urlPart\n      authorId\n      title\n      content\n      genres\n      comments\n      source\n      publishedDate\n      publishedBy\n      modifiedDate\n      modifiedBy\n    }\n  }\n": types.GetWorkDocument,
    "\n  mutation CreateWork($input: CreateWorkInput!) {\n    createWork(input: $input) {\n      work {\n        id\n        urlPart\n        title\n      }\n    }\n  }\n": types.CreateWorkDocument,
    "\n  mutation UpdateWork($id: String!, $input: UpdateWorkInput!) {\n    updateWork(id: $id, input: $input) {\n      work {\n        id\n        urlPart\n        title\n      }\n    }\n  }\n": types.UpdateWorkDocument,
    "\n  mutation DeleteWork($id: String!) {\n    deleteWork(id: $id) {\n      success\n    }\n  }\n": types.DeleteWorkDocument,
};

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 *
 *
 * @example
 * ```ts
 * const query = graphql(`query GetUser($id: ID!) { user(id: $id) { name } }`);
 * ```
 *
 * The query argument is unknown!
 * Please regenerate the types.
 */
export function graphql(source: string): unknown;

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetAuthors($skip: Int, $take: Int, $where: AuthorFilterInput) {\n    authors(skip: $skip, take: $take, where: $where) {\n      items {\n        id\n        urlPart\n        displayName\n        birthDate\n        deathDate\n        addedDate\n        modifiedDate\n      }\n      totalCount\n    }\n  }\n"): (typeof documents)["\n  query GetAuthors($skip: Int, $take: Int, $where: AuthorFilterInput) {\n    authors(skip: $skip, take: $take, where: $where) {\n      items {\n        id\n        urlPart\n        displayName\n        birthDate\n        deathDate\n        addedDate\n        modifiedDate\n      }\n      totalCount\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetAuthor($id: String!) {\n    author(id: $id) {\n      id\n      urlPart\n      name\n      surName\n      lastName\n      displayName\n      biography\n      birthDate\n      deathDate\n      addedDate\n      modifiedDate\n    }\n  }\n"): (typeof documents)["\n  query GetAuthor($id: String!) {\n    author(id: $id) {\n      id\n      urlPart\n      name\n      surName\n      lastName\n      displayName\n      biography\n      birthDate\n      deathDate\n      addedDate\n      modifiedDate\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation CreateAuthor($input: CreateAuthorInput!) {\n    createAuthor(input: $input) {\n      author {\n        id\n        urlPart\n        displayName\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation CreateAuthor($input: CreateAuthorInput!) {\n    createAuthor(input: $input) {\n      author {\n        id\n        urlPart\n        displayName\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdateAuthor($id: String!, $input: UpdateAuthorInput!) {\n    updateAuthor(id: $id, input: $input) {\n      author {\n        id\n        urlPart\n        displayName\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation UpdateAuthor($id: String!, $input: UpdateAuthorInput!) {\n    updateAuthor(id: $id, input: $input) {\n      author {\n        id\n        urlPart\n        displayName\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation DeleteAuthor($id: String!) {\n    deleteAuthor(id: $id) {\n      success\n    }\n  }\n"): (typeof documents)["\n  mutation DeleteAuthor($id: String!) {\n    deleteAuthor(id: $id) {\n      success\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetWorks($skip: Int, $take: Int, $where: WorkFilterInput) {\n    works(skip: $skip, take: $take, where: $where) {\n      items {\n        id\n        urlPart\n        authorId\n        title\n        publishedDate\n        modifiedDate\n      }\n      totalCount\n    }\n  }\n"): (typeof documents)["\n  query GetWorks($skip: Int, $take: Int, $where: WorkFilterInput) {\n    works(skip: $skip, take: $take, where: $where) {\n      items {\n        id\n        urlPart\n        authorId\n        title\n        publishedDate\n        modifiedDate\n      }\n      totalCount\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetWork($id: String!) {\n    work(id: $id) {\n      id\n      urlPart\n      authorId\n      title\n      content\n      genres\n      comments\n      source\n      publishedDate\n      publishedBy\n      modifiedDate\n      modifiedBy\n    }\n  }\n"): (typeof documents)["\n  query GetWork($id: String!) {\n    work(id: $id) {\n      id\n      urlPart\n      authorId\n      title\n      content\n      genres\n      comments\n      source\n      publishedDate\n      publishedBy\n      modifiedDate\n      modifiedBy\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation CreateWork($input: CreateWorkInput!) {\n    createWork(input: $input) {\n      work {\n        id\n        urlPart\n        title\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation CreateWork($input: CreateWorkInput!) {\n    createWork(input: $input) {\n      work {\n        id\n        urlPart\n        title\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdateWork($id: String!, $input: UpdateWorkInput!) {\n    updateWork(id: $id, input: $input) {\n      work {\n        id\n        urlPart\n        title\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation UpdateWork($id: String!, $input: UpdateWorkInput!) {\n    updateWork(id: $id, input: $input) {\n      work {\n        id\n        urlPart\n        title\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation DeleteWork($id: String!) {\n    deleteWork(id: $id) {\n      success\n    }\n  }\n"): (typeof documents)["\n  mutation DeleteWork($id: String!) {\n    deleteWork(id: $id) {\n      success\n    }\n  }\n"];

export function graphql(source: string) {
  return (documents as any)[source] ?? {};
}

export type DocumentType<TDocumentNode extends DocumentNode<any, any>> = TDocumentNode extends DocumentNode<  infer TType,  any>  ? TType  : never;
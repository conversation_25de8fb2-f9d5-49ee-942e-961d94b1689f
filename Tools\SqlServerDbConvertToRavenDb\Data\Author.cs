﻿using System;
using Newtonsoft.Json;

// ReSharper disable CheckNamespace
// namespace saved in RavenDb
namespace Shigriyat.Data
// ReSharper restore CheckNamespace
{
    public sealed class Author
    {
        public const string IdPrefix = "authors/";
        private string urlPart;

        /// <summary>
        /// Идентификатор: IdPrefix + UrlPart
        /// </summary>
        public string Id { get; private set; }
        
        [JsonIgnore]
        public string UrlPart
        {
            get
            {
                if (urlPart != null)
                    return urlPart;

                if (string.IsNullOrEmpty(Id))
                    return Id;
                if (Id.StartsWith(IdPrefix))
                    return Id.Substring(IdPrefix.Length);
                throw new InvalidOperationException("Id not start with prefix");
            }
            set
            {
                urlPart = value;
                Id = IdPrefix + value;
            }
        }

        public string Name { get; set; }
        public string SurName { get; set; }
        public string LastName { get; set; }
        public string DisplayName { get; set; }

        public string Biography { get; set; }

        public DateTime? BirthDate { get; set; }
        public DateTime? DeathDate { get; set; }
        
        public DateTime AddedDate { get; set; }
        public DateTime ModifiedDate { get; set; }

        /// <summary>
        /// Старый id (int). Удалить позже, если не пригодится.
        /// </summary>
        [Obsolete]
        public int LegacyId { get; set; }
    }
}

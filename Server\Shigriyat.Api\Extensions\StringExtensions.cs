﻿using System;
using JetBrains.Annotations;

namespace Shigriyat.Api.Extensions
{
    internal static class StringExtensions
    {
        public static string TrimPrefix([NotNull] this string text, [NotNull] string prefix)
        {
            if (text == null) 
                throw new ArgumentNullException(nameof(text));
            if (prefix == null) 
                throw new ArgumentNullException(nameof(prefix));
            if (text.Length < prefix.Length)
                throw new ArgumentOutOfRangeException(nameof(prefix), "Prefix cannot be longer that text");
            if (!text.StartsWith(prefix, StringComparison.InvariantCultureIgnoreCase))
                throw new ArgumentOutOfRangeException(nameof(text), "Text doesn't start with prefix");
            
            return text.Substring(prefix.Length);
        }
    }
}
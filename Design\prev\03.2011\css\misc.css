﻿acronym {
    border-bottom: 1px dotted #777777;
    cursor: help;
}

blockquote {
    background: url("../img/q-r.gif") no-repeat scroll 12px 12px #F8F8F8;
    border: 1px solid #F0F0F0;
    color: #555555;
    font-size: 1.25em;
    font-style: italic;
    font-weight: normal;
    line-height: 1.5em;
    margin: 10px 25px;
    padding: 10px 20px 10px 32px;
}

blockquote, q {
    quotes: "" "";
}

blockquote > p {
    margin: 10px 25px;
}

ul {
    list-style: disc outside none;
}
ol {
    list-style: decimal outside none;
}

dt {
    color: #1980AF;
    font-weight: bold;
}
dd {
    padding-left: 20px;
}
dl {
    margin: 10px 25px;
}

ul, ol {
  margin: 10px 25px;
  padding: 0 20px;
  margin-bottom: 24px;
    /* Remember, if your magic number is
    different to this, use your own. */
}

ul {
  list-style: square outside;
}

ul ul, ol ol {
  margin: 0 0 0 60px;
}

.float-left {
    float: left;
}
.float-right {
    float: right;
}
.align-left {
    text-align: left;
}
.align-right {
    text-align: right;
}

"use client";

import { Button } from "@/components/ui/button";
import { signOut } from "next-auth/react";
import { useState, useEffect } from "react";
// import ThemeToggle from "@/components/ui/theme-toggle";

interface AdminHeaderProps {
  user?: {
    name?: string | null;
    email?: string | null;
    image?: string | null;
  };
}

export function AdminHeader({ user }: AdminHeaderProps) {
  const [isMounted, setIsMounted] = useState(false);

  // Устанавливаем isMounted в true после первого рендера
  useEffect(() => {
    setIsMounted(true);
  }, []);

  const handleLogout = () => {
    // Удаляем временный cookie
    document.cookie = "admin-auth=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";

    // Выходим через NextAuth
    signOut({ callbackUrl: "/login" });
  };

  // Если компонент не смонтирован, возвращаем пустой заголовок
  if (!isMounted) {
    return <header className="bg-white border-b border-slate-200 h-16"></header>;
  }

  return (
    <header className="bg-white border-b border-slate-200 h-16 flex items-center justify-between px-6">
      <h1 className="text-xl font-semibold">Шигърият.ру Админ</h1>
      <div className="flex items-center gap-4">
        <span className="text-sm text-slate-600">
          {user?.name || user?.email}
        </span>
        <Button
          variant="outline"
          size="sm"
          onClick={handleLogout}
        >
          Чыгу
        </Button>
        {/* not working properly yet */}
        {/* <ThemeToggle /> */}
      </div>
    </header>
  );
}

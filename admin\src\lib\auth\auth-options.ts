import type { NextAuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";

export const authOptions: NextAuthOptions = {
  debug: process.env.NODE_ENV === "development",
  logger: {
    error(code, metadata) {
      console.error({ type: 'inside-auth-js', code, metadata });
    },
    warn(code) {
      console.warn({ type: 'inside-auth-js', code });
    },
    debug(code, metadata) {
      console.log({ type: 'inside-auth-js', code, metadata });
    },
  },
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        username: { label: "Username", type: "text" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        try {
          if (process.env.NODE_ENV === "development") {
            console.log("Authorizing with credentials:", { username: credentials?.username });
          }

          //TODO Временное решение для тестирования
          if (credentials?.username === "admin" && credentials?.password === "admin123") {
            console.log("Using test credentials");
            return {
              id: "1",
              name: "Admin",
              email: "<EMAIL>",
              role: "Admin",
              accessToken: "test-token"
            };
          }

          // Запрос к API для аутентификации
          try {
            const API_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:3556";
            console.log("API URL:", API_URL);

            const response = await fetch(`${API_URL}/api/auth/login`, {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                username: credentials?.username,
                password: credentials?.password,
              }),
            });

            if (process.env.NODE_ENV === "development") {
              console.log("Auth API response status:", response.status);
            }

            if (!response.ok) {
              if (process.env.NODE_ENV === "development") {
                console.error("Auth API error:", response.statusText);
              }
              return null;
            }

            const user = await response.json();

            if (process.env.NODE_ENV === "development") {
              console.log("Auth API response user:", user ? "exists" : "null");
            }

            if (user) {
              return user;
            }
          } catch (apiError) {
            console.error("API request failed:", apiError);
            // Возвращаем null, чтобы показать ошибку аутентификации
          }

          return null;
        } catch (error) {
          console.error("Auth exception:", error);
          return null;
        }
      }
    })
  ],
  pages: {
    signIn: "/login",
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.role = user.role;
        token.accessToken = user.accessToken;
      }
      return token;
    },
    async session({ session, token }) {
      if (session.user) {
        session.user.id = token.id as string;
        session.user.role = token.role as string;
        session.accessToken = token.accessToken as string;
      }
      return session;
    },
  },
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 дней
  },
  cookies: {
    sessionToken: {
      name: "next-auth.session-token",
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
      },
    },
  },
};



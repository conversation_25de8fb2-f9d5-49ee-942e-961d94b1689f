schema {
  query: SchemaRoot
  mutation: Mutation
}

"""
The purpose of the `cost` directive is to define a `weight` for GraphQL types, fields, and arguments. Static analysis can use these weights when calculating the overall cost of a query or response.
"""
directive @cost(
  """
  The `weight` argument defines what value to add to the overall cost for every appearance, or possible appearance, of a type, field, argument, etc.
  """
  weight: String!
) on ARGUMENT_DEFINITION | ENUM | FIELD_DEFINITION | INPUT_FIELD_DEFINITION | OBJECT | SCALAR

"""
The purpose of the `@listSize` directive is to either inform the static analysis about the size of returned lists (if that information is statically available), or to point the analysis to where to find that information.
"""
directive @listSize(
  """
  The `assumedSize` argument can be used to statically define the maximum length of a list returned by a field.
  """
  assumedSize: Int

  """
  The `requireOneSlicingArgument` argument can be used to inform the static analysis that it should expect that exactly one of the defined slicing arguments is present in a query. If that is not the case (i.e., if none or multiple slicing arguments are present), the static analysis may throw an error.
  """
  requireOneSlicingArgument: Boolean! = true

  """
  The `sizedFields` argument can be used to define that the value of the `assumedSize` argument or of a slicing argument does not affect the size of a list returned by a field itself, but that of a list returned by one of its sub-fields.
  """
  sizedFields: [String!]

  """
  The `slicingArgumentDefaultValue` argument can be used to define a default value for a slicing argument, which is used if the argument is not present in a query.
  """
  slicingArgumentDefaultValue: Int

  """
  The `slicingArguments` argument can be used to define which of the field's arguments with numeric type are slicing arguments, so that their value determines the size of the list returned by that field. It may specify a list of multiple slicing arguments.
  """
  slicingArguments: [String!]
) on FIELD_DEFINITION

"""Defines when a policy shall be executed."""
enum ApplyPolicy {
  """After the resolver was executed."""
  AFTER_RESOLVER

  """Before the resolver was executed."""
  BEFORE_RESOLVER

  """The policy is applied in the validation step before the execution."""
  VALIDATION
}

type Author {
  addedDate: DateTime!
  biography: String
  birthDate: DateTime
  deathDate: DateTime
  displayName: String
  id: String
  lastName: String
  legacyId: Int! @deprecated(reason: "No longer supported.")
  modifiedDate: DateTime!
  name: String
  surName: String
  urlPart: String
}

input AuthorFilterInput {
  addedDate: DateTimeOperationFilterInput
  and: [AuthorFilterInput!]
  biography: StringOperationFilterInput
  birthDate: DateTimeOperationFilterInput
  deathDate: DateTimeOperationFilterInput
  displayName: StringOperationFilterInput
  id: StringOperationFilterInput
  lastName: StringOperationFilterInput
  legacyId: IntOperationFilterInput
  modifiedDate: DateTimeOperationFilterInput
  name: StringOperationFilterInput
  or: [AuthorFilterInput!]
  surName: StringOperationFilterInput
  urlPart: StringOperationFilterInput
}

input AuthorSortInput {
  addedDate: SortEnumType
  biography: SortEnumType
  birthDate: SortEnumType
  deathDate: SortEnumType
  displayName: SortEnumType
  id: SortEnumType
  lastName: SortEnumType
  legacyId: SortEnumType
  modifiedDate: SortEnumType
  name: SortEnumType
  surName: SortEnumType
  urlPart: SortEnumType
}

"""A segment of a collection."""
type AuthorsCollectionSegment {
  """A flattened list of the items."""
  items: [Author]

  """Information to aid in pagination."""
  pageInfo: CollectionSegmentInfo!
  totalCount: Int!
}

"""Information about the offset pagination."""
type CollectionSegmentInfo {
  """
  Indicates whether more items exist following the set defined by the clients arguments.
  """
  hasNextPage: Boolean!

  """
  Indicates whether more items exist prior the set defined by the clients arguments.
  """
  hasPreviousPage: Boolean!
}

enum ContentFormat {
  BLOCKS
  HTML
}

input ContentFormatOperationFilterInput {
  eq: ContentFormat
  in: [ContentFormat!]
  neq: ContentFormat
  nin: [ContentFormat!]
}

input CreateAuthorInput {
  biography: String
  birthDate: DateTime
  deathDate: DateTime
  displayName: String
  lastName: String
  name: String
  surName: String
  urlPart: String
}

type CreateAuthorPayload {
  author: Author
}

input CreateWorkInput {
  authorId: String
  comments: String
  content: String
  genres: [String]
  publishedDate: DateTime
  source: String
  title: String
}

type CreateWorkPayload {
  work: Work
}

"""The `DateTime` scalar represents an ISO-8601 compliant date time type."""
scalar DateTime

input DateTimeOperationFilterInput {
  eq: DateTime
  gt: DateTime
  gte: DateTime
  in: [DateTime]
  lt: DateTime
  lte: DateTime
  neq: DateTime
  ngt: DateTime
  ngte: DateTime
  nin: [DateTime]
  nlt: DateTime
  nlte: DateTime
}

type DeleteAuthorPayload {
  success: Boolean!
}

type DeleteWorkPayload {
  success: Boolean!
}

type Genre {
  iconCssClass: String
  id: String
  title: String
}

input GenreFilterInput {
  and: [GenreFilterInput!]
  iconCssClass: StringOperationFilterInput
  id: StringOperationFilterInput
  or: [GenreFilterInput!]
  title: StringOperationFilterInput
}

input GenreSortInput {
  iconCssClass: SortEnumType
  id: SortEnumType
  title: SortEnumType
}

"""A connection to a list of items."""
type GenresConnection {
  """A list of edges."""
  edges: [GenresEdge!]

  """A flattened list of the nodes."""
  nodes: [Genre]

  """Information to aid in pagination."""
  pageInfo: PageInfo!
}

"""An edge in a connection."""
type GenresEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: Genre
}

type GuestbookMessage {
  answerAuthorName: String
  answerDate: DateTime
  answerMessage: String
  authorEmail: String
  authorName: String
  message: String
  messageDate: DateTime!
}

input GuestbookMessageFilterInput {
  and: [GuestbookMessageFilterInput!]
  answerAuthorName: StringOperationFilterInput
  answerDate: DateTimeOperationFilterInput
  answerMessage: StringOperationFilterInput
  authorEmail: StringOperationFilterInput
  authorName: StringOperationFilterInput
  message: StringOperationFilterInput
  messageDate: DateTimeOperationFilterInput
  or: [GuestbookMessageFilterInput!]
}

input GuestbookMessageSortInput {
  answerAuthorName: SortEnumType
  answerDate: SortEnumType
  answerMessage: SortEnumType
  authorEmail: SortEnumType
  authorName: SortEnumType
  message: SortEnumType
  messageDate: SortEnumType
}

"""A connection to a list of items."""
type GuestbookMessagesConnection {
  """A list of edges."""
  edges: [GuestbookMessagesEdge!]

  """A flattened list of the nodes."""
  nodes: [GuestbookMessage]

  """Information to aid in pagination."""
  pageInfo: PageInfo!
}

"""An edge in a connection."""
type GuestbookMessagesEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: GuestbookMessage
}

input IntOperationFilterInput {
  eq: Int
  gt: Int
  gte: Int
  in: [Int]
  lt: Int
  lte: Int
  neq: Int
  ngt: Int
  ngte: Int
  nin: [Int]
  nlt: Int
  nlte: Int
}

input ListStringOperationFilterInput {
  any: Boolean
  none: StringOperationFilterInput
  some: StringOperationFilterInput
}

type Mutation {
  createAuthor(input: CreateAuthorInput): CreateAuthorPayload
  createWork(input: CreateWorkInput): CreateWorkPayload
  deleteAuthor(id: String): DeleteAuthorPayload
  deleteWork(id: String): DeleteWorkPayload
  updateAuthor(id: String, input: UpdateAuthorInput): UpdateAuthorPayload
  updateWork(id: String, input: UpdateWorkInput): UpdateWorkPayload
}

"""Information about pagination in a connection."""
type PageInfo {
  """When paginating forwards, the cursor to continue."""
  endCursor: String

  """
  Indicates whether more edges exist following the set defined by the clients arguments.
  """
  hasNextPage: Boolean!

  """
  Indicates whether more edges exist prior the set defined by the clients arguments.
  """
  hasPreviousPage: Boolean!

  """When paginating backwards, the cursor to continue."""
  startCursor: String
}

type SchemaRoot {
  author(id: String): Author
  authors(order: [AuthorSortInput!], skip: Int, take: Int, where: AuthorFilterInput): AuthorsCollectionSegment
  genres(
    """Returns the elements in the list that come after the specified cursor."""
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """Returns the first _n_ elements from the list."""
    first: Int

    """Returns the last _n_ elements from the list."""
    last: Int
    order: [GenreSortInput!]
    where: GenreFilterInput
  ): GenresConnection
  guestbookMessages(
    """Returns the elements in the list that come after the specified cursor."""
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """Returns the first _n_ elements from the list."""
    first: Int

    """Returns the last _n_ elements from the list."""
    last: Int
    order: [GuestbookMessageSortInput!]
    where: GuestbookMessageFilterInput
  ): GuestbookMessagesConnection
  work(id: String): Work
  works(order: [WorkSortInput!], skip: Int, take: Int, where: WorkFilterInput): WorksCollectionSegment
}

enum SortEnumType {
  ASC
  DESC
}

input StringOperationFilterInput {
  and: [StringOperationFilterInput!]
  contains: String
  endsWith: String
  eq: String
  in: [String]
  ncontains: String
  nendsWith: String
  neq: String
  nin: [String]
  nstartsWith: String
  or: [StringOperationFilterInput!]
  startsWith: String
}

input UpdateAuthorInput {
  biography: String
  birthDate: DateTime
  deathDate: DateTime
  displayName: String
  lastName: String
  name: String
  surName: String
}

type UpdateAuthorPayload {
  author: Author
}

input UpdateWorkInput {
  authorId: String
  comments: String
  content: String
  genres: [String]
  publishedDate: DateTime
  source: String
  title: String
}

type UpdateWorkPayload {
  work: Work
}

type Work {
  authorId: String
  comments: String
  content: String
  contentFormat: ContentFormat!
  genres: [String]
  id: String
  legacyId: Int! @deprecated(reason: "No longer supported.")
  modifiedBy: String
  modifiedDate: DateTime!
  publishedBy: String
  publishedDate: DateTime!
  source: String
  title: String
  urlPart: String
}

input WorkFilterInput {
  and: [WorkFilterInput!]
  authorId: StringOperationFilterInput
  comments: StringOperationFilterInput
  content: StringOperationFilterInput
  contentFormat: ContentFormatOperationFilterInput
  genres: ListStringOperationFilterInput
  id: StringOperationFilterInput
  legacyId: IntOperationFilterInput
  modifiedBy: StringOperationFilterInput
  modifiedDate: DateTimeOperationFilterInput
  or: [WorkFilterInput!]
  publishedBy: StringOperationFilterInput
  publishedDate: DateTimeOperationFilterInput
  source: StringOperationFilterInput
  title: StringOperationFilterInput
  urlPart: StringOperationFilterInput
}

input WorkSortInput {
  authorId: SortEnumType
  comments: SortEnumType
  content: SortEnumType
  contentFormat: SortEnumType
  id: SortEnumType
  legacyId: SortEnumType
  modifiedBy: SortEnumType
  modifiedDate: SortEnumType
  publishedBy: SortEnumType
  publishedDate: SortEnumType
  source: SortEnumType
  title: SortEnumType
  urlPart: SortEnumType
}

"""A segment of a collection."""
type WorksCollectionSegment {
  """A flattened list of the items."""
  items: [Work]

  """Information to aid in pagination."""
  pageInfo: CollectionSegmentInfo!
  totalCount: Int!
}
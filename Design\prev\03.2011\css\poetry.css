﻿.author {
	color:#777;
	margin:0 0 0.25em 0;
	font-style:italic;
}

a {
	color:#000;
	text-decoration:underline;
}

a:hover	{
	color:#c00;
}
	
div.header {
	background:#ebebe1 url("../img/back_feather.jpg") bottom right no-repeat;
	height:170px;
	color:#444;
}
	
.mainmenu {
	margin: 0;
	font-size:0.9em;
	padding: 1em 0;
	border-bottom:1px solid #ccccc2;
}

.mainmenu li {
	display:inline;
}

.mainmenu a, .mainmenu_current {
	padding:0.25em 0.5em;
	margin:0 1em;
	color:#58a;
}
	
.mainmenu a:hover {
	color:#c00;
}	

.mainmenu_current {
	background:#ebebe1;
}

.mainmenu_current  a {
	color:#000;
}

.logo {
	padding:20px 0 0 50px;
	float:left;
	/* background: url("../img/logo.gif") center no-repeat; */
}
	
.logo_left_text {
	float:left;
	font-size:22px;
	font-style:italic;
	padding:30px 0 0 90px;
	line-height:1.1em;
	color:#444;
	}
	
.logo_left_text_padding {
	padding:0 0 0 5em;
	}		
	
.q_left {
	float:left;
	display:inline;
	font-size:18px;
	margin:20px 0 0 30px;
	line-height:1.5em;
	background:	url("../img/q-l.gif") bottom left no-repeat;
	padding:0 0 0 38px;
	}
	
.q_right {
	float:left;
	background: url("../img/q-r.gif") top left no-repeat;
	margin:20px 0 0 15px;
	padding:0 0 0 30px;
	}
	
.q_autor {
	text-align:right;
	font-style:italic;
	}
	
.catalog {
	margin:1em 0 0.75em 0;
	text-align:center;
}

.catalog a {
	padding:0 0.25em 0 0.25em;
	margin:0;
	color:#666;
}
	
.catalog a:hover {
	color:#c00;
}
	
.catalog_hole {
	margin:0 2em 0 0;
	}	

.left_spacer {
	background: url("../img/left.gif") 1.5em center no-repeat;
	height:4em;
	}

.left_header {
	font-variant:small-caps;
	font-weight:bold;
	padding:0 0 0.5em 0;
	}		

.left_header a {
	color:#58a;
	}		

.left_header a:hover {
	color:#c00;
}		
	
.left_text {
	margin:0 0 0.5em 0;
	line-height:1.2em;
}

.left_text a {
	margin:0 0 0.5em 0;
	line-height:1.2em;
	display:block;
	color:#666;
}
	
.left_text a:hover {
	color:#c00;
	}		

.h_left {
	background: url("../img/h-l.gif") left 0.3em no-repeat;
}
	
.h_right {
	background:	url("../img/h-r.gif") right 0.3em no-repeat;
	padding:0 2em;
	line-height:1.75em;
}
	
.h_bold_line {
	font-size:0.9em;
	margin:0 0 2px 0;
	border-bottom:3px solid #ccccc2;
	}

.h_line {
	clear:both;
	border-bottom:1px solid #ccccc2;
	}	

.autor_menu {
	margin:0 0 1.5em 0;
	}

.autor_menu a, .autor_menu_current {
	padding:0 0.5em 0.25em 0.5em;
	margin:0 1em 0 0;
	color:#58a;
	}

.autor_menu a:hover {
	color:#c00;
}

.autor_menu_current {
	background:#EBEBE1;
	color:#000;
}

.list {
	font-size:1.125em;
	padding:0 0 1.5em 0;
}

.list a {
	margin:0 0 0.5em 0;
	display:block;
}

.spacer_line {
	clear:both;
	border-bottom:1px solid #ccccc2;
	font-size:8px;
	margin:0 0 2px 0;
}
	
.spacer_logo {
	clear:both;
	height:0.9em;
}}	
	
.copy {
	color:#777;
}
	
.answer	{
	background:#ebebe1;
	padding:1em 1em 0 1em;
	margin:0 0 1.5em 0;
}
	
.search_box {
	width:120px;
}	
	
.print {
	display:none;	
}
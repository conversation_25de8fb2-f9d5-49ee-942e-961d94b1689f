﻿using System;
using Newtonsoft.Json;

// ReSharper disable CheckNamespace
// namespace saved in RavenDb
namespace Shigriyat.Data
// ReSharper restore CheckNamespace
{
    public sealed class Work
    {
        public const string IdPrefix = "works/";
        private string urlPart;

        /// <summary>
        /// Идентификатор: IdPrefix + UrlPart
        /// </summary>
        public string Id { get; private set; }

        [JsonIgnore]
        public string UrlPart
        {
            get
            {
                if (urlPart != null)
                    return urlPart;

                if (string.IsNullOrEmpty(Id))
                    return Id;
                if (Id.StartsWith(IdPrefix))
                    return Id.Substring(IdPrefix.Length);
                throw new InvalidOperationException("Id not start with prefix");
            }
            set
            {
                urlPart = value;
                Id = IdPrefix + value;
            }
        }

        public string AuthorId { get; set; }

        public string Title { get; set; }
        public string Content { get; set; }

        public string Comments { get; set; }
        public string Source { get; set; }

        public DateTime PublishedDate { get; set; }
        public string PublishedBy { get; set; }

        public DateTime ModifiedDate { get; set; }
        public string ModifiedBy { get; set; }

        /// <summary>
        /// Старый id (int). Удалить позже, если не пригодится.
        /// </summary>
        [Obsolete]
        public int LegacyId { get; set; }
    }
}

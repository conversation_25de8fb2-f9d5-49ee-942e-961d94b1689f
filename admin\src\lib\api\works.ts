import { getSession } from "next-auth/react";
import { gql } from "graphql-tag";
import {
  GetWorksQuery,
  GetWorksQueryVariables,
  GetWorkQuery,
  GetWorkQueryVariables,
  CreateWorkMutation,
  CreateWorkMutationVariables,
  UpdateWorkMutation,
  UpdateWorkMutationVariables,
  DeleteWorkMutation,
  DeleteWorkMutationVariables,
  Work,
  CreateWorkInput,
  UpdateWorkInput
} from "@/lib/graphql/generated/graphql";

const API_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:3556";

async function fetchWithAuth(url: string, options: RequestInit = {}) {
  const session = await getSession();

  return fetch(url, {
    ...options,
    headers: {
      ...options.headers,
      "Content-Type": "application/json",
      Authorization: `Bearer ${session?.accessToken}`,
    },
  });
}

// GraphQL запросы и мутации
const GET_WORKS = gql`
  query GetWorks($skip: Int, $take: Int, $where: WorkFilterInput) {
    works(skip: $skip, take: $take, where: $where) {
      items {
        id
        urlPart
        authorId
        title
        publishedDate
        modifiedDate
      }
      totalCount
    }
  }
`;

const GET_WORK = gql`
  query GetWork($id: String!) {
    work(id: $id) {
      id
      urlPart
      authorId
      title
      content
      genres
      comments
      source
      publishedDate
      publishedBy
      modifiedDate
      modifiedBy
    }
  }
`;

const CREATE_WORK = gql`
  mutation CreateWork($input: CreateWorkInput!) {
    createWork(input: $input) {
      work {
        id
        urlPart
        title
      }
    }
  }
`;

const UPDATE_WORK = gql`
  mutation UpdateWork($id: String!, $input: UpdateWorkInput!) {
    updateWork(id: $id, input: $input) {
      work {
        id
        urlPart
        title
      }
    }
  }
`;

const DELETE_WORK = gql`
  mutation DeleteWork($id: ID!) {
    deleteWork(id: $id) {
      success
    }
  }
`;

export async function getWorks(
  page: number = 1,
  pageSize: number = 10,
  filter?: string,
  authorId?: string
): Promise<{ works: Work[], totalCount: number }> {
  const skip = (page - 1) * pageSize;

  // Создаем объект фильтрации для GraphQL запроса
  let where: any = undefined;

  if (filter || authorId) {
    where = {};

    if (filter) {
      where.title = { contains: filter };
    }

    if (authorId) {
      where.authorId = { eq: authorId };
    }
  }

  const response = await fetchWithAuth(`${API_URL}/graphql`, {
    method: "POST",
    body: JSON.stringify({
      query: GET_WORKS.loc?.source.body,
      variables: { skip, take: pageSize, where }
    }),
  });

  const data = await response.json();

  if (data.errors) {
    throw new Error(data.errors[0].message);
  }

  const result = data.data as GetWorksQuery;
  return {
    works: result.works.items as Work[],
    totalCount: result.works.totalCount
  };
}

export async function getWork(id: string): Promise<Work> {
  const response = await fetchWithAuth(`${API_URL}/graphql`, {
    method: "POST",
    body: JSON.stringify({
      query: GET_WORK.loc?.source.body,
      variables: { id },
    }),
  });

  const data = await response.json();

  if (data.errors) {
    throw new Error(data.errors[0].message);
  }

  const result = data.data as GetWorkQuery;
  return result.work as Work;
}

export async function createWork(input: CreateWorkInput): Promise<Work> {
  const response = await fetchWithAuth(`${API_URL}/graphql`, {
    method: "POST",
    body: JSON.stringify({
      query: CREATE_WORK.loc?.source.body,
      variables: { input },
    }),
  });

  const data = await response.json();

  if (data.errors) {
    throw new Error(data.errors[0].message);
  }

  const result = data.data as CreateWorkMutation;
  return result.createWork.work as Work;
}

export async function updateWork(id: string, input: UpdateWorkInput): Promise<Work> {
  const response = await fetchWithAuth(`${API_URL}/graphql`, {
    method: "POST",
    body: JSON.stringify({
      query: UPDATE_WORK.loc?.source.body,
      variables: { id, input },
    }),
  });

  const data = await response.json();

  if (data.errors) {
    throw new Error(data.errors[0].message);
  }

  const result = data.data as UpdateWorkMutation;
  return result.updateWork.work as Work;
}

export async function deleteWork(id: string): Promise<boolean> {
  const response = await fetchWithAuth(`${API_URL}/graphql`, {
    method: "POST",
    body: JSON.stringify({
      query: DELETE_WORK.loc?.source.body,
      variables: { id },
    }),
  });

  const data = await response.json();

  if (data.errors) {
    throw new Error(data.errors[0].message);
  }

  const result = data.data as DeleteWorkMutation;
  return result.deleteWork.success;
}

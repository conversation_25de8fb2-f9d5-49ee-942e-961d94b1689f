import { z } from "zod";

export const workSchema = z.object({
  authorId: z.string().min(1, "Автор сайлагыз"),
  title: z.string().min(1, "Исем кирәк"),
  content: z.string().min(1, "Эчтәлек кирәк"),
  genres: z.array(z.string()).optional(),
  comments: z.string().optional(),
  source: z.string().optional(),
  publishedDate: z.string().optional(),
});

export type WorkFormValues = z.infer<typeof workSchema>;

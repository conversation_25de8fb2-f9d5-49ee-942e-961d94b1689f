# Шигърият.ру Админ

Админская часть для сайта Шигърият.ру, позволяющая управлять поэтами и произведениями.

## Технические детали решения

Админская панель реализована как отдельное Next.js приложение, которое взаимодействует с серверным GraphQL API. Приложение использует современные практики разработки React-приложений, включая:

- **App Router** - использование нового маршрутизатора Next.js с поддержкой вложенных маршрутов и группировки маршрутов
- **Server Components** - использование серверных компонентов для улучшения производительности и SEO
- **TypeScript** - строгая типизация для более надежного кода
- **Tailwind CSS** - утилитарный CSS-фреймворк для быстрого создания стилей

### Основные технологии и библиотеки

- **Next.js 14** - фреймворк для React с поддержкой SSR, SSG и ISR
- **NextAuth.js** - библиотека для аутентификации в Next.js приложениях
- **React Hook Form** - библиотека для управления формами с минимальными перерисовками
- **Zod** - библиотека для валидации данных с поддержкой TypeScript
- **Lexical** - редактор контента от Facebook с поддержкой форматирования текста
- **shadcn/ui** - набор переиспользуемых компонентов UI на основе Radix UI и Tailwind CSS
- **TanStack Query (React Query)** - библиотека для управления состоянием и асинхронными запросами
- **next-themes** - библиотека для управления темами в Next.js
- **sonner** - минималистичная библиотека для уведомлений (toast)

### Взаимодействие с API

Приложение взаимодействует с серверным GraphQL API через следующие механизмы:

- **GraphQL запросы** - для получения данных об авторах и произведениях
- **GraphQL мутации** - для создания, обновления и удаления данных
- **JWT аутентификация** - для защиты API запросов

### Архитектура приложения

Приложение использует следующие архитектурные паттерны:

- **Модульная архитектура** - разделение кода на модули по функциональности
- **Разделение ответственности** - компоненты отвечают только за свою функциональность
- **Композиция** - создание сложных компонентов из простых
- **Хуки** - использование React хуков для повторного использования логики

## Установка и запуск

1. Установите зависимости:

```bash
npm install
```

2. Создайте файл `.env.local` со следующим содержимым:

```
NEXT_PUBLIC_API_URL=http://localhost:3556
```

3. Запустите сервер разработки:

```bash
npm run dev
```

4. Откройте [http://localhost:3000](http://localhost:3000) в браузере.

## Структура проекта

- `/src/app` - страницы приложения
  - `/(auth)` - страницы аутентификации
  - `/(admin)` - защищенные страницы админки
- `/src/components` - компоненты React
  - `/ui` - базовые UI компоненты
  - `/auth` - компоненты аутентификации
  - `/authors` - компоненты для управления авторами
  - `/works` - компоненты для управления произведениями
  - `/editor` - компоненты редактора контента
  - `/layout` - компоненты макета
- `/src/lib` - утилиты и хуки
  - `/api` - функции для работы с API
  - `/auth` - настройки аутентификации
  - `/hooks` - React хуки
  - `/schemas` - схемы валидации
  - `/utils` - вспомогательные функции

## Аутентификация и безопасность

В приложении реализована система аутентификации на основе NextAuth.js с использованием JWT-токенов. Основные компоненты системы безопасности:

- **JWT токены** - для защищенной аутентификации
- **Middleware** - для защиты маршрутов админской панели
- **Ролевая система** - для разграничения прав доступа

Для входа в админскую часть используйте следующие учетные данные:

- Логин: `admin`
- Пароль: `admin123`

После первого входа рекомендуется сменить пароль.

## Разработка и расширение

Приложение разработано с учетом возможности дальнейшего расширения. Для добавления новых функций или изменения существующих, следуйте структуре проекта и используйте существующие компоненты и хуки.

Основные направления для расширения:

- Добавление новых типов контента (например, жанры, теги)
- Расширение функциональности редактора контента
- Добавление статистики и аналитики
- Расширение системы управления пользователями

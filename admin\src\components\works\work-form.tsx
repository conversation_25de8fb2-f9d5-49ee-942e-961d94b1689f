"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { BlockNoteEditorField } from "@/components/editor/blocknote-editor";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useCreateWork, useUpdateWork } from "@/lib/hooks/use-works";
import { workSchema } from "@/lib/schemas/work-schema";
import { Work } from "@/lib/types";
import { useState } from "react";
import { toast } from "sonner";
import { formatDateForInput } from "@/lib/utils/format-date";
import { useAuthors } from "@/lib/hooks/use-authors";
import { AuthorSelect } from "@/components/authors/author-select";
import { prepareContentForSave, prepareContentForEditor } from "@/lib/utils/content-format";
import { PartialBlock } from "@blocknote/core";
import { BlockNoteEditor } from '@blocknote/core';
import { useEffect } from 'react';
import { SafeBlockNoteEditorField } from '@/components/editor/blocknote-editor';

interface WorkFormProps {
  work?: Work;
  authorId?: string;
}

export function WorkForm({ work, authorId }: WorkFormProps) {
  const router = useRouter();
  const createWork = useCreateWork();
  const updateWork = useUpdateWork();
  const { isLoading: isLoadingAuthors } = useAuthors();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<typeof workSchema._type>({
    resolver: zodResolver(workSchema),
    defaultValues: work
      ? {
        authorId: work.authorId,
        title: work.title,
        content: work.content || "",
        genres: work.genres || [],
        comments: work.comments || "",
        source: work.source || "",
        publishedDate: work.publishedDate ? formatDateForInput(work.publishedDate) : "",
      }
      : {
        authorId: authorId || "",
        title: "",
        content: "",
        genres: [],
        comments: "",
        source: "",
        publishedDate: "",
      },
  });

  async function onSubmit(values: typeof workSchema._type) {
    setIsSubmitting(true);
    try {
      console.log("onSubmit", values.content);

      // Преобразуем строковые даты в объекты Date или null
      // Сохраняем контент как JSON для BlockNote
      const formattedValues = {
        ...values,
        content: values.content, // Сохраняем JSON напрямую
        publishedDate: values.publishedDate ? new Date(values.publishedDate).toISOString() : null,
      };

      if (work) {
        await updateWork.mutateAsync({
          id: work.id,
          data: formattedValues,
        });
        toast.success("Әсәр мәгълүматлары уңышлы үзгәртелде");
      } else {
        await createWork.mutateAsync(formattedValues);
        toast.success("Яңа әсәр уңышлы өстәлде");
      }
      router.push("/works");
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("Хата: " + (error instanceof Error ? error.message : "Билгесез хата"));
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="authorId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Автор</FormLabel>
                  <FormControl>
                    <AuthorSelect
                      value={field.value}
                      onChange={field.onChange}
                      disabled={isLoadingAuthors}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Исем</FormLabel>
                  <FormControl>
                    <Input placeholder="Әсәр исеме" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="content"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Эчтәлек</FormLabel>
                <FormControl>
                  <div className="min-h-[400px] w-full">
                    <SafeBlockNoteEditorField
                      value={field.value}
                      contentFormat={work?.contentFormat}
                      onChange={(blocks: PartialBlock[]) => {
                        const jsonContent = JSON.stringify(blocks);
                        field.onChange(jsonContent);
                      }}
                      placeholder="Шигыйрь эчтәлеге..."
                      className="border rounded-md"
                    />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="source"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Чыганак</FormLabel>
                  <FormControl>
                    <Input placeholder="Китап исеме, журнал һ.б." {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="publishedDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Басылган көне</FormLabel>
                  <FormControl>
                    <Input type="date" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="comments"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Искәрмәләр</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Әсәр турында өстәмә мәгълүмат"
                    className="min-h-[100px]"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex justify-end gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push("/works")}
            >
              Кире кайту
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting
                ? "Саклана..."
                : work
                  ? "Үзгәртүләрне саклау"
                  : "Әсәрне өстәү"}
            </Button>
          </div>
        </form>
      </Form>
  );
}

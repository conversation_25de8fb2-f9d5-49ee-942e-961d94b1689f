"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { useState, useEffect } from "react";

const navItems = [
  { href: "/dashboard", label: "Башбит" },
  { href: "/authors", label: "Шагыйрьләр" },
  { href: "/works", label: "Әсәрләр" },
];

export function AdminSidebar() {
  const pathname = usePathname();
  const [isMounted, setIsMounted] = useState(false);

  // Устанавливаем isMounted в true после первого рендера
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Если компонент не смонтирован, возвращаем пустой боковой панель
  if (!isMounted) {
    return <aside className="w-64 bg-slate-800 text-white"></aside>;
  }

  return (
    <aside className="w-64 bg-slate-800 text-white">
      <div className="p-6">
        <h2 className="text-xl font-bold">Шигърият.ру</h2>
      </div>
      <nav className="mt-6">
        <ul className="space-y-1">
          {navItems.map((item) => (
            <li key={item.href}>
              <Link
                href={item.href}
                className={cn(
                  "flex items-center px-6 py-3 text-sm font-medium",
                  pathname === item.href || pathname.startsWith(`${item.href}/`)
                    ? "bg-slate-700 text-white"
                    : "text-slate-300 hover:bg-slate-700 hover:text-white"
                )}
              >
                {item.label}
              </Link>
            </li>
          ))}
        </ul>
      </nav>
    </aside>
  );
}
